<template>
    <div>
        <el-tabs type="border-card">
            <el-tab-pane>
                <span slot="label"><i class="el-icon-search"></i> 查询条件</span>
                <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
                    <el-form-item label="下料时间">
                        <el-date-picker v-model="dateRange" type="datetimerange" range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    </el-form-item>
                </el-form>
            </el-tab-pane>

            <el-tab-pane label="消息中心"> <span slot="label"><i class="el-icon-s-tools"></i> 刷新设置</span>

                <el-form :model="configParams" ref="configParamsForm" size="small" :inline="true" label-width="68px">
                    <el-form-item label="刷新开关" prop="isRefresh">
                        <el-switch v-model="configParams.isRefresh" active-color="#13ce66" inactive-color="#ff4949">
                        </el-switch>
                    </el-form-item>
                    <el-form-item label="刷新频率" prop="timePar">
                        <el-time-picker v-model="configParams.timePar" :clearable="false"
                            :picker-options="{ selectableRange: '00:00:01 - 23:59:59' }"></el-time-picker>
                    </el-form-item>
                    <el-form-item label="近期时长" prop="resultTime">
                        <el-time-picker v-model="configParams.resultTime" :clearable="false"
                            :picker-options="{ selectableRange: '00:00:01 - 23:59:59' }"></el-time-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="refreshPlay">应用</el-button>
                    </el-form-item>
                </el-form>

            </el-tab-pane>

        </el-tabs>

        <vxe-table :data="tableData" :border="true" align="center">
            <vxe-column field="时间" title="时间"></vxe-column>
            <vxe-column field="料批" title="料批"></vxe-column>
            <vxe-column field="焦重" title="焦重"></vxe-column>
            <vxe-column field="矿重" title="矿重"></vxe-column>

            <vxe-column field="杂1仓" title="杂1仓"></vxe-column>
            <vxe-column field="杂2仓" title="杂2仓"></vxe-column>
            <!-- <vxe-column field="杂3仓" title="杂3仓"></vxe-column>
            <vxe-column field="杂4仓" title="杂4仓"></vxe-column> -->

            <vxe-column field="块1仓" title="块1仓"></vxe-column>
            <vxe-column field="块2仓" title="块2仓"></vxe-column>
            <vxe-column field="块3仓" title="块3仓"></vxe-column>

            <!-- <vxe-column field="球1仓" title="球1仓"></vxe-column>
            <vxe-column field="球2仓" title="球2仓"></vxe-column>
            <vxe-column field="球3仓" title="球3仓"></vxe-column> -->

            <vxe-column field="烧1仓" title="烧1仓"></vxe-column>
            <vxe-column field="烧2仓" title="烧2仓"></vxe-column>
            <vxe-column field="烧3仓" title="烧3仓"></vxe-column>
            <vxe-column field="烧4仓" title="烧4仓"></vxe-column>
            <vxe-column field="烧5仓" title="烧5仓"></vxe-column>
            <vxe-column field="烧6仓" title="烧6仓"></vxe-column>
            <!-- <vxe-column field="烧7仓" title="烧7仓"></vxe-column> -->

            <vxe-column field="焦丁仓" title="焦丁仓"></vxe-column>
        </vxe-table>
    </div>
</template>

<script>
import { queryByTime } from "@/api/bin/consume";
import dayjs from "dayjs";

export default {
    name: "ConsumeReport",

    data() {
        return {
            tableData: [],
            // 遮罩层
            loading: false,
            // 日期范围
            dateRange: [],
            // 查询参数
            queryParams: {
                startTime: null,
                endTime: null,
                prodCenterCode: null,
            },
            //定时器
            intervalId: null,

            configParams: {
                isRefresh: false,
                timePar: '2025-03-01 00:01:00', // 日期无效只使用时间
                resultTime: '2025-03-01 00:30:00',
            },
        }
    },

    created() {
        this.dateRange.push(dayjs(new Date()).add(-60, "minute"));
        this.dateRange.push(dayjs(new Date()));

        const storeKey = this.getProdCenterCode() + "refreshConfig";
        const storeValue = localStorage.getItem(storeKey);
        if (storeValue != undefined) {
            this.configParams = JSON.parse(storeValue);
            if (this.configParams.isRefresh == true) {
                this.dataRefreh();
            }
        }

        this.getList(null);
    },

    methods: {
        queyrByTimeList() {
            this.queryParams.prodCenterCode = this.getProdCenterCode();
            if (this.dateRange.length == 2) {
                this.queryParams.startTime = dayjs(this.dateRange[0]).format(
                    "YYYY-MM-DD HH:mm:ss"
                );
                this.queryParams.endTime = dayjs(this.dateRange[1]).format(
                    "YYYY-MM-DD HH:mm:ss"
                );
            }

            queryByTime(this.queryParams).then(rep => {
                // console.log("rep:", JSON.stringify(rep));
                this.tableData = rep.data
                this.loading = false;
            })
        },

        /** 查询变料-消耗记录列表 */
        getList(selectVO) {
            this.loading = true;
            if (selectVO) {
                this.selectVO = selectVO;
            }
            this.queyrByTimeList();
        },

        /** 搜索按钮操作 */
        handleQuery() {
            this.getList();
        },

        getProdCenterCode() {
            return this.$route.query.prodCenterCode;
        },

        // 定时刷新数据函数
        dataRefreh() {
            // 计时器正在进行中，退出函数
            if (this.intervalId != null) {
                this.clear();
            }
            const interinalDay = dayjs(this.configParams.timePar);
            const interinaMs = this.convertToMilliseconds(interinalDay.hour(), interinalDay.minute(), interinalDay.second());

            // 计时器为空，操作
            this.intervalId = setInterval(() => {
                let dr = [];

                const dateRDay = dayjs(this.configParams.resultTime);
                const dateRMS = this.convertToMilliseconds(dateRDay.hour(), dateRDay.minute(), dateRDay.second());

                dr.push(dayjs(new Date()).add(-1 * dateRMS, "millisecond"));
                dr.push(dayjs(new Date()));

                this.$set(this, 'dateRange', dr);

                this.getList(null);
            }, interinaMs);
        },
        convertToMilliseconds(hours, minutes, seconds) {
            return hours * 3600000 + minutes * 60000 + seconds * 1000;
        },

        // 停止定时器
        clear() {
            if (this.intervalId != null) {
                clearInterval(this.intervalId); //清除计时器
                this.intervalId = null; //设置为null
            }

        },

        refreshPlay() {
            const key = this.getProdCenterCode() + "refreshConfig";
            localStorage.setItem(key, JSON.stringify(this.configParams));

            this.$message({
                message: '配置应用成功',
                type: 'success'
            });
            if (this.configParams.isRefresh == false) {
                this.clear();
            } else {
                this.dataRefreh();
            }

        },


    }


}
</script>
