<template>
  <div class="bin-chart-container">
    <div class="bin-header">
      <h3>料仓装货状态监控</h3>
      <span class="bin-count">共 {{ siloData.length }} 个料仓</span>
    </div>
    
    <div class="bin-grid">
      <div 
        v-for="silo in siloData" 
        :key="silo.binId" 
        class="bin-item">
        
        <!-- 料仓名称 -->
        <div class="bin-name">{{ silo.binName }}</div>
        
        <!-- 料仓可视化容器 -->
        <div class="bin-visual">
          <!-- 料仓外壳 -->
          <div class="bin-shell">
            <!-- 料仓顶部 -->
            <div class="bin-top"></div>
            
            <!-- 料仓主体 -->
            <div class="bin-body">
              <!-- 物料填充 -->
              <div 
                class="bin-material" 
                :style="{
                  height:  calculatePercentage(silo),
                  background: getMaterialColor(silo.binType)
                }">
              </div>
              
              <!-- 料位刻度线 -->
              <div class="bin-scale">
                <div v-for="i in 5" :key="i" class="scale-line" :style="{ bottom: (i-1) * 20 + '%' }">
                  <span class="scale-text">{{ (i-1) * 20 }}%</span>
                </div>
              </div>
            </div>
            
            <!-- 料仓底部出料口 -->
            <div class="bin-bottom"></div>
          </div>
        </div>
        
        <!-- 料仓信息 -->
        <div class="bin-info">
          <div class="info-item">
            <span class="info-label">类型:</span>
            <span class="info-value">{{ silo.binType }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">重量:</span>
            <span class="info-value">{{ silo.materialWeight ? silo.materialWeight.toFixed(1) + 't' : '0.0t' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">料位:</span>
            <span class="info-value" :style="{ color: getPercentageColor(silo) }">
              {{ calculatePercentage(silo)}}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BinChart',
  props: {
    siloData: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    /* 料位百分比 */
    calculatePercentage(silo) {
      if (silo.materialPercentage !== null && silo.materialPercentage !== undefined) {
        return silo.materialPercentage
      }
      return 0
    },

    /* 获取料仓百分比 */
    getPercentage(silo) {
      const percentage = this.calculatePercentage(silo)
      if (percentage === 0) return 0
      return Math.round(percentage)
    },

    /* 根据物料类型获取颜色 */
    getMaterialColor(binType) {
      const colorMap = {
        '焦槽': 'linear-gradient(180deg, #2c3e50 0%, #34495e 100%)', // 深灰色（焦炭）
        '矿槽': 'linear-gradient(180deg, #8b4513 0%, #a0522d 100%)', // 棕色（矿石）
        'J10启动': 'linear-gradient(180deg, #e74c3c 0%, #c0392b 100%)', // 红色
        'J10运行': 'linear-gradient(180deg, #27ae60 0%, #229954 100%)', // 绿色
        'J7启动': 'linear-gradient(180deg, #f39c12 0%, #e67e22 100%)', // 橙色
        'J7运行': 'linear-gradient(180deg, #3498db 0%, #2980b9 100%)', // 蓝色
        '返焦料位': 'linear-gradient(180deg, #9b59b6 0%, #8e44ad 100%)', // 紫色
        '返矿料位': 'linear-gradient(180deg, #1abc9c 0%, #16a085 100%)', // 青色
      }
      return colorMap[binType] || 'linear-gradient(180deg, #95a5a6 0%, #7f8c8d 100%)' // 默认灰色
    },

    /* 根据料位百分比获取文字颜色 */
    getPercentageColor(silo) {
      const percentage = this.getPercentage(silo)
      if (percentage >= 80) return '#e74c3c' // 红色：料位过高
      if (percentage >= 60) return '#f39c12' // 橙色：料位较高
      if (percentage >= 30) return '#27ae60' // 绿色：料位正常
      return '#95a5a6' // 灰色：料位较低
    }
  }
}
</script>

<style scoped>

.bin-chart-container {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}


.bin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #dee2e6;
}

.bin-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.bin-count {
  color: #6c757d;
  font-size: 14px;
  background: #e9ecef;
  padding: 4px 12px;
  border-radius: 12px;
}


.bin-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 20px;
  max-height: 600px;
  overflow-y: auto;
}


.bin-item {
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  border: 1px solid #e9ecef;
}

.bin-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}


.bin-name {
  text-align: center;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 14px;
}


.bin-visual {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}


.bin-shell {
  position: relative;
  width: 60px;
  height: 120px;
}


.bin-top {
  width: 70px;
  height: 8px;
  background: linear-gradient(135deg, #bdc3c7, #95a5a6);
  border-radius: 4px 4px 0 0;
  margin-left: -5px;
  border: 1px solid #7f8c8d;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}


.bin-body {
  position: relative;
  width: 60px;
  height: 100px;
  background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
  border: 2px solid #95a5a6;
  border-top: none;
  border-bottom: none;
  overflow: hidden;
}


.bin-material {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  transition: height 0.8s ease-in-out;
  border-radius: 0 0 2px 2px;
  opacity: 0.9;
}


.bin-scale {
  position: absolute;
  right: -25px;
  top: 0;
  height: 100%;
  width: 20px;
}

.scale-line {
  position: absolute;
  right: 0;
  width: 8px;
  height: 1px;
  background: #7f8c8d;
}

.scale-text {
  position: absolute;
  right: 10px;
  top: -6px;
  font-size: 10px;
  color: #6c757d;
  white-space: nowrap;
}


.bin-bottom {
  width: 40px;
  height: 12px;
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
  margin: 0 auto;
  border-radius: 0 0 6px 6px;
  border: 1px solid #7f8c8d;
  border-top: none;
  position: relative;
}

.bin-bottom::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 8px;
  height: 4px;
  background: #7f8c8d;
  border-radius: 0 0 2px 2px;
}


.bin-info {
  font-size: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  padding: 2px 0;
}

.info-label {
  color: #6c757d;
  font-weight: 500;
}

.info-value {
  color: #2c3e50;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bin-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
  }
  
  .bin-item {
    padding: 12px;
  }
  
  .bin-shell {
    width: 50px;
    height: 100px;
  }
  
  .bin-body {
    width: 50px;
    height: 80px;
  }
  
  .bin-top {
    width: 60px;
    margin-left: -5px;
  }
}
</style>
