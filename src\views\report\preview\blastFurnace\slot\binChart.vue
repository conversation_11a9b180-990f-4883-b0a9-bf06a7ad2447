<template>
  <div class="bin-chart-container">
    <div class="bin-header">
      <h3>料仓装货状态</h3>
      <span class="bin-count">共 {{ siloData.length }} 个料仓</span>
    </div>

    <div class="bin-grid">
      <div
        v-for="(silo, index) in siloData"
        :key="`silo-${silo.binId}-${index}`"
        class="bin-item">

        <!-- 物料名称（主标题） -->
        <div class="material-name">{{ silo.materialName || silo.binType || '-' }}</div>

        <!-- 料仓可视化区域 -->
        <div class="bin-visual">
          <!-- 料仓名称（竖向显示在左侧） -->
          <div class="bin-name-vertical">
            {{ silo.binName }}
          </div>

          <!-- 料仓外壳 -->
          <div class="bin-shell">
            <!-- 料仓顶部 -->
            <div class="bin-top"></div>

            <!-- 料仓主体 -->
            <div class="bin-body">
              <!-- 物料填充 -->
              <div
                class="bin-material"
                :style="{
                  height:  calculatePercentage(silo),
                  background: getMaterialColor(silo.binType)
                }">
              </div>

              <!-- 料位刻度线 -->
              <div class="bin-scale">
                <div v-for="i in 5" :key="`scale-${silo.binId}-${i}`" class="scale-line" :style="{ bottom: (i-1) * 20 + '%' }">
                  <span class="scale-text">{{ (i-1) * 20 }}%</span>
                </div>
              </div>
            </div>

            <!-- 料仓底部及出料口 -->
            <div class="bin-bottom"></div>
          </div>
        </div>

        <!-- 料仓信息 -->
        <div class="bin-info">
          <div class="info-item">
            <span class="info-label">类型:</span>
            <span class="info-value">{{ silo.binType }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">重量:</span>
            <span class="info-value">{{ silo.materialWeight ? silo.materialWeight.toFixed(1) + 'kg' : '0.0kg' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">料位:</span>
            <span class="info-value" :style="{ color: getPercentageColor(silo) }">
              {{ calculatePercentage(silo)}}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BinChart',
  props: {
    siloData: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    /* 计算料位百分比 */
    calculatePercentage(silo) {
      if (silo.materialPercentage !== null && silo.materialPercentage !== undefined) {
        return silo.materialPercentage
      }
      // 如果没有百分比数据，根据重量和容量计算
      if (silo.materialWeight && silo.binSolvent) {
        const percentage = (silo.materialWeight / silo.binSolvent) * 100
        return Math.min(percentage, 100)
      }
      return 0
    },



    /* 根据类型获取颜色 */
    getMaterialColor(binType) {
      const colorMap = {
        '焦槽': 'linear-gradient(180deg, #2c3e50 0%, #34495e 100%)',
        '矿槽': 'linear-gradient(180deg, #8b4513 0%, #a0522d 100%)',
        'J10启动': 'linear-gradient(180deg, #e74c3c 0%, #c0392b 100%)',
        'J10运行': 'linear-gradient(180deg, #27ae60 0%, #229954 100%)',
        'J7启动': 'linear-gradient(180deg, #f39c12 0%, #e67e22 100%)',
        'J7运行': 'linear-gradient(180deg, #3498db 0%, #2980b9 100%)',
        '返焦料位': 'linear-gradient(180deg, #9b59b6 0%, #8e44ad 100%)',
        '返矿料位': 'linear-gradient(180deg, #1abc9c 0%, #16a085 100%)',
      }
      return colorMap[binType] || 'linear-gradient(180deg, #95a5a6 0%, #7f8c8d 100%)' // 灰色
    },

    /* 获取料仓百分比（用于显示） */
    getPercentage(silo) {
      const percentage = this.calculatePercentage(silo)
      return Math.round(percentage)
    },

    /* 根据料位设置不同文字颜色 */
    getPercentageColor(silo) {
      const percentage = this.getPercentage(silo)

      if (percentage >= 80) {
        return '#e74c3c' // 红色：料位过高
      }
      if (percentage >= 60) {
        return '#f39c12' // 橙色：料位较高
      }
      if (percentage >= 30) {
        return '#27ae60' // 绿色：料位正常
      }
      return '#95a5a6' // 灰色：料位较低
    }
  }
}
</script>

<style scoped>

.bin-chart-container {
  margin-top: 5px;
  //padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}


.bin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #dee2e6;
}

.bin-header h3 {
  margin: 0;
  padding-top:10px;
  padding-left:10px;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.bin-count {
  color: #6c757d;
  font-size: 14px;
  background: #e9ecef;
  padding: 4px 12px;
  border-radius: 12px;
}


.bin-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  grid-template-rows: auto auto;
  gap: 15px 20px;
  //max-height: 400px;
  overflow-y: auto;
  padding: 0 10px;
}


.bin-item {
  background: white;
  border-radius: 6px;
  padding: 10px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  border: 1px solid #e9ecef;
  min-height: 160px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.bin-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}


.material-name {
  text-align: center;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 14px;
}


.bin-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
  position: relative;
}


.bin-name-vertical {
  position: absolute;
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #6c757d;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 2px;
  border-radius: 3px;
  border: 1px solid #e9ecef;
  writing-mode: vertical-rl;
  text-orientation: upright;
  letter-spacing: 2px;
  line-height: 1;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}


.bin-shell {
  position: relative;
  width: 50px;
  height: 100px;
}


.bin-top {
  width: 60px;
  height: 6px;
  background: linear-gradient(135deg, #bdc3c7, #95a5a6);
  border-radius: 4px 4px 0 0;
  margin-left: -5px;
  border: 1px solid #7f8c8d;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}


.bin-body {
  position: relative;
  width: 50px;
  height: 80px;
  background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
  border: 2px solid #95a5a6;
  border-top: none;
  border-bottom: none;
  overflow: hidden;
}


.bin-material {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  transition: height 0.8s ease-in-out;
  border-radius: 0 0 2px 2px;
  opacity: 0.9;
}


.bin-scale {
  position: absolute;
  /*right: -25px;*/
  right:16px;
  top: 0;
  height: 100%;
  width: 20px;
}

.scale-line {
  position: absolute;
  right: 0;
  width: 8px;
  height: 1px;
  background: #7f8c8d;
}

.scale-text {
  position: absolute;
  right: 10px;
  top: -6px;
  font-size: 10px;
  color: #6c757d;
  white-space: nowrap;
}


.bin-bottom {
  width: 35px;
  height: 10px;
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
  margin: 0 auto;
  border-radius: 0 0 5px 5px;
  border: 1px solid #7f8c8d;
  border-top: none;
  position: relative;
}

.bin-bottom::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 8px;
  height: 4px;
  background: #7f8c8d;
  border-radius: 0 0 2px 2px;
}


.bin-info {
  font-size: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  padding: 2px 0;
}

.info-label {
  color: #6c757d;
  font-weight: 500;
}

.info-value {
  color: #2c3e50;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .bin-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px 15px;
  }

  .bin-item {
    min-height: 140px;
    padding: 8px;
  }
}

@media (max-width: 768px) {
  .bin-grid {
    grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
    gap: 10px 12px;
    max-height: 350px;
  }

  .bin-item {
    padding: 8px;
    min-height: 130px;
  }

  .bin-shell {
    width: 40px;
    height: 80px;
  }

  .bin-body {
    width: 40px;
    height: 65px;
  }

  .bin-top {
    width: 50px;
    height: 5px;
    margin-left: -5px;
  }

  .bin-bottom {
    width: 30px;
    height: 8px;
  }

  .material-name {
    font-size: 12px;
    margin-bottom: 8px;
  }

  .bin-name-vertical {
    font-size: 9px;
    left: -18px;
    padding: 3px 1px;
    min-height: 50px;
    letter-spacing: 1px;
  }

  .bin-info {
    font-size: 10px;
  }
}
</style>
