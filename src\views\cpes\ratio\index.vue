<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="auto" size="small">
      <el-form-item label="开始时间">
        <el-date-picker v-model="dateRange" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期"
                        type="datetimerange"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="时间班组" prop="workClsass">
        <BWorkShiftSelect v-model="queryParams.workClsass" model-name="烧结排班"/>
      </el-form-item>
      <el-form-item label="管理班组" prop="workGroup">
        <BWorkClassSelect v-model="queryParams.workGroup" model-name="烧结排班"/>
      </el-form-item>

      <el-form-item>
        <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="el-icon-edit"
          plain
          size="mini"
          type="success"
          @click="handleUpdate"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="el-icon-edit"
          plain
          size="mini"
          type="success"
          @click="handleUpdateReason"
        >修改变更原因
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="el-icon-delete"
          plain
          size="mini"
          type="danger"
          @click="handleDelete"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="el-icon-download"
          plain
          size="mini"
          type="warning"
          @click="handleExport"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="queryList"></right-toolbar>
    </el-row>

    <div class="tableInfo">
      <vxe-grid
        ref="tableMainRef"
        :column-config="{resizable: true}"
        :row-style="tableRowStyle"
        @radio-change="handleRadioChange"
        v-bind="mainTable.gridOptions"
        :span-method="mergeRowsMethod"
      >
      </vxe-grid>

    </div>

    <!-- 添加或修改烧结物料消耗对话框 -->
    <el-dialog :title="editDialog.title" :visible.sync="editDialog.open" append-to-body width="600">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="记账日期" prop="workDate">
              <el-date-picker v-model="form.workDate"
                              clearable
                              placeholder="请选择记账日期"
                              type="date"
                              value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="管理班组" prop="workGroup">
              <BWorkClassSelect v-model="form.workGroup" model-name="烧结排班"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="时间班组" prop="workClsass">
              <BWorkShiftSelect v-model="form.workClsass" model-name="烧结排班"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="开始时间" prop="beginTime">
              <el-date-picker
                v-model="form.beginTime"
                placeholder="选择日期时间"
                type="datetime"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                placeholder="选择日期时间"
                type="datetime"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="变更原因" prop="endTime">
          <el-input
            v-model="form.changeReason"
            placeholder="清输入变更原因"
            type="textarea"
          >
          </el-input>
        </el-form-item>

        <el-tabs type="border-card">
          <el-tab-pane label="物料消耗清单">

            <el-row>
              <el-col :span="24">
                <el-button-group style="float: right; margin-right: 10px;">
                  <el-button icon="el-icon-plus" size="small" type="success" @click="editAddHandler">新增</el-button>
                  <el-button icon="el-icon-delete" size="small" type="danger" @click="editDeleteHandler">删除
                  </el-button>
                </el-button-group>
              </el-col>
            </el-row>

            <el-row style="margin-top: 10px">
              <el-col :span="24">
                <vxe-table
                  ref="editTableef"
                  :column-config="{resizable: true}"
                  :data="form.detailList"
                  :height="450"
                  :row-config="{isHover: true}"
                  border
                  header-align="center"
                  stripe
                >
                  <vxe-column align="center" type="checkbox" width="10%"></vxe-column>
                  <vxe-column field="mateCode" title="物料名称" width="40%">
                    <template #default="{ row }">
                      <BMateSelect v-model="row.mateCode"/>
                    </template>
                  </vxe-column>
                  <vxe-column field="ratio" title="配比" width="25%">
                    <template #default="{ row }">
                      <el-input-number v-model="row.ratio" :max="100" :min="0" :precision="6"
                                       style="margin-right: 5px ; margin-left: 5px;"
                      ></el-input-number>
                    </template>
                  </vxe-column>
                  <vxe-column field="inputWeight" title="重量" width="25%">
                    <template #default="{ row }">
                      <el-input-number v-model="row.qty" :precision="6" style="margin-right: 5px ; margin-left: 5px;"
                      ></el-input-number>
                    </template>
                  </vxe-column>
                </vxe-table>
              </el-col>
            </el-row>


          </el-tab-pane>

        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="editDialog.open=false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="editReasonDialog.open" append-to-body title="修改变更原因" width="600">
      <el-form ref="formReason" :model="formReason" :rules="rulesReasone" label-width="80px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="记账日期" prop="workDate">
              {{ formReason.workDate }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="管理班组" prop="workGroup">
              {{ formReason.workGroup }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="时间班组" prop="workClsass">
              {{ formReason.workClsass }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="开始时间" prop="beginTime">
              <el-date-picker
                v-model="formReason.beginTime"
                placeholder="选择日期时间"
                readonly
                type="datetime"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="formReason.endTime"
                placeholder="选择日期时间"
                readonly
                type="datetime"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>


        <el-tabs type="border-card">
          <el-tab-pane label="变更原因">
            <el-input
              v-model="formReason.changeReason"
              placeholder="清输入变更原因"
              type="textarea"
            >
            </el-input>
          </el-tab-pane>
        </el-tabs>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormReason">确 定</el-button>
        <el-button @click="editReasonDialog.open=false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {
  queryforManger,
  getEdit,
  updateReason, delRatio, queryLastEndTime, saveOrUpdate
} from '@/api/cpes/ratio'
import dayjs from 'dayjs'
import XEUtils from 'xe-utils'
import BWorkClassSelect from '@/components/BWorkClassSelect/index.vue'
import BWorkShiftSelect from '@/components/BWorkShiftSelect/index.vue'
import BMateSelect from '@/components/BMateSelect/index.vue'

export default {
  name: 'RatioManger',
  components: { BMateSelect, BWorkShiftSelect, BWorkClassSelect },
  data() {
    return {
      tableHeight: 300,
      dateRange: [],
      mainTable: {
        selectedRadioRow: null,
        loading: true,
        single: true,
        multiple: true,
        selectId: [],
        gridOptions: {
          border: true,
          stripe: false,
          loading: false,
          height: 300,
          columnConfig: {
            resizable: true
          },
          cellStyle: this.tableCellStyle,
          rowConfig: {
            isHover: true
          },
          radioConfig: {
            labelField: 'name',
            trigger: 'row'
          },
          columns: [
            { type: 'checkbox', width: 60 }
          ],
          data: [],
          ratioMasterIds: [],
          spanMethod({ row, rowIndex, column, visibleData }) {
            if (column.type === 'checkbox') {
              return
            }
            if (row.CONTENT_TYPE === '配比') {
              if (column.title === '变更原因') {
                return { rowspan: 2, colspan: 0 }
              }
            }

          }
        }
      },
      showSearch: true,
      editDialog: {
        title: '',
        // 是否显示弹出层
        open: false
      },
      editReasonDialog: {
        // 是否显示弹出层
        open: false
      },
      // 查询参数
      queryParams: {
        dtStart: null,
        dtEnd: null,
        workDate: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null
      },
      // 表单参数
      form: {
        workDate: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null,
        formTableData: []
      },
      formReason: {
        mateRatioMasterId: null,
        workDate: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null,
        beginTime: null,
        endTime: null
      },
      rulesReasone: {
        changeReason: [
          {
            required: true, message: '变更原因不能', trigger: 'blur'
          }
        ]

      },
      // 表单校验
      rules: {
        workDate: [
          {
            required: true, message: '记账日期不能为空', trigger: 'blur'
          }
        ],
        workClsass: [
          {
            required: true, message: '时间班组不能为空', trigger: 'blur'
          }
        ],
        workGroup: [
          {
            required: true, message: '管理班组不能为空', trigger: 'blur'
          }
        ],
        beginTime: [
          {
            required: true, message: '开始时间buneng ', trigger: 'blur'
          }
        ]
      }
    }
  },
  created() {
    this.dateRange.push(dayjs(new Date()).startOf('date'))
    this.dateRange.push(dayjs(new Date()).endOf('date'))
    this.queryList()
  },
  methods: {
    formatDate(date) {
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    queryList() {
      if (this.dateRange.length == 2) {
        this.queryParams.dtStart = dayjs(this.dateRange[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.queryParams.dtEnd = dayjs(this.dateRange[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      }
      this.queryParams.prodCenterCode = this.getProdCenterCode()
      this.mainTable.gridOptions.columns = []
      this.mainTable.gridOptions.data = []
      this.mainTable.gridOptions.ratioMasterIds = []
      this.mainTable.gridOptions.selectedRadioRow = null
      queryforManger(this.queryParams).then(response => {
        this.mainTable.gridOptions.columns.push({ type: 'radio', field: 'radio', width: 'auto', fixed: 'left' })

        response.data.col.forEach((item) => {
          if (item.value == '记账日期') {
            this.mainTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              fixed: 'left',
              width: 'auto',
              formatter({ cellValue }) {
                return XEUtils.toDateString(cellValue, 'yyyy-MM-dd')
              }
            })
          } else if (item.value == '配比ID') {
            this.mainTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              width: 'auto',
              visible: false
            })
          } else if (item.value == '计划编号') {
            this.mainTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              width: 'auto',
              fixed: 'left'
            })
          } else if (item.value == '开始时间') {
            this.mainTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              width: 'auto',
              fixed: 'left'
            })
          } else if (item.value == '变更原因') {
            this.mainTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              width: 'auto',
              fixed: 'right'
            })
          } else if (item.value == '时间班组') {
            this.mainTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              width: 'auto',
              fixed: 'left'
            })
          } else if (item.value == '管理班组') {
            this.mainTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              width: 'auto',
              fixed: 'left'
            })
          } else if (item.value.includes('变更')) {
            this.mainTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              width: 'auto',
              visible: false
            })
          } else {
            this.mainTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              width: 'auto'
            })
          }

        })
        if (response.data.rec.length != 0) {
          response.data.rec.forEach((item) => {

            const fitler = this.mainTable.gridOptions.ratioMasterIds.filter((item2) => {
              return item2 == item.MATE_RATIO_MASTER_ID
            })

            if (fitler.length == 0) {
              this.mainTable.gridOptions.ratioMasterIds.push(item.MATE_RATIO_MASTER_ID)
            }
          })
        }
        this.mainTable.gridOptions.data = response.data.rec
      })
    },
    tableCellStyle({ row, column }) {
      if (column.type === 'checkbox') {
        return
      }
      if (row.CONTENT_TYPE === '配比') {

        if (column.field.startsWith('mate')) {

          const changeField = column.field.replace('mate', 'change')
          const value = row[changeField]
          if (value === true) {
            return {
              color: 'red',
              fontWeight: 'bold'
            }
          }
        }
      }
    },
    tableRowStyle({ row }) {
      var value = this.mainTable.gridOptions.ratioMasterIds.indexOf(row.MATE_RATIO_MASTER_ID)
      if (value % 2 != 0) {
        return { backgroundColor: 'white' }
      } else {
        return { backgroundColor: '#EBEEF5' }
      }
    },
    mergeRowsMethod({ row, rowIndex, column, data }) {
      if (column.type === 'radio') { // 若为单选框列
        const groupSize = 2 // 每 8 行合并一次
        const groupIndex = Math.floor(rowIndex / groupSize)
        const firstRowInGroup = groupIndex * groupSize
        if (rowIndex === firstRowInGroup) {
          return { rowspan: groupSize, colspan: 1 }
        } else {
          return { rowspan: 0, colspan: 0 }
        }
      }
      if (column.field === 'CHANGE_REASON') { // 若为单选框列
        const groupSize = 2 // 每 8 行合并一次
        const groupIndex = Math.floor(rowIndex / groupSize)
        const firstRowInGroup = groupIndex * groupSize
        if (rowIndex === firstRowInGroup) {
          return { rowspan: groupSize, colspan: 1 }
        } else {
          return { rowspan: 0, colspan: 0 }
        }
      }
      return { rowspan: 1, colspan: 1 }
    },
    getProdCenterCode() {
      return this.$route.query.prodCenterCode
    },
    handleRadioChange({ row }) {
      this.mainTable.selectedRadioRow = row
    },
    // 表单重置
    reset() {
      this.form = {
        workDate: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null,
        detailList: []
      }
      this.resetForm('form')
    },
    resetReasone() {
      this.formReason = {
        mateRatioMasterId: null,
        workDate: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null,
        beginTime: null,
        endTime: null,
        changeReason: null
      }
      this.resetForm('formReason')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryList()
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      queryLastEndTime(this.getProdCenterCode()).then(res => {
        this.form.beginTime = dayjs(res.data).add(1, 'second')
        this.editDialog.open = true
        this.editDialog.title = '添加烧结物料消耗'
      })

    },
    handleUpdateReason() {
      this.resetReasone()
      const selectedRows = this.mainTable.selectedRadioRow
      if (selectedRows == null) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
      }
      const id = selectedRows.MATE_RATIO_MASTER_ID
      this.editReasonDialog.open = true
      getEdit(id).then(res => {
        this.formReason = res.data

      })
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const selectedRows = this.mainTable.selectedRadioRow
      if (selectedRows == null) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
      }
      const id = selectedRows.MATE_RATIO_MASTER_ID

      getEdit(id).then(res => {

        let tCpesMateRatio = res.data
        var detialListResult = []

        var mateCodeList = tCpesMateRatio.detailList.map(item => {
          return item.mateCode
        })
        mateCodeList = mateCodeList.filter((item, index) => mateCodeList.indexOf(item) === index)
        mateCodeList.forEach(mateCodeListKey => {
          var item = { mateCode: mateCodeListKey, ratio: 0, qty: 0, mateRatioDetailId: 0 }

          tCpesMateRatio.detailList.forEach(detail => {
            if (detail.mateCode === mateCodeListKey) {
              item.mateRatioDetailId = detail.mateRatioDetailId
              if (detail.contentType === '配比') {
                item.ratio = item.ratio + detail.finalRatio
              }
              if (detail.contentType === '消耗量') {
                item.qty = item.qty + detail.finalRatio
              }
            }
          })

          detialListResult.push(item)
        })

        tCpesMateRatio.detailList = detialListResult
        this.form = tCpesMateRatio
        this.editDialog.open = true
      })

    },
    submitFormReason() {
      this.$refs['formReason'].validate(valid => {
        if (valid) {
          updateReason(this.formReason).then(res => {
            this.editReasonDialog.open = false
            this.handleQuery()
          })
        }
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (this.form.detailList.length == 0) {
          this.$message({
            message: '请填写物料消耗清单',
            type: 'warning'
          })
          return
        }
        var formPar = JSON.parse(JSON.stringify(this.form))

        const detialList = []
        for (let item of this.form.detailList) {
          var ratio = {
            mateCode: item.mateCode,
            inputRatio: item.ratio,
            contentType: '配比'
          }
          var qty = {
            mateCode: item.mateCode,
            inputRatio: item.qty,
            contentType: '消耗量'
          }
          if (ratio.inputRatio === 0) {
            this.$message({
              message: '配比不能等于零',
              type: 'warning'
            })
          }
          if (qty.inputRatio === 0) {
            this.$message({
              message: '消耗量不能等于零',
              type: 'warning'
            })
          }
          detialList.push(ratio)
          detialList.push(qty)
        }
        if (valid) {
          formPar.beginTime = dayjs(formPar.beginTime).format('YYYY-MM-DD HH:mm:ss')
          formPar.endTime = dayjs(formPar.endTime).format('YYYY-MM-DD HH:mm:ss')
          formPar.detailList = detialList
          formPar.prodCenterCode = this.getProdCenterCode()
          saveOrUpdate(formPar).then(response => {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.editDialog.open = false
            this.handleQuery()
          })
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const selectedRows = this.mainTable.selectedRadioRow
      if (selectedRows == null) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
      }
      const masterID = selectedRows.MATE_RATIO_MASTER_ID
      this.$modal
        .confirm('是否确认删除？')
        .then(function() {
          return delRatio(masterID)
        })
        .then(() => {
          this.handleQuery()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {
        })
    },

    /** 导出按钮操作 */
    handleExport() {
      const $table = this.$refs.tableMainRef
      if ($table) {
        $table.exportData({
          type: 'csv',
          filename: '烧结下料量', // 文件名
          sheetName: 'Sheet1' // sheet名称
        })
      }
    },
    editAddHandler() {
      this.form.detailList.push({
        mateRatioDetailId: -1 * new Date().getTime() / 1000,
        mateCode: null,
        ratio: 0,
        qty: 0
      })
    },
    editDeleteHandler() {
      const selectedRows = this.$refs.editTableef.getCheckboxRecords()
      if (selectedRows == null || selectedRows.length == 0) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
        return
      }
      selectedRows.forEach(item => {
        this.form.detailList.splice(this.form.detailList.indexOf(item), 1)
      })
    }
  },
  mounted() {
    this.$nextTick(() => {
      let topValue = document.getElementsByClassName('tableInfo')[0].getBoundingClientRect().top
      this.tableHeight = document.body.clientHeight - topValue - 10
      this.mainTable.gridOptions.height = this.tableHeight - 5
    })
  }
}
</script>
