<template>
  <div class="app-container" style="padding: 0px;">
    <div class="furnace-selector" style="margin-top:8px; padding: 8px 12px; background: #f8f9fa; border-radius: 6px; border: 1px solid #e9ecef;">
      <div class="furnace-buttons" style="display: flex; align-items: center; gap: 10px;">
        <el-button
          :type="selectParam.prodCenterCode === 'IPES01' ? 'primary' : 'default'"
          :class="{ 'active-furnace': selectParam.prodCenterCode === 'IPES01' }"
          @click="switchFurnace('IPES01')"
          size="small"
          class="compact-button"
          style="min-width: 70px; height: 26px; padding: 2px 8px; font-size: 12px;">
          1#高炉
        </el-button>
        <el-button
          :type="selectParam.prodCenterCode === 'IPES02' ? 'primary' : 'default'"
          :class="{ 'active-furnace': selectParam.prodCenterCode === 'IPES02' }"
          @click="switchFurnace('IPES02')"
          size="small"
          class="compact-button"
          style="min-width: 70px; height: 26px; padding: 2px 8px; font-size: 12px;">
          2#高炉
        </el-button>
      </div>
    </div>
    <!-- 上半部分：表格展示 -->
    <div style="margin-top: 7px; ">
      <vxe-table
        border
        align="center"
        :data="tableData"
        :row-config="{ height: 35 }"
        :header-row-config="{ height: 40 }"
        class="compact-table">
        <vxe-column v-for="(column, index) in columns" :key="`col-${column.binNameMixed}`" :field="column.field" :title="column.title" width="auto"></vxe-column>
      </vxe-table>
    </div>
  </div>
</template>

<script>
  import {slotPositionSelectDate,} from "@/api/report/preview/blastFurnace/slotPositionHour";
    export default {
        name: "slotPositionHour",
      data(){
          return{
            selectParam:{
              prodCenterCode:'IPES01',
              status:'生效',
              reportUnitCode:'',
            },
            prodCenterList:[
              {label: '1#高炉', value: 'IPES01'},
              {label: '2#高炉', value: 'IPES02'},
            ],
            siloData: [], // 存储原始料仓数据
            tableData:[
              {
                typeName: '料仓名',
              },
              {
                typeName: '物料名06:00',
              },
              {
                typeName: '料位高度06:00',
              },
              {
                typeName: '物料名12:00',
              },
              {
                typeName: '料位高度12:00',
              },
              {
                typeName: '物料名20:00',
              },
              {
                typeName: '料位高度20:00',
              },
            ],
            columns: [
              // { field: 'typeName', title: '类型' ,width:'auto'},
            ]
          }
      },
      created() {
          this.initData();
      },
      methods:{
        /* 切换高炉 */
        switchFurnace(furnaceCode) {
          if (this.selectParam.prodCenterCode !== furnaceCode) {
            this.selectParam.prodCenterCode = furnaceCode;
            // this.stopAutoRefresh();
            this.columns = []; // 重置列结构
            this.tableData = []; // 重置表格数据
            this.siloData = []; // 重置料仓数据
            this.initData(); // 重新初始化数据
          }
        },
        /* 初始化数据 */
        initData(){
          if("IPES01" == this.selectParam.prodCenterCode){
            this.selectParam.reportUnitCode = "IPES01004"
          }else if("IPES02" == this.selectParam.prodCenterCode){
            this.selectParam.reportUnitCode = "IPES02003"
          }
          slotPositionSelectDate(this.selectParam).then(response=>{
            if(response.data && response.data.length > 0){
              console.log("response.data:",JSON.stringify(response.data))
              let siloData = response.data
              this.siloData = siloData
              // 构建表格列（只在初始化时执行一次）
              this.columns.push({ field: 'typeName', title: '料仓名称', width: '120'})
              for(let i = 0; i < siloData.length; i++){
                let silo = siloData[i]
                this.columns.push({
                  title: silo.binName,
                  field: `silo_${silo.binId}`,
                  width: '100'
                })
              }
              // 构建表格数据
              this.buildTableData()
            }
          }).catch(error => {
            console.error('初始化数据失败:', error)
          })
        },

        /* 构建表格数据 */
        buildTableData(){

          this.tableData = [
            { typeName: '物料名06:00' },
            { typeName: '料位高度06:00' },
            { typeName: '物料名12:00' },
            { typeName: '料位高度12:00' },
            { typeName: '物料名20:00' },
            { typeName: '料位高度20:00' },
          ]

          // 每行数据添加料仓值
          this.tableData.forEach((row) => {
            this.siloData.forEach(silo => {
              const fieldName = `silo_${silo.binId}`
              switch(row.typeName) {
                case '料仓名':
                  row[fieldName] = silo.binName
                  break
                case '物料名06:00':
                  row[fieldName] = silo.materialName_6 || silo.binType || '-'
                  break
                case '料位高度06:00':
                  row[fieldName] = silo.materialHight_6 ? silo.materialHight_6.toFixed(2) : '0.00'
                  break
                case '物料名12:00':
                  row[fieldName] = silo.materialName_12 || silo.binType || '-'
                  break
                case '料位高度12:00':
                  row[fieldName] = silo.materialHight_12 ? silo.materialHight_12.toFixed(2) : '0.00'
                  break
                case '物料名20:00':
                  row[fieldName] = silo.materialName_20 || silo.binType || '-'
                  break
                case '料位高度20:00':
                  row[fieldName] = silo.materialHight_20 ? silo.materialHight_20.toFixed(2) :   '0.00'
                  break

              }
            })
          })
        },


      },
    }
</script>

<style scoped>

</style>
