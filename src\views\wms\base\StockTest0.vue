<template>
    <div class="app-container" style="padding:10px;">

        <vxe-grid ref="gridRef" v-bind="gridOptions" @page-change="pageChangeEvent">
            <template #form>
                <vxe-form ref="searchFormRef" v-bind="searchFormOptions">
                    <template #action>
                        <vxe-button status="primary" @click="searchEvent">搜索</vxe-button>
                        <vxe-button @click="resetEvent">重置</vxe-button>
                    </template>
                </vxe-form>
            </template>

            <template #toolbarButtons>
                <vxe-button status="success" @click="inEvent">入库</vxe-button>
                <vxe-button status="error" @click="outEvent">出库</vxe-button>
                <vxe-button status="primary" @click="stackEvent">倒垛</vxe-button>
                <!--<vxe-button status="warning" @click="">调拨</vxe-button> -->
            </template>
        </vxe-grid>

        <!-- 入库  -->
        <el-dialog title="入库" width="500px" :visible.sync="open" append-to-body>
            <el-form ref="form" :model="form" label-width="80px">
                <el-form-item label="入库物料" prop="mateCode">
                    <el-select v-model="form.mateCode" filterable placeholder="请选择物料" style="width: 100%;"
                        @change="mateChange">
                        <el-option v-for="dict in mateList" :key="dict.value" :label="dict.label" :value="dict.value">
                            <span style="float: left">{{ dict.label }}</span>
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="料条" prop="crossRegion">
                    <el-select v-model="form.crossRegion" filterable placeholder="请选择料条" style="width: 100%;">
                        <el-option v-for="dict in stripList" :key="dict.value" :label="dict.label" :value="dict.value">
                            <span style="float: left">{{ dict.label }}</span>
                        </el-option>
                    </el-select>
                    <!-- <el-input v-model="form.crossRegion" type="text" placeholder="请输入内容" /> -->
                </el-form-item>

                <el-form-item label="垛位" prop="stackingPosition">
                    <el-input v-model="form.stackingPosition" type="text" placeholder="请输入内容" />
                </el-form-item>

                <el-form-item label="入库重量" prop="weight">
                    <el-input-number v-model="form.weight" :precision="4" :step="1" :min="0"
                        style="width: 100%;"></el-input-number>
                </el-form-item>

            </el-form>

            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>


        <!-- 倒垛  -->
        <el-dialog title="倒垛" width="500px" :visible.sync="openStack" append-to-body>
            <el-form ref="form" :model="formStack" label-width="80px">
                <el-form-item label="入库物料" prop="mateCode">
                    <el-select v-model="formStack.mateCode" filterable placeholder="请选择物料" style="width: 100%;"
                        @change="mateChange">
                        <el-option v-for="dict in mateList" :key="dict.value" :label="dict.label" :value="dict.value">
                            <span style="float: left">{{ dict.label }}</span>
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="料条" prop="crossRegion">
                    <el-select v-model="formStack.crossRegion" filterable placeholder="请选择料条" style="width: 100%;">
                        <el-option v-for="dict in stripList" :key="dict.value" :label="dict.label" :value="dict.value">
                            <span style="float: left">{{ dict.label }}</span>
                        </el-option>
                    </el-select>
                    <!-- <el-input v-model="form.crossRegion" type="text" placeholder="请输入内容" /> -->
                </el-form-item>

                <el-form-item label="垛位" prop="stackingPosition">
                    <el-input v-model="formStack.stackingPosition" type="text" placeholder="请输入内容" />
                </el-form-item>

                <el-form-item label="倒入料条" prop="toCrossRegion">
                    <el-select v-model="formStack.toCrossRegion" filterable placeholder="请选择料条" style="width: 100%;">
                        <el-option v-for="dict in stripList" :key="dict.value" :label="dict.label" :value="dict.value">
                            <span style="float: left">{{ dict.label }}</span>
                        </el-option>
                    </el-select>
                    <!-- <el-input v-model="form.crossRegion" type="text" placeholder="请输入内容" /> -->
                </el-form-item>

                <el-form-item label="倒入垛位" prop="toStackingPosition">
                    <el-input v-model="formStack.toStackingPosition" type="text" placeholder="请输入内容" />
                </el-form-item>

                <el-form-item label="倒入重量" prop="weight">
                    <el-input-number v-model="formStack.weight" :precision="4" :step="1" :min="0"
                        style="width: 100%;"></el-input-number>
                </el-form-item>
            </el-form>

            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitFormStack">确 定</el-button>
                <el-button @click="cancelStack">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { initMaterialList } from "@/api/md/material";
import { listStorehouse } from "@/api/wms/storehouse";

import { selectStockRealData, selectStockRealDataByCode } from "@/api/wms/stockreal";

export default {
    name: 'StockTest',
    data() {

        const stripEditRender = {
            name: 'VxeSelect',
            props: {
                clearable: true,
            },
            options: [
                { value: 'A', label: 'A' },
                { value: 'B', label: 'B' },
                { value: 'C', label: 'C' },
                { value: 'D', label: 'D' },
                { value: 'E', label: 'E' },
            ]
        }

        const gridOptions = {
            columns: [
                { type: 'checkbox', width: 50 },
                { type: 'seq', width: 50 },
                { field: 'stockRealId', visible: false },
                { field: 'storehouseName', title: '库存地', },
                { field: 'crossRegion', title: '料条', },
                { field: 'stackingPosition', title: '垛位', },
                { field: 'mateCode', title: '物料编码', visible: false },
                { field: 'mateName', title: '物料名称', },
                { field: 'stockWeight', title: '库存重量', editRender: { name: 'VxeNumberInput' } },
                { field: 'remark', title: '备注', editRender: { name: 'VxeTextarea' } },
            ],
            data: [],

            border: true,
            strip: true,
            align: 'center',
            showOverflow: true,
            keepSource: true,
            loading: false,

            columnConfig: {
                resizable: true
            },
            rowConfig: {
                isHover: true,
                isCurrent: true,
            },
            pagerConfig: {
                total: 0,
                currentPage: 1,
                pageSize: 10
            },

            toolbarConfig: {
                // custom: true,
                zoom: true,
                slots: {
                    buttons: 'toolbarButtons'
                }
            },

            editConfig: {
                mode: 'cell',
                trigger: 'dblclick',
                showStatus: true,
            },
        }

        const searchFormOptions = {
            data: {
                storehouseCode: '',
                crossRegion: '',
                stackingPosition: '',
                mateCode: '',
                mateName: '',
            },
            items: [
                { field: 'crossRegion', title: '料条', itemRender: stripEditRender },
                { field: 'mateName', title: '物料名称', itemRender: { name: 'VxeInput' } },
                { slots: { default: 'action' } }
            ]
        }

        return {
            gridOptions,
            searchFormOptions,

            title: '',
            open: false,
            // 提交表单
            form: {
                storehouseCode: '',
                mateCode: '',
                crossRegion: '',
                stackingPosition: '',
                weight: 0,
            },
            // 提交表单校验
            rules: {
                storehouseCode: [
                    {
                        required: true, message: "仓库编码不能为空", trigger: "blur"
                    }
                ],
            },

            titleStack: '',
            openStack: false,
            // 提交表单
            formStack: {
                storehouseCode: '',
                mateCode: '',
                crossRegion: '',
                stackingPosition: '',
                weight: 0,
                toCrossRegion: '',
                toStackingPosition: '',
            },
            // 提交表单校验
            rulesStack: {
                storehouseCode: [
                    {
                        required: true, message: "仓库编码不能为空", trigger: "blur"
                    }
                ],
            },



            mateList: [],
            storehouseList: [],
            stripList: [
                { value: 'A', label: 'A' },
                { value: 'B', label: 'B' },
                { value: 'C', label: 'C' },
                { value: 'D', label: 'D' },
                { value: 'E', label: 'E' },
            ],

        }
    },
    methods: {
        searchList() {
            this.handlePageData()
        },
        handlePageData() {
            this.gridOptions.loading = true
            this.searchFormOptions.data.storehouseCode = this.getProdCenterCode()
            selectStockRealData(this.searchFormOptions.data).then(response => {
                let data = response.data
                const { pageSize, currentPage } = this.gridOptions.pagerConfig
                this.gridOptions.pagerConfig.total = data.length
                this.gridOptions.data = data.slice((currentPage - 1) * pageSize, currentPage * pageSize)
                this.gridOptions.loading = false
            })
        },
        pageChangeEvent({ pageSize, currentPage }) {
            this.gridOptions.pagerConfig.currentPage = currentPage
            this.gridOptions.pagerConfig.pageSize = pageSize
            this.handlePageData()
        },

        searchEvent() {
            this.searchList()
        },
        resetEvent() {
            const $form = this.$refs.searchFormRef
            if ($form) {
                $form.reset()
                this.searchList()
            }
        },

        initList() {
            initMaterialList().then(response => {
                let data = response.data
                let list = []
                for (let i = 0; i < data.length; i++) {
                    list.push({
                        value: `${data[i].materialNumber}`,
                        label: `${data[i].shortName ? data[i].shortName : data[i].materialName}`
                    })
                }
                this.mateList = list
                // this.materialEditRender.options = list
            })

            listStorehouse(null).then(response => {
                let data = response.data
                let list = []
                for (let i = 0; i < data.length; i++) {
                    list.push({
                        value: `${data[i].storehouseCode}`,
                        label: `${data[i].storehouseName}`
                    })
                }
                this.storehouseList = list
                // this.storehouseEditRender.options = list
            })
        },

        // 入库
        inEvent() {
            this.reset()

            const $grid = this.$refs.gridRef
            if ($grid) {
                this.open = true
                this.title = "入库"
                let ck = $grid.getCheckboxRecords()
                if (ck.length === 1) {
                    this.form.storehouseCode = ck[0].storehouseCode
                    this.form.mateCode = ck[0].mateCode
                    this.form.crossRegion = ck[0].crossRegion
                    this.form.stackingPosition = ck[0].stackingPosition
                }
            }
        },
        // 出库
        outEvent() {
            this.reset()

            const $grid = this.$refs.gridRef
            if ($grid) {
                let ck = $grid.getCheckboxRecords()
                if (ck.length != 1) {
                    this.$modal.msgSuccess('请选择一条出库数据')
                } else {
                    this.open = true
                    this.title = "出库"

                    this.form.storehouseCode = ck[0].storehouseCode
                    this.form.mateCode = ck[0].mateCode
                    this.form.crossRegion = ck[0].crossRegion
                    this.form.stackingPosition = ck[0].stackingPosition
                }
            }
        },

        // 倒垛
        stackEvent() {
            this.reset()

            const $grid = this.$refs.gridRef
            if ($grid) {
                let ck = $grid.getCheckboxRecords()
                if (ck.length != 1) {
                    this.$modal.msgSuccess('请选择一条倒垛数据')
                } else {
                    this.openStack = true
                    this.title = "倒垛"

                    this.formStack.storehouseCode = ck[0].storehouseCode
                    this.formStack.mateCode = ck[0].mateCode
                    this.formStack.crossRegion = ck[0].crossRegion
                    this.formStack.stackingPosition = ck[0].stackingPosition
                }
            }
        },

        mateChange() {
            this.form.storehouseCode = this.getProdCenterCode()
            selectStockRealDataByCode(this.form).then(response => {
                let data = response.data
                if (data.length === 1) {
                    this.form.crossRegion = data[0].crossRegion
                    this.form.stackingPosition = data[0].stackingPosition
                }
            })

        },

        submitForm() {
            if (this.title === '入库') {
                console.log('入库');
                this.open = false
                this.reset()
                console.log(this.form);
                this.searchList()
            }
            if (this.title === '出库') {
                console.log('出库');
                this.open = false
                this.reset()
                console.log(this.form);
                this.searchList()
            }
        },
        cancel() {
            this.open = false
            this.reset()
        },
        reset() {
            this.form = {
                storehouseCode: '',
                mateCode: '',
                crossRegion: '',
                stackingPosition: '',
                weight: 0,
            }

            this.formStack = {
                storehouseCode: '',
                mateCode: '',
                crossRegion: '',
                stackingPosition: '',
                weight: 0,
                toCrossRegion: '',
                toStackingPosition: '',
            }
        },

        submitFormStack() {
            console.log(this.formStack);

        },
        cancelStack() {
            this.openStack = false
            this.reset()
        },

        getProdCenterCode() {
            return this.$route.query.prodCenterCode
        },

    },
    created() {
        this.initList()
    },
    mounted() {
        this.searchList()
    },
}
</script>