import Vue from 'vue'

import Cookies from 'js-cookie'

import Element from 'element-ui'
import './assets/styles/element-variables.scss'

// 添加研究院表格样式
import 'dlTable/dist/dlTable.css'

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/lingxiao.scss' // lingxiao css

import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive
import plugins from './plugins' // plugins
import { download } from '@/utils/request'


import './assets/icons' // icon
import './permission' // permission control
import { getDicts } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from "@/utils/lingxiao";
// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar"
// 富文本组件
import Editor from "@/components/Editor"
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
// 图片预览组件
import ImagePreview from "@/components/ImagePreview"
// 字典标签组件
import DictTag from '@/components/DictTag'
// 头部标签组件
import VueMeta from 'vue-meta'
// 字典数据组件
import DictData from '@/components/DictData'
// 引入研究院表格插件
import dlTable from 'dlTable'

//需要按需引入，先引入vue并引入element-ui
import AFTableColumn from 'af-table-column'

import {Split} from 'view-design'

import 'view-design/dist/styles/iview.css'
// 面板分割组件
import SplitPane from 'vue-splitpane';
//引入echarts
import * as echarts from 'echarts'

//kform
import { useAntd } from 'k-form-design/packages/core/useComponents'
import KFormDesign from 'k-form-design/packages/use.js'
import 'k-form-design/lib/k-form-design.css'
//vform
import axios from 'axios'  //如果需要axios，请引入
import ElementUI from 'element-ui'  //引入element-ui库
import VForm from 'vform-builds'  //引入VForm库
import 'element-ui/lib/theme-chalk/index.css'  //引入element-ui样式
import 'vform-builds/dist/VFormDesigner.css'  //引入VForm样式
import { Base64 } from "js-base64";
import VxeUIAll from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'
import VxeUITable from 'vxe-table'
import 'vxe-table/lib/style.css'
import VxeUIPluginRenderElement from '@vxe-ui/plugin-render-element'
import '@vxe-ui/plugin-render-element/dist/style.css'

//日期格式化插件
import moment from 'moment';

import $ from 'jquery';
Vue.prototype.$jq = $

// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
Vue.prototype.$moment = moment
//vue全局注入echarts
Vue.prototype.$echarts = echarts
// 全局组件挂载
Vue.component('DictTag', DictTag)
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Editor', Editor)
Vue.component('FileUpload', FileUpload)
Vue.component('ImageUpload', ImageUpload)
Vue.component('ImagePreview', ImagePreview)
Vue.component('iSplit', Split)
Vue.component('SplitPane', SplitPane)
Vue.use(directive)
Vue.use(plugins)
Vue.use(VueMeta)
// 研究院表格全局注册
Vue.use(dlTable)
// kform
Vue.use(KFormDesign)
useAntd(Vue)
// vform
Vue.use(ElementUI)
Vue.use(VForm)
Vue.use(Base64)
//vuex
Vue.use(VxeUIAll)
Vue.use(VxeUITable)
VxeUIAll.use(VxeUIPluginRenderElement)

window.axios = axios

// 修改 el-dialog 默认点击遮照不关闭
Element.Dialog.props.closeOnClickModal.default = false;

const fontRate = {
    CHAR_RATE: 1.1, // 汉字比率
    NUM_RATE: 0.65, // 数字
    OTHER_RATE: 0.8 // 除汉字和数字以外的字符的比率
  }
const fontSize = 20
Vue.use(AFTableColumn, { fontRate, fontSize })
DictData.install()

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
    size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.config.productionTip = false
Vue.config.devtools = true

new Vue({
    el: '#app',
    router,
    store,
    render: h => h(App)
})
