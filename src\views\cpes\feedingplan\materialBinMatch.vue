<template>
  <div class="material-bin-match">
    <div class="header-section">
      <div class="title">物料-料仓匹配</div>
      <div class="action-buttons">
        <el-button
          type="warning"
          size="mini"
          @click="handleEdit"
          :disabled="!planid"
        >
          {{ isEditing ? '取消编辑' : '修改' }}
        </el-button>
        <el-button
          type="primary"
          size="mini"
          @click="handleSave"
          :disabled="!planid || !isEditing"
        >
          保存
        </el-button>

      </div>
    </div>

    <div class="content-section">
      <el-table
        :data="materialList"
        border
        style="width: 100%"
        :header-cell-style="{background:'#f5f7fa', color:'#606266'}"
        size="mini"
      >
        <!-- <el-table-column
          prop="materialCode"
          label="物料编码"
          width="120"
          align="center"
        ></el-table-column> -->

        <el-table-column
          prop="materialName"
          label="物料名称"
          width="150"
          align="center"
        ></el-table-column>

        <el-table-column
          label="对应料仓"
          width="200"
          align="center"
        >
          <template slot-scope="scope">
            <div v-if="!isEditing">
              {{ getBinDisplayName(scope.row.selectedBin) || '未选择' }}
            </div>
            <el-select
              v-else
              v-model="scope.row.selectedBin"
              placeholder="请选择料仓"
              size="mini"
              style="width: 100%"
              @change="handleBinChange(scope.row, scope.$index)"
              clearable
            >
              <el-option
                v-for="bin in availableBins"
                :key="bin.siloCode || bin.id"
                :label="bin.siloName"
                :value="bin.siloCode"
                :disabled="isBinOccupied(bin.siloCode, scope.row)"
                v-if="bin && bin.siloCode"
              >
                <span style="float: left">{{ bin.siloName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ bin.siloCode }}</span>
              </el-option>
            </el-select>
          </template>
        </el-table-column>

      </el-table>
    </div>



  </div>
</template>

<script>
import { getBinData, saveMaterialBinMatch, saveSiloExtend, getSiloExtendList } from '@/api/feedingplan/feedingplan'

export default {
  name: 'MaterialBinMatch',
  props: {
    planid: {
      type: [String, Number],
      default: ''
    },
    prodCode: {
      type: String,
      default: ''
    },
    dataList2nd: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isEditing: false,
      materialList: [],
      availableBins: [],
      matchRelations: {},
      originalMaterialList: [],
      siloExtendData: []
    }
  },
  computed: {
    usedBinsCount() {
      return this.materialList.filter(item => item.selectedBin).length
    },

    matchCompletionRate() {
      if (this.materialList.length === 0) return 0
      return Math.round((this.usedBinsCount / this.materialList.length) * 100)
    },


  },
  mounted() {
    this.$nextTick(() => {
      this.fetchData()
    })
  },
  watch: {
    planid: {
      handler(newVal, oldVal) {
        if (newVal && newVal !== oldVal) {
          this.fetchData()
        }
      },
      immediate: true
    },
    prodCode: {
      handler(newVal, oldVal) {
        if (newVal && newVal !== oldVal) {
          this.fetchData()
        }
      },
      immediate: true
    },
    dataList2nd: {
      handler(newVal, oldVal) {
        if (newVal && newVal.length > 0 && this.processMaterialDataFromList2nd) {
          this.processMaterialDataFromList2nd()
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    fetchData() {
      this.fetchMaterialData()
      if (this.prodCode) {
        this.fetchBinData()
      }
      if (this.planid) {
        // 延迟执行，避免竞态条件
        this.$nextTick(() => {
          this.fetchSiloExtendData()
        })
      }
    },

    fetchMaterialData() {
      if (this.dataList2nd && this.dataList2nd.length > 0) {
        this.processMaterialDataFromList2nd()
      }
    },

    fetchBinData() {
      if (!this.prodCode) {
        this.$message.warning('未提供生产中心编码，无法获取料仓数据')
        return Promise.resolve()
      }

      return getBinData(this.prodCode)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.availableBins = response.data
          } else {
            this.$message.warning('获取料仓数据失败：' + (response.msg || '接口返回异常'))
          }
        })
        .catch(error => {
          this.$message.error('获取料仓数据失败')
        })
    },

    async fetchSiloExtendData() {
      if (!this.planid) {
        return
      }

      try {
        const response = await getSiloExtendList(this.planid)

        if (response && typeof response === 'object' && response.code === 200) {
          this.siloExtendData = Array.isArray(response.data) ? response.data : []

          // 如果有已保存的对应关系，则更新物料列表
          if (this.siloExtendData.length > 0) {
            this.processMaterialDataWithSiloExtend()
          }
        } else {
          this.siloExtendData = []
        }
      } catch (error) {
        this.siloExtendData = []
        console.error('获取料仓扩展数据失败:', error)
      }
    },

    processMaterialDataFromList2nd() {
      const materials = []

      this.dataList2nd.forEach((item, index) => {
        if (item.materialname) {
          materials.push({
            materialCode: item.materialcode || item.materialnumber || '',
            materialName: item.materialname,
            blockType: `BLOCK${index + 1}`,
            quantity: item.actVal || item.settingVal || 0,
            selectedBin: '',
            originalData: item
          })
        }
      })

      this.materialList = materials

      // 如果有已保存的对应关系数据，则应用到物料列表中
      if (this.siloExtendData && this.siloExtendData.length > 0) {
        this.applyExistingMatches()
      }
    },

    processMaterialDataWithSiloExtend() {
      if (!this.siloExtendData || this.siloExtendData.length === 0) {
        return
      }

      const materials = []

      // 使用已保存的对应关系数据构建物料列表
      this.siloExtendData.forEach((item, index) => {
        materials.push({
          materialCode: item.mateCode,
          materialName: item.mateName,
          blockType: `BLOCK${index + 1}`,
          quantity: 0, // 数量信息在对应关系中没有，设为0
          selectedBin: item.siloCode,
          originalData: item
        })
      })

      // dataList2nd中有额外的物料，添加进来
      if (this.dataList2nd && this.dataList2nd.length > 0) {
        this.dataList2nd.forEach((dataItem, index) => {
          if (dataItem.materialname) {
            // 检查是否已经在已保存关系中
            const existingMatch = this.siloExtendData.find(siloItem =>
              siloItem.mateCode === (dataItem.materialcode || dataItem.materialnumber) ||
              siloItem.mateName === dataItem.materialname
            )

            if (!existingMatch) {
              // 如果不在已保存关系中，添加为新物料
              materials.push({
                materialCode: dataItem.materialcode || dataItem.materialnumber || '',
                materialName: dataItem.materialname,
                blockType: `BLOCK${materials.length + 1}`,
                quantity: dataItem.actVal || dataItem.settingVal || 0,
                selectedBin: '',
                originalData: dataItem
              })
            }
          }
        })
      }

      this.materialList = materials
    },

    applyExistingMatches() {
      // 将对应关系应用到当前物料列表中
      this.materialList.forEach(material => {
        const existingMatch = this.siloExtendData.find(siloItem =>
          siloItem.mateCode === material.materialCode ||
          siloItem.mateName === material.materialName
        )

        if (existingMatch) {
          material.selectedBin = existingMatch.siloCode
          // 已保存关系中的物料名称不同，使用已保存的名称
          if (existingMatch.mateName !== material.materialName) {
            material.materialName = existingMatch.mateName
          }
        }
      })
    },

    processMaterialData(blockData) {
      const materials = []

      Object.keys(blockData).forEach(blockType => {
        const blockItems = blockData[blockType] || []
        blockItems.forEach(item => {
          if (item.materialname) {
            materials.push({
              materialCode: item.materialcode || item.materialnumber || '',
              materialName: item.materialname,
              blockType: blockType.toUpperCase(),
              quantity: item.actVal || item.settingVal || 0,
              selectedBin: '',
              originalData: item
            })
          }
        })
      })

      this.materialList = materials
    },

    handleBinChange(row, index) {
      if (row.selectedBin) {
        const selectedBin = this.availableBins.find(bin => bin.siloCode === row.selectedBin)
        const binName = selectedBin ? selectedBin.siloName : row.selectedBin
        this.$message.success(` ${row.materialName} 已匹配到料仓 ${binName}`)
      } else {
        this.$message.info(`已清除物料 ${row.materialName} 的料仓匹配`)
      }
    },

    handleEdit() {
      if (this.isEditing) {
        // 取消编辑，恢复原始数据
        this.materialList = JSON.parse(JSON.stringify(this.originalMaterialList))
        this.isEditing = false
        //this.$message.info('已取消编辑')
      } else {
        // 开始编辑，保存原始数据
        this.originalMaterialList = JSON.parse(JSON.stringify(this.materialList))
        this.isEditing = true
        //this.$message.info('进入编辑模式')
      }
    },

    getBinDisplayName(binCode) {
      if (!binCode || !this.availableBins) return ''
      const bin = this.availableBins.find(item => item && item.siloCode === binCode)
      return bin ? bin.siloName : binCode
    },

    isBinOccupied(binCode, currentRow) {
      if (!binCode || !this.materialList || !currentRow) {
        return false
      }
      return this.materialList.some(item =>
        item.selectedBin === binCode && item !== currentRow
      )
    },

    getBinStatusType(selectedBin) {
      if (!selectedBin) return 'info'
      return 'success'
    },

    getBinStatusText(selectedBin) {
      if (!selectedBin) return '未匹配'
      return '已匹配'
    },

    formatNumber(value) {
      if (value === null || value === undefined) return '-'
      if (typeof value === 'number') return value.toFixed(2)
      if (typeof value === 'string' && !isNaN(parseFloat(value))) {
        return parseFloat(value).toFixed(2)
      }
      return value
    },

    handleRefresh() {
      this.fetchData()
    },

    handleSave() {
      const unmatchedMaterials = this.materialList.filter(item => !item.selectedBin)

      if (unmatchedMaterials.length > 0) {
        this.$confirm(
          `还有 ${unmatchedMaterials.length} 个物料未匹配料仓，是否继续保存？`,
          '提示',
          {
            confirmButtonText: '继续保存',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          this.doSave()
        })
      } else {
        this.doSave()
      }
    },

    doSave() {
      // 过滤出已选择料仓的物料
      const selectedMaterials = this.materialList.filter(item => item.selectedBin)

      if (selectedMaterials.length === 0) {
        this.$message.warning('请至少选择一个料仓进行匹配')
        return
      }

      // 构造保存数据数组
      const saveDataArray = selectedMaterials.map(item => {
        const selectedBin = this.availableBins.find(bin => bin.siloCode === item.selectedBin)
        return {
          planId: this.planid,
          prodCenterCode: this.prodCode,
          siloCode: item.selectedBin,
          siloName: selectedBin ? selectedBin.siloName : '',
          mateCode: item.materialCode,
          mateName: item.materialName
        }
      })

      // 发送保存请求
      saveSiloExtend(saveDataArray)
        .then(response => {
          if (response.code === 200) {
            this.$message.success('匹配关系保存成功')
            this.isEditing = false
            this.originalMaterialList = JSON.parse(JSON.stringify(this.materialList))
          } else {
            this.$message.error(response.msg || '保存失败')
          }
        })
        .catch(error => {
          this.$message.error('保存失败，请稍后重试')
        })
    }
  }
}
</script>

<style scoped>
.material-bin-match {
  padding: 20px;
  background-color: #f5f7fa;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  padding-left: 5px;
  border-left: 4px solid #409EFF;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.content-section {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.statistics-section {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.statistics-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
  padding-left: 5px;
  border-left: 4px solid #67C23A;
}

.statistics-content {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
}

.stat-label {
  color: #606266;
  margin-right: 5px;
}

.stat-value {
  font-weight: bold;
  color: #303133;
}

.stat-value.used {
  color: #E6A23C;
}

.stat-value.available {
  color: #67C23A;
}

/deep/ .el-table th {
  background-color: #f5f7fa;
  padding: 8px 0;
  font-weight: bold;
}

/deep/ .el-table td {
  padding: 8px 0;
}

/deep/ .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}
</style>

