import request from '@/utils/request'

// 查询启停机管理明细列表
export function listDetail(query,selectVO) {
  return request({
    url: '/api/cpes/startStopDetail/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询启停机管理明细详细
export function getDetail(detailId) {
  return request({
    url: '/api/cpes/startStopDetail/' + detailId,
    method: 'get'
  })
}

// 新增启停机管理明细
export function addDetail(data) {
  return request({
    url: '/api/cpes/startStopDetail',
    method: 'post',
    data: data
  })
}

// 修改启停机管理明细
export function updateDetail(data) {
  return request({
    url: '/api/cpes/startStopDetail',
    method: 'put',
    data: data
  })
}

// 删除启停机管理明细
export function delDetail(detailId) {
  return request({
    url: '/api/cpes/startStopDetail/' + detailId,
    method: 'delete'
  })
}

export function queryForManger(query) {
  return request({
    url: '/api/cpes/startStopDetail/queryForManger',
    method: 'get',
    params: query
  })
}
