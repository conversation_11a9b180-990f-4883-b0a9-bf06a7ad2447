<template>
  <div class="app-container">
    <el-row>
      <fromSearchBox ref="searchbox" @searchBoxAction="searchBoxAction"></fromSearchBox>
    </el-row>
    <el-row :gutter="10" class="mb8" v-show="flgedit">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" :v-if=false>新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" v-if="false" :disabled="single"
          @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" v-if="false"
          @click="handleExport">导出</el-button>
      </el-col>
    </el-row>

    <template>
      <div>
        <vxe-grid ref="vxegrid" v-bind="gridOptions" @page-change="pageChangeEvent" @menu-click=menuClickEvent></vxe-grid>
      </div>
    </template>

    <!-- 添加或修改报表单元对话框 -->
    <el-dialog ref="dialogForm" :title="title" :visible.sync="formopen" v-if="formopen" width="800px"
      @close='closeDialog' append-to-body>
      <fromBuilder ref="fromBuilder" :propPageKey="pageKey" :propDefFormData="defFormData"
        :propFilterParam="filterParam" :propRowData="subRowData">
      </fromBuilder>
    </el-dialog>

  </div>
</template>

<script>
import { getChart, getcols, gettabledata } from "@/api/formtemplate/formdylist";
import fromSearchBox from './fromSearchBox';
import fromBuilder from './fromBuilder';
import { watch } from "vue";

export default {
  name: "Unit",
  dicts: ["OPER_SOURCE", "SUMMARY_TYPE", "record_status", "form_template_details_status_config"],
  components: { fromSearchBox, fromBuilder },
  data() {
    return {
      // 表格配置
      gridOptions: {},
      // 页面编码
      pageKey: '',
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 选择所有的数据
      selectionData: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      formopen: false,
      flgedit: false,
      // 表单页类型
      formtype: '1',
      //加工中心菜单
      editProductCenterList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        prodCenterCode: null,
        prodCenterName: null,
        reportUnitCode: null,
        reportUnitName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      columns: [],
      selectVO: '',
      defFormData: {},
      filterParam: {},
      pkfieldkey: '',
      subRowData: {},
    };
  },
  watch: {

  },
  created() {
    this.defFormData = this.$route.query;
  },
  mounted() {
    this.pageKey = this.$route.query.pageKey;
    this.getcols();
  },
  methods: {
    /** 查询报表单元列表 */
    getList() {
      console.log("加载表格数据");
      this.getcols();
      this.getTableData();
    },
    getcols() {
      this.loading = true;
      getcols(this.pageKey).then((resp) => {
        this.gridOptions = resp;
        this.formtype = resp.formtype;
        this.flgedit = resp.allowedit === '1';
        this.pkfieldkey = resp.pkfieldkey;
        this.loading = false;
        // console.log(this.gridOptions);
      })
    },
    getTableData() {
      this.searchBoxAction();
    },
    pageChangeEvent({ pageSize, currentPage }) {
      this.gridOptions.pagerConfig.currentPage = currentPage
      this.gridOptions.pagerConfig.pageSize = pageSize
      this.getTableData();
    },
    getProdCenterCode() {
      return this.$route.query.prodCenterCode;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      if (this.formtype === '1')
        this.formopen = false;
    },
    // this.reset();
    // 表单重置
    reset() {
      // this.$refs.fromBuilder.clearFormData();
    },
    // 初始化表单布局
    initForm() {
      // if (this.selectionData.length === 1) {
      //   this.$refs.fromBuilder.filterParam = { "con1": this.selectionData[0].MASTERID };
      // }
      // this.$refs.fromBuilder.pageKey = this.pageKey;
      // this.$refs.fromBuilder.defFormData = this.$route.query;
      // this.$refs.fromBuilder.renderForm(this.pageKey);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      //console.log("selectionselection:", JSON.stringify(selection))
      this.selectionData = selection;
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.$refs.fromBuilder.saveflag = false;
      this.filterParam = {};
      if (this.formtype === '1') {
        this.formopen = true;
      }
      this.open = true;
      this.title = "增加数据";
      this.initForm()
      this.reset();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      if (this.selectionData.length === 1) {
        this.filterParam = { "con1": this.selectionData[0].MASTERID };
      }
      if (this.formtype === '1') {
        this.formopen = true;
      }
      try {
        this.reset();
      } catch (error) {

      }
      this.open = true;
      this.title = "修改数据";
      this.initForm()
    },
    // 右键事件
    menuClickEvent ({ menu,row }) {
      console.log(`点击了 ${menu.name} 选项`)
      console.log(row);
      this.handleUpdateMenu(row);
    },
    // 右键修改按钮
    handleUpdateMenu(row) {
      this.subRowData = row;
      this.filterParam = { "con1" : row[this.pkfieldkey] };
      if (this.formtype === '1') {
        this.formopen = true;
      }
      try {
        this.reset();
      } catch (error) {

      }
      this.open = true;
      this.title = "修改数据";
      this.initForm()
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const obj = JSON.parse(JSON.stringify(this.form));
          if (obj.operSource != null) {
            obj.operSource = obj.operSource.join();
          }

          saveOrUpdate(obj).then((response) => {
            this.$modal.msgSuccess("保存成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const reportUnitIds = row.reportUnitId || this.ids;
      this.$modal
        .confirm("确认删除选中的报表单元？")
        .then(function () {
          return delUnit(reportUnitIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.loading = true;
      this.$refs.searchbox.$refs.vfr.getFormData().then(data => {
        this.download(
          "/formtemp/common/dltable/export",
          {
            "pageKey": this.pageKey,
            "filter": JSON.stringify(data),
          },
          `${this.pageKey}_${new Date().getTime()}.xlsx`
        ).then(ret => {
          this.loading = false;
        }).error(ret => {
          this.loading = true;
        });
      })
    },
    searchBoxAction() {
      this.loading = true;
      this.$refs.searchbox.$refs.vfr.getFormData().then(data => {
        let tbconfig = {
          pagenum: this.gridOptions.pagerConfig.currentPage,
          pagesize: this.gridOptions.pagerConfig.pageSize
        };
        gettabledata(this.pageKey, JSON.stringify(data), JSON.stringify(tbconfig)).then((resp) => {
          this.gridOptions.data = resp.tbdata;
          this.gridOptions.pagerConfig.total = resp.total;
          this.loading = false;
          this.$refs.vxegrid.loadData(this.gridOptions.data);
          // console.log(this.gridOptions);
        })
      })
    },
    //关闭弹框的事件
    closeDialog() {
      // saveflag
      if (this.$refs.fromBuilder.saveflag) {
        this.searchBoxAction();
      }
      this.$refs.fromBuilder.saveflag = false;
      this.reset();
    },
  },
};
</script>
