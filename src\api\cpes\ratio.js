import request from '@/utils/request'

// 查询烧结物料配比主列表
export function listRatio(query,selectVO) {
  return request({
    url: '/api/cpes/ratio/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

export function queryforManger(query) {
  return request({
    url: '/api/cpes/ratio/queryforManger',
    method: 'get',
    params: query
  })
}
export function getEdit(mateRatioMasterId) {
  return request({
    url: '/api/cpes/ratio/getEdit/' + mateRatioMasterId,
    method: 'get'
  })
}

// 查询烧结物料配比主详细
export function getRatio(mateRatioMasterId) {
  return request({
    url: '/api/cpes/ratio/' + mateRatioMasterId,
    method: 'get'
  })
}
export function updateReason(data) {
  return request({
    url: '/api/cpes/ratio/updateReason',
    method: 'post',
    data: data
  })
}
// 新增烧结物料配比主
export function addRatio(data) {
  return request({
    url: '/api/cpes/ratio',
    method: 'post',
    data: data
  })
}

export function saveOrUpdate(data) {
  return request({
    url: '/api/cpes/ratio/saveOrUpdate',
    method: 'post',
    data: data
  })
}


// 修改烧结物料配比主
export function updateRatio(data) {
  return request({
    url: '/api/cpes/ratio',
    method: 'put',
    data: data
  })
}

// 删除烧结物料配比主
export function delRatio(mateRatioMasterId) {
  return request({
    url: '/api/cpes/ratio/' + mateRatioMasterId,
    method: 'delete'
  })
}

export function queryLastEndTime(prodcenterCode) {
  return request({
    url: '/api/cpes/ratio/queryLastEndTime/' + prodcenterCode,
    method: 'get'
  })
}
