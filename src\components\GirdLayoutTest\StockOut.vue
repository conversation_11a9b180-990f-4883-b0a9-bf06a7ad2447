<template>
  <!-- 出/入库  -->
  <el-dialog :title="title" width="500px" append-to-body :visible.sync="dialogVisible" :show-close="false" v-dialogDrag :close-on-click-modal="false"
             :modal="false">
    <el-form ref="form" :model="form" label-width="80px">
      <el-form-item label="入库物料" prop="mateCode">
        <el-input v-model="form.mateCode" type="text" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item label="料条" prop="crossRegion">
        <el-input v-model="form.crossRegion" type="text" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item label="垛位" prop="stackingPosition">
        <el-input v-model="form.stackingPosition" type="text" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item label="入库重量" prop="weight">
        <el-input-number v-model="form.weight" :precision="4" :step="1" :min="0" style="width: 100%;"></el-input-number>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  name: 'StockOut',
  props: {
    open: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      // 提交表单
      form: {
        storehouseCode: '',
        mateCode: '',
        crossRegion: '',
        stackingPosition: '',
        weight: 0,
      },
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.open;
      },
      set(val) {
        this.$emit('update', val);
      }
    }
  },
  methods: {
    cancel() {
      this.reset()
      this.$emit('on-cancel');
    },
    reset() {
      this.form = {
        storehouseCode: '',
        mateCode: '',
        crossRegion: '',
        stackingPosition: '',
        weight: 0,
      }
    },
    submitForm() {
      this.$emit('on-confirm');
      if (this.title === '入库') {
        console.log('入库');
        this.reset()
        console.log(this.form);
      }
      if (this.title === '出库') {
        console.log('出库');
        this.reset()
        console.log(this.form);
      }
    },

  },
  created() {
  },
  mounted() {
  },
}
</script>