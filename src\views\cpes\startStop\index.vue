<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="开始时间">
        <el-date-picker v-model="dateRange" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期"
                        type="datetimerange"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="时间班组" prop="workClsass">
        <BWorkShiftSelect v-model="queryParams.workClsass" model-name="烧结排班"/>
      </el-form-item>
      <el-form-item label="管理班组" prop="workGroup">
        <BWorkClassSelect v-model="queryParams.workGroup" model-name="烧结排班"/>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          @click="handleUpdate"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleDelete"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="queryList"></right-toolbar>
    </el-row>
    <div class="tableInfo">
      <vxe-grid
        ref="tableMainRef"
        @cell-click="handleCellClick"
        :column-config="{resizable: true}"
        v-bind="mainTable.gridOptions"
      >
      </vxe-grid>
    </div>
    <el-tabs type="border-card" style="margin-top: 10px;">
      <el-tab-pane label="用户管理">
        <template #label>
          {{ mainTable.currentWorkDate }}-{{ mainTable.currentWorkClsass }}
        </template>
        <div class="tableInfo">
          <vxe-table
            ref="tableDetailRef"
            :data="detilTable.tableData"
            :row-config="{isHover: true}"
            :column-config="{resizable: true}"
            border
            header-align="center"
            :height="tableHeight-350-10"
            stripe
          >
            <vxe-column field="contentType" title="内容类型" width="auto"></vxe-column>
            <vxe-column field="beginTime" title="班组开始时间" width="auto"></vxe-column>
            <vxe-column field="endTime" title="班组结束时间" width="auto"></vxe-column>
            <vxe-column field="clsssDuration" title="班组时长" width="auto"></vxe-column>
            <vxe-column field="stopReason" title="停机原因" width="auto"></vxe-column>
            <vxe-column field="personCharge" title="责任人" width="auto"></vxe-column>
            <vxe-column field="updateBy" title="修改者" width="auto"></vxe-column>
            <vxe-column field="updateTime" title="修改时间" width="auto"></vxe-column>
            <vxe-column field="createBy" title="创建者" :visible="false" width="auto"></vxe-column>
            <vxe-column field="createTime" title="创建时间" :visible="false" width="auto"></vxe-column>
            <vxe-column field="detailId" title="明细ID" :visible="false" width="auto"></vxe-column>
            <vxe-column field="startStopId" title="启停ID" :visible="false" width="auto"></vxe-column>

          </vxe-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加或修改启停机管理对话框 -->
    <el-dialog :title="editDialog.title" :visible.sync="editDialog.open" append-to-body width="600">

      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="记账日期" prop="workDate">
              <el-date-picker v-model="form.workDate"
                              clearable
                              placeholder="请选择记账日期"
                              type="date"
                              value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="管理班组" prop="workGroup">
              <BWorkClassSelect v-model="form.workGroup" model-name="烧结排班"/>

            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="时间班组" prop="workClsass">
              <BWorkShiftSelect v-model="form.workClsass" model-name="烧结排班"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-tabs type="border-card">
          <el-tab-pane label="停机时间清清单">
            <el-row>
              <el-col :span="24">
                <el-button-group style="float: right; margin-right: 10px;">
                  <el-button icon="el-icon-plus" size="small" type="success" @click="editAddHandler">新增</el-button>
                  <el-button icon="el-icon-delete" size="small" type="danger" @click="editDeleteHandler">删除
                  </el-button>
                </el-button-group>
              </el-col>
            </el-row>

            <el-row style="margin-top: 10px">
              <el-col :span="24">
                <vxe-table
                  ref="editTableef"
                  :column-config="{resizable: true}"
                  :data="form.details"
                  :height="500"
                  :row-config="{isHover: true}"
                  border
                  header-align="center"

                  stripe
                >
                  <vxe-column align="center" type="checkbox" width="10%"></vxe-column>
                  <vxe-column field="beginTime" title="开始时间-结束时间" width="200">
                    <template #default="{ row }">
                      <el-date-picker
                        size="small"
                        v-model="row.beginTime"
                        style="width: 100%"
                        type="datetime"
                        @change="(value, customParam) => handleTimeChange(value, row)"
                        placeholder="开始时间"
                      >
                      </el-date-picker>
                      <el-date-picker
                        size="small"
                        type="datetime"
                        v-model="row.endTime"
                        style="width: 100%; margin-top: 10px"
                        placeholder="结束时间"
                        @change="(value, customParam) => handleTimeChange(value, row)"
                      >
                      </el-date-picker>
                    </template>
                  </vxe-column>

                  <vxe-column field="clsssDuration" title="停机时长" width="100">
                    <template #default="{ row }">
                      {{ row.clsssDuration| formatNumber(3) }}
                    </template>
                  </vxe-column>

                  <vxe-column field="stopReason" title="停机原因" width="300">
                    <template #default="{ row }">
                      <el-input size="small" v-model="row.stopReason" placeholder="请输入 停机原因"></el-input>
                    </template>
                  </vxe-column>

                  <vxe-column field="" title="责任人" width="200">
                    <template #default="{ row }">
                      <el-input size="small" v-model="row.personCharge" placeholder="请输入 责任人"></el-input>
                    </template>
                  </vxe-column>
                </vxe-table>
              </el-col>
            </el-row>


          </el-tab-pane>

        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryforManger, saveOrUpdate, getEdit, deleteByMoPar
} from '@/api/cpes/startStop'
import {
  queryForManger as detialQueryForManger

} from '@/api/cpes/startStopDetail'
import BWorkShiftSelect from '@/components/BWorkShiftSelect/index.vue'
import BWorkClassSelect from '@/components/BWorkClassSelect/index.vue'
import dayjs from 'dayjs'
import XEUtils from 'xe-utils'
import BMateSelect from '@/components/BMateSelect/index.vue'

export default {
  name: 'StartStop',
  components: { BMateSelect, BWorkClassSelect, BWorkShiftSelect },
  data() {
    return {
      tableHeight: 300,
      dateRange: [],
      mainTable: {
        currentWorkDate: null,
        currentWorkClsass: null,
        loading: true,
        single: true,
        multiple: true,
        selectId: [],
        gridOptions: {
          border: true,
          stripe: false,
          loading: false,
          height: 350,
          columnConfig: {
            resizable: true
          },
          cellStyle: this.tableCellStyle,
          rowConfig: {
            isHover: true
          },
          radioConfig: {
            labelField: 'name',
            trigger: 'row'
          },
          columns: [
            { type: 'checkbox', width: 60 }
          ],
          data: [],
          startStopIds: []
        }
      },
      detilTable: {
        loading: true,
        single: true,
        multiple: true,
        tableData: [],
        selectId: [],
        pageConfig: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页显示条目个数
          total: 0, // 总数
          background: true, // 是否展示分页器背景色
          pageSizes: [10, 20, 50, 100]// 分页器分页待选项
        }
      },
      showSearch: true,
      editDialog: {
        title: '',
        // 是否显示弹出层
        open: false
      },
      // 查询参数
      queryParams: {
        operaType: null,
        prodCenterCode: null,
        dtStart: null,
        dtEnd: null,
        workClsass: null,
        workGroup: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        operaType: [
          {
            required: true, message: '业务类型;烧结机、混料不能为空', trigger: 'change'
          }
        ],
        workDate: [
          {
            required: true, message: '记账日期不能为空', trigger: 'blur'
          }
        ],
        prodCenterCode: [
          {
            required: true, message: '加工中心编码不能为空', trigger: 'blur'
          }
        ],
        prodCenterName: [
          {
            required: true, message: '加工中心名称不能为空', trigger: 'blur'
          }
        ],
        workClsass: [
          {
            required: true, message: '时间班组不能为空', trigger: 'blur'
          }
        ],
        workGroup: [
          {
            required: true, message: '管理班组不能为空', trigger: 'blur'
          }
        ]
      }
    }
  },
  created() {
    this.dateRange.push(dayjs(new Date()).startOf('date'))
    this.dateRange.push(dayjs(new Date()).endOf('date'))
    this.queryList()
  },
  methods: {

    queryList() {
      if (this.dateRange.length === 2) {
        this.queryParams.dtStart = dayjs(this.dateRange[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.queryParams.dtEnd = dayjs(this.dateRange[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      }
      this.queryParams.prodCenterCode = this.getProdCenterCode()
      this.queryParams.operaType = this.getOperaType()

      this.mainTable.gridOptions.columns = []
      this.mainTable.gridOptions.data = []
      this.mainTable.gridOptions.ratioMasterIds = []
      this.detilTable.tableData = []
      this.mainTable.currentWorkDate = ''
      this.mainTable.currentWorkClsass = ''
      queryforManger(this.queryParams).then(response => {

        response.data.col.forEach((item) => {
          if (item.value == '记账日期') {
            this.mainTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              fixed: 'left',
              width: 'auto',
              formatter({ cellValue }) {
                return XEUtils.toDateString(cellValue, 'yyyy-MM-dd')
              }
            })
          } else {
            this.mainTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              width: 'auto'
            })
          }
        })

        this.mainTable.gridOptions.data = response.data.rec
      })
    },
    // 取消按钮
    cancel() {
      this.editDialog.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        startStopId: null,
        operaType: null,
        workDate: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null,
        details: []
      }
      this.resetForm('form')
    },

    handleCellClick(event) {

      var workClsass = null
      const row = event.row
      var fieldName = event.column.field.toLowerCase()
      if (fieldName !== 'start_duration'
        && fieldName !== 'stop_duration'
        && (fieldName.indexOf('stop_') !== -1 || fieldName.indexOf('start_') !== -1)) {
        workClsass = fieldName.replace('start_', '').replace('stop_', '')
      }
      var workDate = row['WORK_DATE']
      var querypar = {}
      querypar.prodCenterCode = this.getProdCenterCode()
      querypar.operaType = this.getOperaType()
      querypar.workClsass = workClsass
      querypar.workDate = workDate
      // querypar.contentType = '停机'
      this.mainTable.currentWorkDate = workDate
      this.mainTable.currentWorkClsass = workClsass
      detialQueryForManger(querypar).then(response => {
        this.detilTable.tableData = response.data
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryList()
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.form.workDate = dayjs(new Date()).format('YYYY-MM-DD')
      this.editDialog.open = true
      this.editDialog.title = '添加启停机管理'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      if (this.mainTable.currentWorkClsass == null
        || this.mainTable.currentWorkClsass === ''
        || this.mainTable.currentWorkClsass == null
        || this.mainTable.currentWorkClsass === '') {
        this.$message.warning('请先选择一条数据')
        return
      }
      this.reset()
      var querypar = {}
      querypar.prodCenterCode = this.getProdCenterCode()
      querypar.operaType = this.getOperaType()
      querypar.workClsass = this.mainTable.currentWorkClsass
      querypar.workDate = this.mainTable.currentWorkDate
      getEdit(querypar).then(response => {
        this.form = response.data
        this.editDialog.open = true
        this.editDialog.title = '修改启停机管理'
      })

    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          var saveObject = JSON.parse(JSON.stringify(this.form))
          saveObject.operaType = this.getOperaType()
          saveObject.prodCenterCode = this.getProdCenterCode()
          saveObject.workModelId = this.getWorkModelId()
          saveObject.details.forEach(item => {
            item.beginTime = dayjs(item.beginTime).format('YYYY-MM-DD HH:mm:ss')
            item.endTime = dayjs(item.endTime).format('YYYY-MM-DD HH:mm:ss')
          })
          saveOrUpdate(saveObject).then(response => {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.handleQuery()
            this.editDialog.open = false
            this.reset()
          })

        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (this.mainTable.currentWorkClsass == null
        || this.mainTable.currentWorkClsass === ''
        || this.mainTable.currentWorkClsass == null
        || this.mainTable.currentWorkClsass === '') {
        this.$message.warning('请先选择一条数据')
        return
      }
      var querypar = {}
      querypar.prodCenterCode = this.getProdCenterCode()
      querypar.operaType = this.getOperaType()
      querypar.workClsass = this.mainTable.currentWorkClsass
      querypar.workDate = this.mainTable.currentWorkDate

      this.$modal
        .confirm('是否确认删除？')
        .then(function() {
          return deleteByMoPar(querypar)
        })
        .then(() => {
          this.handleQuery()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {
        })
    },

    /** 导出按钮操作 */
    handleExport() {
      const $table = this.$refs.tableMainRef
      if ($table) {
        $table.exportData({
          type: 'csv',
          filename: '启停机', // 文件名
          sheetName: 'Sheet1' // sheet名称
        })
      }
    },
    editAddHandler() {
      this.form.details.push({
        detailId: -1 * new Date().getTime() / 1000,
        beginTime: null,
        endTime: null,
        clsssDuration: null,
        stopReason: null,
        mateCode: null
      })
    },
    editDeleteHandler() {
      const selectedRows = this.$refs.editTableef.getCheckboxRecords()
      if (selectedRows == null || selectedRows.length == 0) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
        return
      }
      selectedRows.forEach(item => {
        this.form.details.splice(this.form.details.indexOf(item), 1)
      })
    },

    handleTimeChange(value, customParam) {
      customParam.clsssDuration = null
      if (customParam.beginTime && customParam.endTime) {
        const beginTime = dayjs(customParam.beginTime)
        const endTime = dayjs(customParam.endTime)
        // 计算两个时间的差值，单位为毫秒
        const diffInMilliseconds = endTime.diff(beginTime, 'millisecond')
        // 将毫秒转换为分钟，保留秒的精度
        const durationInMinutes = diffInMilliseconds / (1000 * 60)
        customParam.clsssDuration = durationInMinutes
      }
    },
    getProdCenterCode() {
      return this.$route.query.prodCenterCode
    },
    getOperaType() {
      return this.$route.query.operaType
    },
    getWorkModelId() {
      return this.$route.query.workModelId
    }
  },
  filters: {
    formatNumber(value, decimals) {
      if (!value) return 0
      return parseFloat(value).toFixed(decimals)
    }
  },
  mounted() {
    this.$nextTick(() => {
      let topValue = document.getElementsByClassName('tableInfo')[0].getBoundingClientRect().top
      this.tableHeight = document.body.clientHeight - topValue - 10

    })
  }
}
</script>
