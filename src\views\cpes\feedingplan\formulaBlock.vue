<template>
  <div class="app-container">
    <div class="upper-section">
      <div class="table-container">
        <div class="table-title">物料数据</div>
        <el-table
          :data="tableData"
          border
          style="width: 100%"
          :header-cell-style="{background:'#f5f7fa', color:'#606266'}"
          size="mini"
          :cell-style="{padding: '15px 0'}"
          v-loading="loading"
          element-loading-text="加载中..."
        >
          <!-- 第一列固定，显示数据类型（计划总量/剩余量） -->
          <el-table-column
            prop="dataType"
            label="数据类型"
            width="100"
            fixed="left"
          ></el-table-column>

          <!-- 动态生成物料名称 -->
          <el-table-column
            v-for="material in materialList"
            :key="material"
            :prop="material"
            :label="material"
            width="100"
            align="center"
            :show-overflow-tooltip="true"
          ></el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 下部分：配料单 -->
    <div class="lower-section">
      <div class="header-section">
        <div class="table-title">配料单 <span class="subtitle">长300米（万）</span></div>
        <div class="action-buttons">
          <el-input
            v-model="splitBlockCount"
            placeholder="请输入拆分数量"
            size="mini"
            style="width: 120px; margin-right: 10px;"
            type="number"
            :min="1"
          ></el-input>
          <el-button
            type="primary"
            size="mini"
            @click="handleSplitBlock"
            :loading="splitLoading"
            :disabled="!planid || !splitBlockCount || splitBlockCount < 1"
          >
            拆分
          </el-button>
          <el-button
            type="warning"
            size="mini"
            @click="handleToggleEdit"
            :disabled="!planid"
            style="margin-left: 10px;"
          >
            {{ isEditMode ? '取消修改' : '修改' }}
          </el-button>
          <el-button
            type="success"
            size="mini"
            @click="handleSave"
            :loading="saveLoading"
            :disabled="!planid"
            style="margin-left: 10px;"
          >
            保存
          </el-button>
        </div>
      </div>

      <div class="block-tables-container">
        <block-table
          v-for="(block, index) in blocks"
          :key="index"
          :planid="planid"
          :block-type="block.type"
          :edit-mode="isEditMode"
          @data-change="handleBlockDataChange"
        ></block-table>
      </div>
    </div>
  </div>
</template>

<script>
import BlockTable from './blockTable.vue'
import { getBlockStats, getBlockInfo, splitBlock, saveBlockInfo } from "@/api/feedingplan/feedingplan";

export default {
  name: "FormulaBlock",
  components: {
    BlockTable
  },
  props: {
    planid: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      // 物料数据
      materialData: {
        headerinfo: [], // 物料名称信息
        data: [] // 计划总量和剩余量数据
      },
      // 加载状态
      loading: false,
      // block信息数据
      blockInfo: {},
      // block数据（动态生成）
      blocks: [],
      // 拆分相关数据
      splitBlockCount: null, // 拆分数量
      splitLoading: false, // 拆分加载状态
      saveLoading: false, // 保存加载状态
      // 编辑模式状态
      isEditMode: false // 是否处于编辑模式
    };
  },
  computed: {
    // 获取所有物料名称列表
    materialList() {
      // 没有headerinfo数据，返回空数组
      if (!this.materialData.headerinfo || this.materialData.headerinfo.length === 0) {
        return [];
      }

      const materialNames = [];
      this.materialData.headerinfo.forEach(item => {
        const key = Object.keys(item)[0];
        if (key) {
          materialNames.push(item[key]);
        }
      });

      return materialNames;
    },

    // 获取物料编码列表
    materialCodes() {
      // 没有headerinfo数据，返回空数组
      if (!this.materialData.headerinfo || this.materialData.headerinfo.length === 0) {
        return [];
      }

      // 从headerinfo中提取物料编码
      const materialCodes = [];
      this.materialData.headerinfo.forEach(item => {
        const key = Object.keys(item)[0];
        if (key) {
          materialCodes.push(key);
        }
      });

      return materialCodes;
    },

    // 生成表格数据
    tableData() {
      // 如果没有data数据，返回空数组
      if (!this.materialData.data || this.materialData.data.length < 2) {
        return [];
      }
      // 创建表格行数据
      const planTotalRow = { dataType: '计划总量' };
      const remainingRow = { dataType: '剩余量' };

      // 获取计划总量和剩余量数据
      const planTotalData = this.materialData.data[0] || {};
      const remainingData = this.materialData.data[1] || {};

      // 为每个物料添加数量
      this.materialCodes.forEach((code, index) => {
        const materialName = this.materialList[index];
        planTotalRow[materialName] = planTotalData[code] || 0;
        remainingRow[materialName] = remainingData[code] || 0;
      });

      // 返回表格数据
      return [planTotalRow, remainingRow];
    }
  },
  mounted() {
    // 组件挂载后的操作
    this.fetchData();
  },
  watch: {
    // 监听planid变化，当planid变化时重新获取数据
    planid: {
      handler(newVal, oldVal) {
        console.log('planid变化:', oldVal, '->', newVal);
        if (newVal && newVal !== oldVal) {
          this.fetchData();
        }
      },
      immediate: true // 组件创建时立即执行一次
    }
  },
  methods: {
    // 获取数据
    fetchData() {
      if (!this.planid) {
        return;
      }

      this.fetchMaterialData();
      this.fetchBlockData();
    },

    // 单独获取物料数据
    fetchMaterialData() {
      this.loading = true;
      getBlockStats(this.planid)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.materialData = response.data;
          } else {
            this.materialData = {
              headerinfo: [],
              data: []
            };
          }
        })
        .catch(error => {
          this.materialData = {
            headerinfo: [],
            data: []
          };
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 单独获取Block数据
    fetchBlockData() {

      this.loading = true;

      getBlockInfo(this.planid)
        .then(response => {

          if (response.code === 200 && response.data) {
            this.blockInfo = response.data;
            this.generateBlocks();
          } else {
            this.blockInfo = {};
            this.blocks = [];
          }
        })
        .catch(error => {
          this.blockInfo = {};
          this.blocks = [];
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 根据blockInfo生成blocks数组
    generateBlocks() {
      this.blocks = [];

      // 遍历blockInfo对象的所有key
      Object.keys(this.blockInfo).forEach((key, index) => {
        // 只处理有数据的block
        if (Array.isArray(this.blockInfo[key]) && this.blockInfo[key].length > 0) {
          this.blocks.push({
            id: index + 1,
            name: key.toUpperCase(),
            type: key
          });
        }
      });

    },

    // 刷新数据
    refreshData() {
      this.fetchData();
    },

    // 单独刷新物料数据
    refreshMaterialData() {

      this.fetchMaterialData();
    },

    // 单独刷新Block数据
    refreshBlockData() {
      this.fetchBlockData();
    },

    // 处理拆分block
    handleSplitBlock() {
      if (!this.planid) {
        this.$message.warning('未提供planid，无法进行拆分操作');
        return;
      }

      if (!this.splitBlockCount || this.splitBlockCount < 1) {
        this.$message.warning('请输入有效的拆分数量');
        return;
      }

      this.splitLoading = true;

      // 调用拆分接口
      splitBlock(this.planid, this.splitBlockCount)
        .then(response => {
          console.log('拆分接口响应:', response);
          if (response.code === 200) {
            this.$message.success('拆分操作成功');

            // 清空输入框
            this.splitBlockCount = null;

            // 拆分成功后只重新获取block信息，不影响物料数据
            this.fetchBlockData();

            // 同时刷新所有blockTable的料种数据
            this.$nextTick(() => {
              this.refreshAllMaterialOptions();
            });
          } else {
            throw new Error(response.msg || '拆分操作失败');
          }
        })
        .catch(error => {
          console.error('拆分操作出错:', error);
          this.$message.error(error.message || '拆分操作失败，请稍后重试');
        })
        .finally(() => {
          this.splitLoading = false;
        });
    },

    // 处理保存
    handleSave() {
      if (!this.planid) {
        this.$message.warning('未提供planid，无法进行保存操作');
        return;
      }

      this.saveLoading = true;

      // 直接使用blockInfo数据，格式与获取时一致
      const saveData = {
        data: this.blockInfo
      };

      // 调用保存接口
      saveBlockInfo(this.planid, saveData)
        .then(response => {
          if (response.code === 200) {
            this.$message.success('保存成功');
          } else {
            this.$message.error(response.msg || '保存失败');
          }
        })
        .catch(error => {
          this.$message.error('保存失败，请稍后重试');
        })
        .finally(() => {
          this.saveLoading = false;
        });
    },

    // 切换编辑模式
    handleToggleEdit() {
      this.isEditMode = !this.isEditMode;

      if (this.isEditMode) {
        this.$message.success('已进入编辑模式，可以修改BLOCK表格数据');
      } else {
        this.$message.info('已退出编辑模式');
      }
    },

    // 处理子组件数据变化，实时更新blockInfo
    handleBlockDataChange(payload) {
      const { blockType, data } = payload;

      // 更新blockInfo中对应的数据
      this.$set(this.blockInfo, blockType, data);
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 10px;
  background-color: #f5f7fa;
  width: 100%;
  box-sizing: border-box;
}

.upper-section {
  margin-bottom: 15px;
}

.lower-section {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 0;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  margin-top: 5px;
}

.main-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 10px;
  font-weight: normal;
  color: #606266;
}

.block-tables-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start; /* 改为从左开始排列 */
  align-items: flex-start;
  width: 100%;
  margin-top: 15px;
  gap: 20px; /* 增加间距，使表格之间有更明显的分隔 */
  text-align: center;
}

.table-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 10px;
  margin-bottom: 15px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.table-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin: 0;
  padding-left: 5px;
  border-left: 4px solid #409EFF;
}

.action-buttons {
  display: flex;
  align-items: center;
}

/* 表格样式优化 */
/deep/ .el-table th {
  background-color: #f5f7fa;
  padding: 8px 0;
  font-weight: bold;
  height: 40px;
}

/deep/ .el-table td {
  padding: 5px 0;
  height: 36px;
}

/deep/ .el-table--border {
  border-radius: 4px;
  overflow: hidden;
}

/* 紧凑型表格 */
/deep/ .el-table.el-table--mini td,
/deep/ .el-table.el-table--mini th {
  padding: 4px 0;
}

/* 固定表头和滚动条样式 */
/deep/ .el-table__body-wrapper {
  overflow-y: auto;
  overflow-x: auto;
}

/deep/ .el-table__fixed-header-wrapper,
/deep/ .el-table__fixed-body-wrapper {
  background-color: #f5f7fa;
}

/* 强调数据类型列 */
/deep/ .el-table__row td:first-child {
  font-weight: bold;
  background-color: #f5f7fa;
}

/* 美化滚动条 */
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  border-radius: 4px;
}

/deep/ .el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}

/* 表格单元格样式 */
/deep/ .el-table__row td {
  text-align: center;
}

/* 表格行高亮 */
/deep/ .el-table__row:hover td {
  background-color: #f0f9eb;
}

/* 表格列宽自适应 */
/deep/ .el-table__header th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  text-align: center;
}
</style>