
// echart图表 数据模板
export function lineEchartsComm(query) {
  query = [
    // 曲线图
    {
      businessCode: "INDICATIONELEMENT",
      description: "高炉技经指标",
      echarType:"曲线图",
      X_line_axis: [
        {
          name: "workDate",
          tableField: "workDate"
        }
      ],
      Y_line_axis: [
        {
          name: "理论产量",
          tableField: "theoreticalProduction",
          fileIndex: [],
          tableFieldFirst:"production", // 一级表头使用的
        },
        {
          name: "实际产量",
          tableField: "actualProduction",
          fileIndex2: [],
          tableFieldFirst:"production", // 一级表头使用的
        },
        {
          name: "实际一级品率",
          tableField: "actualFirstProduction",
          tableFieldFirst:"firstProduction", // 一级表头使用的
        },
        {
          name: "理论燃料比",
          tableField: "theoreticalFuel",
          tableFieldFirst:"fuel", // 一级表头使用的
        },
        {
          name: "实际燃料比",
          tableField: "actualFuel",
          tableFieldFirst:"fuel", // 一级表头使用的
        },
        {
          name: "理论焦比",
          tableField: "theoreticalCoke",
          tableFieldFirst:"coke", // 一级表头使用的
        },
        {
          name: "实际焦比",
          tableField: "actualCoke",
          tableFieldFirst:"coke", // 一级表头使用的
        },
        {
          name: "理论煤比",
          tableField: "theoreticalCoal",
          tableFieldFirst:"coal", // 一级表头使用的
        },
        {
          name: "实际煤比",
          tableField: "actualCoal",
          tableFieldFirst:"coal", // 一级表头使用的
        },
        {
          name: "理论焦丁比",
          tableField: "theoreticalNutCoke",
          tableFieldFirst:"nutCoke", // 一级表头使用的
        },
        {
          name: "实际焦丁比",
          tableField: "actualNutCoke",
          tableFieldFirst:"nutCoke", // 一级表头使用的
        },
        {
          name: "理论系数值",
          tableField: "theoreticalCoefficient",
          tableFieldFirst:"coefficient", // 一级表头使用的
        },
        {
          name: "实际系数值",
          tableField: "actualCoefficient",
          tableFieldFirst:"coefficient", // 一级表头使用的
        },
        {
          name: "理论冶强值",
          tableField: "theoreticalSmeltStrength",
          tableFieldFirst:"smeltStrength", // 一级表头使用的
        },
        {
          name: "实际冶强值",
          tableField: "actualSmeltStrength",
          tableFieldFirst:"smeltStrength", // 一级表头使用的
        },
        {
          name: "煤气回收",
          tableField: "gasRecovery",
          tableFieldFirst:"gas", // 一级表头使用的
        },
        {
          name: "吨铁煤气消耗",
          tableField: "tonIronGasConsumption",
          tableFieldFirst:"gasConsumption", // 一级表头使用的
        },
        {
          name: "吨铁煤气回收",
          tableField: "tonIronGasRecovery",
          tableFieldFirst:"tonGasRecovery", // 一级表头使用的
        },
        {
          name: "矿耗",
          tableField: "oreConsumption",
          tableFieldFirst:"ore", // 一级表头使用的
        },
        {
          name: "富氧率",
          tableField: "oxyRate",
          tableFieldFirst:"oxy", // 一级表头使用的
        },
        {
          name: "Zn负荷",
          tableField: "znLoad",
          tableFieldFirst:"zn", // 一级表头使用的
        },
        {
          name: "碱负荷",
          tableField: "alkaliLoad",
          tableFieldFirst:"alkali", // 一级表头使用的
        },
      ],
    }
  ]
  return query;
}


