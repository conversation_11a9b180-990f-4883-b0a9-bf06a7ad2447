<template>
    <div>
        <vxe-grid v-bind="gridOptions" style="margin: 10px;">
            <template #form>
                <vxe-form ref="formRef" v-bind="formOptions">
                    <template #action>
                        <vxe-button status="primary" @click="searchEvent">搜索</vxe-button>
                        <vxe-button @click="resetEvent">重置</vxe-button>
                    </template>
                </vxe-form>
            </template>
        </vxe-grid>
    </div>
</template>

<script>
import { selectStockRealData } from "@/api/wms/stockreal";

export default {
    name: 'StockSnapshot',
    data() {
        return {
            gridOptions: {
                columns: [],
                data: [],

                height: 800,
                border: true,
                stripe: true,
                align: 'center',

                columnConfig: {
                    resizable: true
                },
                rowConfig: {
                    isHover: true,
                    isCurrent: true,
                },

                editConfig: {
                    trigger: 'dblclick',
                    mode: 'cell',
                },

                // mergeCells: [
                //     // { row: 0, col: 1, rowspan: 3 },
                //     // { row: 3, col: 1, rowspan: 3 },
                // ],


                toolbarConfig: {
                    custom: true,
                    zoom: true,
                    // buttons: [
                    //     { name: '生成快照', code: 'myAdd', status: 'primary' },
                    // ]
                },
                customConfig: {
                    immediate: true
                },
            },
            formOptions: {
                data: {
                    beginTime: '2025-04-12 00:00:00',
                    endTime: '2025-04-12 23:59:59',
                    mateCode: '',
                    mateName: '',
                },
                items: [
                    { field: 'beginTime', title: '开始时间', itemRender: { name: 'VxeDatePicker', props: { type: 'datetime' } } },
                    { field: 'endTime', title: '结束时间', itemRender: { name: 'VxeDatePicker', props: { type: 'datetime' } } },
                    { field: 'mateCode', title: '物料编码', itemRender: { name: 'VxeInput' } },
                    { field: 'mateName', title: '物料名称', itemRender: { name: 'VxeInput' } },
                    { slots: { default: 'action' } }
                ]
            },
        }
    },

    mounted() {
        this.initGridData();
    },

    methods: {
        initGridData() {
            this.gridOptions.columns = [
                { type: 'seq', width: 50 },
                { field: 'crossRegion', title: '料条', },
                { field: 'stackingPosition', title: '垛位', },
                { field: 'mateCode', title: '物料编码', },
                { field: 'mateName', title: '物料名称', },
                { field: 'specCode', title: '规格型号', },
                { field: '', title: '期初量', },
                { field: 'stockWeight', title: '库存重量', editRender: { name: 'VxeNumberInput', autoSelect: true } },
                { field: 'carsWeight', title: '入库重量', },
                { field: '', title: '出库重量', },
            ]

            this.queryList()

        },

        async searchEvent() {
            this.queryList()
        },

        resetEvent() {
            const $form = this.$refs.formRef
            if ($form) {
                $form.reset()
                this.queryList()
            }
        },

        async queryList() {
            selectStockRealData(this.formOptions.data).then(response => {
                this.gridOptions.data = response.data;
            })
        },



    }
}
</script>