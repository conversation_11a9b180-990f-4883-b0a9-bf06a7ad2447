<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="物料编码" prop="materialNumber">
        <el-input v-model="queryParams.materialNumber" placeholder="请输入编码" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="数采编码111" prop="materialNumberDatacollect">
        <el-input v-model="queryParams.materialNumberDatacollect" placeholder="请输入数采编码" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="名称" prop="materialName">
        <el-input v-model="queryParams.materialName" placeholder="请输入名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-col :span="4" style="padding-right: 10px">
        <el-tree class="tree-border" :data="materialCategoryList" ref="categoryTree" default-expand-all node-key="id"
          empty-text="加载中，请稍候" :props="defaultProps" @node-click="categoryTreeNodeClick"></el-tree>
      </el-col>

      <el-col :span="20">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
              @click="handleUpdate">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
              @click="handleDelete">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport">导入</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <el-table v-loading="loading" :data="materialList" @selection-change="handleSelectionChange" border
          style="width: 100%;">
          <af-table-column type="selection" :fit="false" width="55" align="left" header-align="center" />
          <el-table-column label="分类" fixed="left" align="left" header-align="center"
            prop="tMdMaterialCategory.categoryName" />
          <af-table-column label="物料编码" fixed="left" align="left" header-align="center" prop="materialNumber" />

          <af-table-column label="物料名称" fixed="left" align="left" header-align="center" prop="materialName" />

          <af-table-column label="状态" align="left" header-align="center" prop="materialStatus">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.materialStatus == '生效'" type="success">{{
      scope.row.materialStatus
    }}</el-tag>
              <el-tag v-if="scope.row.materialStatus == '失效'" type="danger">{{
      scope.row.materialStatus
    }}</el-tag>
            </template>
          </af-table-column>
          <af-table-column label="数采编码111" align="left" header-align="center" prop="materialNumberDatacollect" />
          <af-table-column label="助记码111" align="left" header-align="center" prop="helpCode" />
          <af-table-column label="别名111" align="left" header-align="center" prop="aliasName" />
          <af-table-column label="简称" align="left" header-align="center" prop="shortName" />
          <af-table-column label="计量单位" align="left" header-align="center" prop="mdBaseUnit.unitName" />
          <af-table-column label="注册商标" align="left" header-align="center" prop="registeredMark" />
          <af-table-column label="批准文号" align="left" header-align="center" prop="warrantNumber" />
          <af-table-column label="启用批次" align="left" header-align="center" prop="isBatch" />
          <af-table-column label="净重" align="left" header-align="center" prop="grossWeight" />
          <af-table-column label="毛重" align="left" header-align="center" prop="netWeight" />
          <af-table-column label="重量单位" align="left" header-align="center" prop="mdWeightUnit.unitName" />
          <af-table-column label="参考价格" align="left" header-align="center" prop="proposedPrice" />
          <af-table-column label="ERP关联" align="left" header-align="center" prop="erpId" />
          <af-table-column label="规格型号" align="left" header-align="center" prop="specModel" />
          <af-table-column label="外文名称" align="left" header-align="center" prop="foreignName" />
          <af-table-column label="长度" align="left" header-align="center" prop="lengthValue" />
          <af-table-column label="长度单位" align="left" header-align="center" prop="mdLengthUnit.unitName" />
          <af-table-column label="宽度" align="left" header-align="center" prop="widthValue" />
          <af-table-column label="宽度单位" align="left" header-align="center" prop="mdWidthUnit.unitName" />
          <af-table-column label="高度" align="left" header-align="center" prop="heightValue" />
          <af-table-column label="高度单位" align="left" header-align="center" prop="mdHeightUnit.unitName" />
          <af-table-column label="体积" align="left" header-align="center" prop="volumeValue" />
          <af-table-column label="体积单位" align="left" header-align="center" prop="mdVolumnUnit.unitName" />
          <af-table-column label="备注" align="left" header-align="center" prop="remark" />
          <af-table-column label="操作" align="left" header-align="center" fixed="right" width="120"
            class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </af-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>

    <!-- 添加或修改物料管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="分类" prop="materialCategoryID">
              <treeselect v-model="form.materialCategoryID" :options="materialCategoryList" :show-count="true"
                placeholder="请选择归属分类" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="状态">
              <el-select v-model="form.materialStatus" placeholder="请选择状态">
                <el-option v-for="dict in dict.type.effective_or_not" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="批次管理">
              <el-select v-model="form.isBatch" placeholder="请选择批次">
                <el-option v-for="dict in isBatchList" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="编码" prop="materialNumber">
              <el-input v-model="form.materialNumber" placeholder="请输入编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数采编码" prop="materialNumberDatacollect">
              <el-input v-model="form.materialNumberDatacollect" placeholder="请输入数采编码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="名称" prop="materialName">
              <el-input v-model="form.materialName" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计量单位" prop="baseUnit">
              <el-select style="width: 100%;" filterable v-model="form.baseUnit" placeholder="请选择计量单位">
                <el-option v-for="item in allUnit" :key="item.unitNumber" :label="item.unitName"
                  :value="item.unitNumber">
                  <span style="float: left">{{ item.unitNumber }}-{{ item.unitName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{
      item.unitGropCode
    }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="ERP关联" prop="erpId">
              <el-input v-model="form.erpId" placeholder="请输入ERP关联" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="参考价格" prop="proposedPrice">
              <el-input-number v-model="form.proposedPrice" :precision="5" :step="0.1" :min="0"
                style="width: 100%;"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-tabs type="border-card">
          <el-tab-pane label="基础信息">
            <el-row>
              <el-col :span="12">
                <el-form-item label="简称" prop="shortName">
                  <el-input v-model="form.shortName" placeholder="请输入简称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="别名" prop="aliasName">
                  <el-input v-model="form.aliasName" placeholder="请输入别名" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="助记码" prop="helpCode">
                  <el-input v-model="form.helpCode" placeholder="请输入助记码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="外文名称" prop="foreignName">
                  <el-input v-model="form.foreignName" placeholder="请输入外文名称" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="批准文号" prop="warrantNumber">
                  <el-input v-model="form.warrantNumber" placeholder="请输入批准文号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册商标" prop="registeredMark">
                  <el-input v-model="form.registeredMark" placeholder="请输入注册商标" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="规格型号" prop="specModel">
                  <el-input v-model="form.specModel" placeholder="请输入规格型号" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="计量信息">
            <el-row>
              <el-col :span="12">
                <el-form-item label="毛重" prop="netWeight">
                  <el-input-number style="width: 249px" aria-placeholder="请输入毛重" v-model="form.netWeight" :precision="5"
                    :step="0.1" :min="0" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="净重" prop="grossWeight">
                  <el-input-number style="width: 249px" aria-placeholder="请输入毛重" v-model="form.grossWeight"
                    :precision="5" :step="0.1" :min="0" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="重量单位" prop="weightUnit">
                  <el-select style="width: 249px" filterable v-model="form.weightUnit" placeholder="请选择重量单位">
                    <el-option v-for="item in weightUnit" :key="item.unitNumber" :label="item.unitName"
                      :value="item.unitNumber">
                      <span style="float: left">{{ item.unitNumber }}-{{ item.unitName }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ item.unitGropCode }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="长度" prop="lengthValue">
                  <el-input-number style="width: 249px" aria-placeholder="请输入毛重" v-model="form.lengthValue"
                    :precision="5" :step="0.1" :min="0" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="长度单位" prop="lengthUnit">
                  <el-select style="width: 249px" filterable v-model="form.lengthUnit" placeholder="请选择长度单位">
                    <el-option v-for="item in lengthUnit" :key="item.unitNumber" :label="item.unitName"
                      :value="item.unitNumber">
                      <span style="float: left">{{ item.unitNumber }}-{{ item.unitName }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ item.unitGropCode }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="宽度" prop="widthValue">
                  <el-input-number style="width: 249px" aria-placeholder="请输入毛重" v-model="form.widthValue"
                    :precision="5" :step="0.1" :min="0" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="宽度单位" prop="widthUnit">
                  <el-select style="width: 249px" filterable v-model="form.widthUnit" placeholder="请选择长度单位">
                    <el-option v-for="item in lengthUnit" :key="item.unitNumber" :label="item.unitName"
                      :value="item.unitNumber">
                      <span style="float: left">{{ item.unitNumber }}-{{ item.unitName }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ item.unitGropCode }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="高度" prop="heightValue">
                  <el-input-number style="width: 249px" aria-placeholder="请输入毛重" v-model="form.heightValue"
                    :precision="5" :step="0.1" :min="0" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="高度单位" prop="heightUnit">
                  <el-select style="width: 249px" filterable v-model="form.heightUnit" placeholder="请选择高度单位">
                    <el-option v-for="item in lengthUnit" :key="item.unitNumber" :label="item.unitName"
                      :value="item.unitNumber">
                      <span style="float: left">{{ item.unitNumber }}-{{ item.unitName }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ item.unitGropCode }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="体积" prop="volumeValue">
                  <el-input-number style="width: 249px" aria-placeholder="请输入毛重" v-model="form.volumeValue"
                    :precision="5" :step="0.1" :min="0" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="体积单位" prop="volumnUnit">
                  <el-select style="width: 249px" filterable v-model="form.volumnUnit" placeholder="请选择体积单位">
                    <el-option v-for="item in volumnUnit" :key="item.unitNumber" :label="item.unitName"
                      :value="item.unitNumber">
                      <span style="float: left">{{ item.unitNumber }}-{{ item.unitName }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ item.unitGropCode }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
        <el-row style="margin-top: 10px">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已存在的物料
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryFormManger,
  getMaterial,
  delMaterial,
  saveOrUpdate,
  getSaveEntity,
} from "@/api/md/material";
import { treeselectByGroup } from "@/api/md/materialCategory";
import { queryByUnitGropCode } from "@/api/md/materialUnit";
import Treeselect from "@riophae/vue-treeselect";
import { getToken } from "@/utils/auth";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Material",
  dicts: ["effective_or_not", "material_tags"],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物料管理表格数据
      materialList: [],
      materialCategoryList: [],
      materialCategoryCurrent: {},
      allUnit: [],
      weightUnit: [],
      lengthUnit: [],
      volumnUnit: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialCategoryCode: null,
        materialNumber: null,
        materialNumberDatacollect: null,
        materialName: null,
      },
      isBatchList: [
        {
          value: '禁用批次',
          label: '禁用批次'
        }, {
          value: '启用批次',
          label: '启用批次'
        }
      ],
      defaultProps: {
        children: "children",
        label: "label",
      },
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",

        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/api/md/material/importData"
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        materialName: [
          { required: true, message: "名称不能为空", trigger: "blur" },
        ],
        materialCategoryID: [
          { required: true, message: "分类不能为空", trigger: "blur" },
        ],
        materialNumber: [
          { required: true, message: "编码不能为空", trigger: "blur" },
        ],
        materialCategoryCode: [
          { required: true, message: "分类不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    treeselectByGroup("苍穹").then((response) => {
      const result = [{ id: 0, label: '(全部)', children: response.data }];
      this.materialCategoryList = result;
    });
    queryByUnitGropCode("").then((response) => {
      this.allUnit = response.data;
      this.allUnit.forEach((element) => {
        const model = JSON.parse(JSON.stringify(element));
        if (model.unitGropCode === "重量组") {
          this.weightUnit.push(model);
        }
        if (model.unitGropCode === "长度组") {
          this.lengthUnit.push(model);
        }
        if (model.unitGropCode === "体积组") {
          this.volumnUnit.push(model);
        }
      });
    });
  },
  methods: {
    /** 查询物料管理列表 */
    getList() {
      this.loading = true;
      if (this.materialCategoryCurrent != null) {
        this.queryParams.materialCategoryID = this.materialCategoryCurrent.id;
      }
      queryFormManger(this.queryParams).then((response) => {
        this.materialList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        materialId: null,
        materialCategoryID: null,
        materialNumber: null,
        materialNumberDatacollect: null,
        materialName: null,
        materialStatus: "生效",
        erpId: null,
        helpCode: null,
        aliasName: null,
        shortName: null,
        foreignName: null,
        baseUnit: null,
        grossWeight: null,
        netWeight: null,
        proposedPrice: null,
        registeredMark: null,
        warrantNumber: null,
        specModel: null,
        lengthValue: null,
        widthValue: null,
        heightValue: null,
        volumeValue: null,
        weightUnit: null,
        lengthUnit: null,
        widthUnit: null,
        heightUnit: null,
        volumnUnit: null,
        remark: null,
        isBatch: this.isBatchList[0].value
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.materialId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加物料管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const materialId = row.materialId || this.ids;
      getSaveEntity(materialId).then((response) => {

        const obj = response.data;
        // if (obj.tags != null && obj.tags != "") {
        //   obj.tags = obj.tags.split(",");
        // }

        this.form = obj;
        this.open = true;
        this.title = "修改物料管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const obj = JSON.parse(JSON.stringify(this.form));
          // if (obj.tags != null) {
          //   obj.tags = obj.tags.join();
          // }
          saveOrUpdate(obj).then((response) => {
            this.$modal.msgSuccess("保存成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const materialIds = row.materialId || this.ids;
      this.$modal
        .confirm('是否确认删除物料？')
        .then(function () {
          return delMaterial(materialIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "/api/md/material/export",
        {
          ...this.queryParams,
        },
        `物料_${new Date().getTime()}.xlsx`
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "物料导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('/api/md/material/importTemplate', {
      }, `物料模板_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    categoryTreeNodeClick(data) {
      if (data.id != 0) {
        this.materialCategoryCurrent = data;
      } else {
        this.materialCategoryCurrent = {};
      }

      this.getList();
    },
  },
};
</script>

<style scoped>
/* 单元格回行设置 */
/deep/ .el-table .cell {
  white-space: nowrap;
  padding-left: 5px;
  padding-right: 5px;
}
</style>