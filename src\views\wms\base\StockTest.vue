<template>
    <div>
        <vxe-form v-bind="formOptions">
            <template #action>
                <vxe-button type="reset">重置</vxe-button>
                <vxe-button type="submit" status="primary">提交</vxe-button>
            </template>
        </vxe-form>
    </div>
</template>

<script>
export default {
    data() {

        const typeItemRender = {
            name: 'VxeSelect',
            options: [],
        }

        const sexItemRender = {
            name: 'VxeSelect',
            options: [],
            events: {
                change: this.change
            }
        }

        const formOptions = {
            titleWidth: 120,
            data: {
                name: 'test1',
                nickname: 'Testing',
                sex: '',
                sexList: [],
                type: '',
                typeList: []
            },
            items: [
                { field: 'name', title: '名称', span: 24, itemRender: { name: 'VxeInput' } },
                { field: 'sex', title: '下拉框', span: 24, itemRender: sexItemRender },
                { field: 'type', title: '类型', span: 24, itemRender: typeItemRender },

                { align: 'center', span: 24, slots: { default: 'action' } }
            ]
        }
        return {
            formOptions,

            sexItemRender,
            typeItemRender,
        }
    },
    methods: {
        change() {
            this.formOptions.data.type = 3
        }
    },

    created() {
        // 模拟后台接口
        setTimeout(() => {
            this.sexItemRender.options = [
                { label: '女', value: 'Women' },
                { label: '男', value: 'Man' }
            ]

            this.typeItemRender.options = [
                { label: '1', value: '1' },
                { label: '2', value: '2' },
                { label: '3', value: '3' },
            ]
        }, 200)
    }
}
</script>
