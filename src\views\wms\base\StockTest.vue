<template>
    <div>
        <vxe-grid v-bind="grid1Options">
            <template #expand_content="{ row }">
                <div class="expand-wrapper">
                    <vxe-grid v-bind="grid2Options" :data="row.childList"></vxe-grid>
                </div>
            </template>
        </vxe-grid>
    </div>
    <!-- <div class="wrap">
        <div class="wrapGrid1">
            <vxe-grid ref="grid1Ref" v-bind="grid1Options">
            </vxe-grid>
        </div>

        <div class="wrapGrid2">
            <vxe-grid ref="grid2Ref" v-bind="grid2Options">
            </vxe-grid>
        </div>
    </div> -->

    <!-- <div>
        <vxe-grid ref="grid1Ref" v-bind="grid1Options">
        </vxe-grid>
        <vxe-grid ref="grid2Ref" v-bind="grid2Options">
        </vxe-grid>
    </div> -->
</template>


<script>

export default {
    name: 'StockTest',
    data() {

        const grid1Options = {
            columns: [
                { field: 'seq', type: 'seq', width: 50 },
                { type: 'checkbox', width: 50 },
                { type: 'expand', width: 60, slots: { content: 'expand_content' } },
                { field: 'stackPlanId', visible: false },
                { field: 'storehouseCode', title: '原料场编码', visible: false },
                { field: 'storehouseName', title: '原料场名称', },
                { field: 'crossRegion', title: '料条', },
                { field: 'stackingPosition', title: '垛位', },
                { field: 'startPosition', title: '开始位置', },
                { field: 'endPosition', title: '结束位置', },
            ],
            data: [
                {
                    stackPlanId: 1001, storehouseCode: 'YLC001', storehouseName: '原料库1#料场', crossRegion: 'A', stackingPosition: 'A-1', startPosition: 200, endPosition: 300,
                    childList: [
                        { stackPlanId: '1001', mateCode: '1030100001', mateName: '干熄焦炭(捣固)' },
                    ]
                },
                { stackPlanId: 1002, storehouseCode: 'YLC001', storehouseName: '原料库1#料场', crossRegion: 'A', stackingPosition: 'A-2', startPosition: 200, endPosition: 300, },
                { stackPlanId: 1003, storehouseCode: 'YLC001', storehouseName: '原料库1#料场', crossRegion: 'A', stackingPosition: 'A-3', startPosition: 200, endPosition: 300, },
                { stackPlanId: 1004, storehouseCode: 'YLC001', storehouseName: '原料库1#料场', crossRegion: 'A', stackingPosition: 'A-4', startPosition: 200, endPosition: 300, },
            ],

            border: true,
            strip: true,
            align: 'center',
            showOverflow: true,
            keepSource: true,
            loading: false,

            columnConfig: {
                resizable: true
            },
            rowConfig: {
                isHover: true,
                isCurrent: true,
            },

            pagerConfig: {
                total: 0,
                currentPage: 1,
                pageSize: 10
            },
        }

        const grid2Options = {
            columns: [
                { field: 'stackPlanId', visible: false },
                { field: 'mateCode', title: '物料编码', },
                { field: 'mateName', title: '物料名称', },
            ],


            border: true,
            strip: true,
            align: 'center',
            showOverflow: true,
            keepSource: true,
            loading: false,

            columnConfig: {
                resizable: true
            },
            rowConfig: {
                isHover: true,
                isCurrent: true,
            },
        }

        return {
            grid1Options,
            grid2Options,
        }
    },
    methods: {

    },
    created() { },
    mounted() { },
    beforeDestroy() { },
}
</script>


<style scoped>
.wrap {
    display: flex;
    justify-content: stretch;
}

.wrapGrid1 {
    width: 80%;
}

.wrapGrid2 {
    width: 20%;
}
</style>