<template>
    <div class="app-container" style="padding: 10px;">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="存储编号" prop="storeCode">
                <el-select v-model="queryParams.storeCode" placeholder="请输入存储编号" clearable
                    @keyup.enter.native="handleQuery">
                    <el-option v-for="item in storeList" :key="item.storeCode" :label="item.storeName"
                        :value="item.storeCode">
                        <span style="float: left">{{ item.storeName }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.storeCode }}</span>
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="点位工序" prop="pointProcess">
                <el-select v-model="queryParams.pointProcess" placeholder="请输入点位业务" allow-create clearable filterable
                    @keyup.enter.native="handleQuery">
                    <el-option v-for="item in processList" :key="item" :label="item" :value="item">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="点位业务" prop="pointOperation">
                <el-select v-model="queryParams.pointOperation" placeholder="请输入点位工序" allow-create clearable filterable
                    @keyup.enter.native="handleQuery">
                    <el-option v-for="item in operList" :key="item" :label="item" :value="item">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="点位编号" prop="pointCode">
                <el-input v-model="queryParams.pointCode" placeholder="请输入点位编号" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>


            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询点位</el-button>
            </el-form-item>
        </el-form>


        <el-row>
            <el-col :span="8">
                <el-tabs type="border-card">
                    <el-tab-pane label="点位信息">
                        <div class="mes_new_table">
                            <dlTable refName="dlTable" :stripe="true" :border="true" :height="dltableHeight"
                                :columns="columns" :pageConfig="pageConfig" :tableData="tableData"
                                :basicConfig="basicConfig" @selection-change="handleSelectionChange">
                            </dlTable>
                        </div>
                    </el-tab-pane>
                </el-tabs>

            </el-col>
            <el-col :span="16">
                <el-tabs type="border-card" style="margin-left: 10px;">
                    <el-tab-pane label="性能分析">
                        <el-row>
                            <el-col>
                                <el-form :model="queryParamsDely" ref="queryFormDely" :inline="true" size="small"
                                    label-width="68px">
                                    <el-form-item label="开始时间">
                                        <el-date-picker v-model="dateRange" type="datetimerange" range-separator="至"
                                            start-placeholder="开始日期" end-placeholder="结束日期">
                                        </el-date-picker>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" @click="delyHandler"
                                            icon="el-icon-search">延迟分析</el-button>
                                    </el-form-item>
                                </el-form>
                            </el-col>
                        </el-row>
                        <div ref="chart4" class="chart4" :style="{ height: chartHeight + 'px', }"
                            style="width: 100%; margin-left: auto; margin-right: auto;">
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </el-col>
        </el-row>



    </div>
</template>
  
<script>
import {
    nopageList,
    getProcess,
    getOperList,
    analysisDelay,
} from "@/api/collect/point";
import {
    getAllList
} from "@/api/collect/store";
import dayjs from "dayjs";

export default {
    name: "syncAnalysis",
    data() {
        return {
            chart4: null,
            storeList: [],
            processList: [],
            operList: [],
            dltableHeight: 660,
            chartHeight: 660,
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 点位配置表格数据
            pointList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                storeCode: null,
                pointCode: null,
                pointName: null,
                pointProcess: null,
                pointOperation: null,
                pointDesc: null
            },
            dateRange: [],
            queryParamsDely: {

            },
            basicConfig: {
                index: false, // 是否启用序号列
                needPage: false, // 是否展示分页
                indexName: null, // 序号列名(默认为：序号)
                selectionType: true, // 是否启用多选框
                indexWidth: null, // 序号列宽(默认为：50)
                indexFixed: null, // 序号列定位(默认为：left)
                settingType: true, // 是否展示表格配置按钮
                headerSortSaveType: false // 表头排序是否保存在localStorage中
            },
            pageConfig: {
                pageNum: 1, // 页码
                pageSize: 20, // 每页显示条目个数
                total: 0, // 总数
                background: true, // 是否展示分页器背景色
                pageSizes: [10, 20, 50, 100]// 分页器分页待选项
            },
            columns: [
                {
                    label: '点位编号', // 表头描述
                    fieldIndex: 'pointCode', // 表格显示内容绑定值
                    width: 130,
                    sortable: false, // 此属性可以设置排序
                    filterable: false, // 是否筛选 默认为false
                    searchField: 'POINT_CODE',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: false, // 是否显示筛选icon 和排序 icon
                    fixed: true,
                },

                {
                    label: '点位工序', // 表头描述
                    fieldIndex: 'pointProcess', // 表格显示内容绑定值
                    width: 100,
                    sortable: false, // 此属性可以设置排序
                    filterable: false, // 是否筛选 默认为false
                    searchField: 'POINT_PROCESS',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: false, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '点位业务', // 表头描述
                    fieldIndex: 'pointOperation', // 表格显示内容绑定值
                    width: 100,
                    sortable: false, // 此属性可以设置排序
                    filterable: false, // 是否筛选 默认为false
                    searchField: 'POINT_OPERATION',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: false, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '存储编号', // 表头描述
                    fieldIndex: 'storeCode', // 表格显示内容绑定值
                    width: 100,
                    sortable: false, // 此属性可以设置排序
                    filterable: false, // 是否筛选 默认为false
                    searchField: 'STORE_CODE',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: false, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '点位名称', // 表头描述
                    fieldIndex: 'pointName', // 表格显示内容绑定值
                    width: 260,
                    sortable: false, // 此属性可以设置排序
                    filterable: false, // 是否筛选 默认为false
                    searchField: 'POINT_NAME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: false, // 是否显示筛选icon 和排序 icon
                },

                {
                    label: '最后采集时间', // 表头描述
                    fieldIndex: 'lastCollectionTime', // 表格显示内容绑定值
                    width: 150,
                    sortable: false, // 此属性可以设置排序
                    filterable: false, // 是否筛选 默认为false
                    searchField: 'LAST_COLLECTION_TIME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: false, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '开始采集时间', // 表头描述
                    fieldIndex: 'startCollectionTime', // 表格显示内容绑定值
                    width: 150,
                    sortable: false, // 此属性可以设置排序
                    filterable: false, // 是否筛选 默认为false
                    searchField: 'START_COLLECTION_TIME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: false, // 是否显示筛选icon 和排序 icon
                },

                {
                    label: '状态', // 表头描述
                    fieldIndex: 'status', // 表格显示内容绑定值
                    width: 130,
                    sortable: false, // 此属性可以设置排序
                    filterable: false, // 是否筛选 默认为false
                    searchField: 'STATUS',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: false, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '点位描述', // 表头描述
                    fieldIndex: 'pointDesc', // 表格显示内容绑定值
                    width: 300,
                    sortable: false, // 此属性可以设置排序
                    filterable: false, // 是否筛选 默认为false
                    searchField: 'POINT_DESC',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: false, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '备注', // 表头描述
                    fieldIndex: 'remark', // 表格显示内容绑定值
                    width: 130,
                    sortable: false, // 此属性可以设置排序
                    filterable: false, // 是否筛选 默认为false
                    searchField: 'REMARK',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: false, // 是否显示筛选icon 和排序 icon
                }
            ],
            tableData: [],
            selectVO: '',
            option4: {

                tooltip: {
                    trigger: 'axis',
                    confine: true,
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                legend: {
                    data: ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine'],
                    orient: 'horizontal',
                    left: 'left',
                    top: 'top',
                    type: 'scroll',
                },
                dataZoom: [{ // 这部分是dataZoom组件
                    type: 'inside', // 表示内置型数据区域缩放组件
                    start: 0, // 数据窗口范围的起始百分比
                    end: 80 // 数据窗口范围的结束百分比
                }],
                grid: {
                    left: '1%',
                    right: '1%',
                    bottom: '%',
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'category',
                        boundaryGap: false,
                        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                    }
                ],
                series: [
                    {
                        name: 'Email',
                        type: 'line',
                        stack: 'Total',
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        data: [120, 132, 101, 134, 90, 230, 210]
                    },
                    {
                        name: 'Union Ads',
                        type: 'line',
                        stack: 'Total',
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        data: [220, 182, 191, 234, 290, 330, 310]
                    },
                    {
                        name: 'Video Ads',
                        type: 'line',
                        stack: 'Total',
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        data: [150, 232, 201, 154, 190, 330, 410]
                    },
                    {
                        name: 'Direct',
                        type: 'line',
                        stack: 'Total',
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        data: [320, 332, 301, 334, 390, 330, 320]
                    },
                    {
                        name: 'Search Engine',
                        type: 'line',
                        stack: 'Total',
                        label: {
                            show: true,
                            position: 'top'
                        },
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        data: [820, 932, 901, 934, 1290, 1330, 1320]
                    }
                ]
            },
        };
    },
    created() {
        this.dateRange.push(dayjs(new Date()).add(-1, 'hour'));
        this.dateRange.push(dayjs(new Date()));
        getAllList().then(response => {
            this.storeList = response.data;
        });
        getProcess().then(response => {
            this.processList = response.data;
        });
        getOperList().then(response => {
            this.operList = response.data;
        });
        this.getList(null);
    },
    methods: {
        /** 查询点位配置列表 */
        getList(selectVO) {
            this.loading = true;
            if (selectVO) {
                this.selectVO = selectVO;
            }
            this.queryList();
        },
        queryList() {
            nopageList(this.queryParams).then(response => {
                this.tableData = response.data;
            });
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.getList();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.pointCode)
        },
        delyHandler() {
            if (this.ids == null || this.ids.length == 0) {
                this.$message.warning('请选择点位');
                return;
            }

            this.chart4 = this.$echarts.init(this.$refs.chart4);
            this.chart4.clear();
            var par = {}
            par.dtStart = dayjs(this.dateRange[0]).format(
                "YYYY-MM-DD HH:mm:ss"
            );
            par.dtEnd = dayjs(this.dateRange[1]).format(
                "YYYY-MM-DD HH:mm:ss"
            );
            par.potinCodeList = this.ids;
            analysisDelay(par).then((response) => {


                this.option4.legend.data.splice(0, this.option4.legend.data.length);
                this.option4.xAxis[0].data.splice(0, this.option4.xAxis[0].data.length);
                this.option4.series.splice(0, this.option4.series.length);

                response.data.xAxis.forEach(element => {
                    this.option4.xAxis[0].data.push(element);
                })
                response.data.legend.forEach(element => {
                    this.option4.legend.data.push(element);
                })

                this.option4.legend.data.forEach((element, index) => {
                    var item = response.data.series.filter((item) => {
                        return item.name == element;
                    })
                    this.option4.series.push({
                        name: item[0].name,
                        type: 'line',
                        stack: 'Total',
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        data: item[0].data
                    })
                })
                this.chart4.clear();
                this.chart4.setOption(this.option4);
            });

        }
    },
    mounted() {
        this.$nextTick(() => {
            this.$nextTick(() => {
                /*mes_new_table 到顶部的高炉 */
                let topValue = document.getElementsByClassName('mes_new_table')[0].getBoundingClientRect().top;
                /*mes_new_table 屏幕高度-mes_new_table到顶部的高炉-50（分页控件） */
                this.dltableHeight = document.body.clientHeight - topValue - 20;

                let chart4Vaue = document.getElementsByClassName('chart4')[0].getBoundingClientRect().top;
                this.chartHeight = document.body.clientHeight - chart4Vaue - 20;
            })
        })
    },
}
    ;
</script>
<style scoped>
.scroll-container {
    width: 100%;
    /* 或者指定一个固定宽度 */
    overflow-x: auto;
    /* 启用横向滚动 */
}

.el-row {
    display: flex;
    /* 使用flex布局 */
}
</style>