<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="高炉" prop="prodCode">
        <el-input v-model="queryParams.prodCode" placeholder="加工中心编码" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-input v-model="queryParams.status" placeholder="选择状态" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="启用时间" prop="beginTime">
        <el-input v-model="queryParams.beginTime" placeholder="启用时间" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row>
      <el-col :span="20">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
              @click="handleUpdate">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
              @click="handleDelete">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
              @click="handleNext">流程</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>


        <el-table v-loading="loading" :data="planlist" @selection-change="handleSelectionChange" border
          style="width: 100%;">
          <af-table-column type="selection" :fit="false" width="55" align="left" header-align="center" />
          <el-table-column label="ID" align="left" header-align="center" prop="planid" type="expand">
            <template slot-scope="props">
              <el-table fit v-loading="loading" :data="props.row.listOre" border style="width: 90%;background: #fff;"
                :row-class-name="tableRowClassName" :header-cell-style="{
                  textAlign: 'center',
                  height: '10px',
                }">
                <el-table-column label="原料" fixed="left" align="left" header-align="center" width="200px"
                  prop="materialNumber" />
                <el-table-column label="配比" align="left" header-align="center">
                  <el-table-column label="%" align="left" header-align="center" prop="allRate" />
                  <el-table-column label="kg" align="left" header-align="center" prop="allWt" />
                </el-table-column>
                <el-table-column label="Fe" align="left" header-align="center">
                  <el-table-column label="%" align="left" header-align="center" prop="tfeRate" />
                  <el-table-column label="kg" align="left" header-align="center" prop="tfeWt" v-if="wtShowFlag" />
                </el-table-column>
                <el-table-column label="SiO2" align="left" header-align="center">
                  <el-table-column label="%" align="left" header-align="center" prop="sio2Rate" />
                  <el-table-column label="kg" align="left" header-align="center" prop="sio2Wt" v-if="wtShowFlag" />
                </el-table-column>
                <el-table-column label="CaO" align="left" header-align="center">
                  <el-table-column label="%" align="left" header-align="center" prop="caoRate" />
                  <el-table-column label="kg" align="left" header-align="center" prop="caoWt" v-if="wtShowFlag" />
                </el-table-column>
                <el-table-column label="MgO" align="left" header-align="center">
                  <el-table-column label="%" align="left" header-align="center" prop="mgoRate" />
                  <el-table-column label="kg" align="left" header-align="center" prop="mgoWt" v-if="wtShowFlag" />
                </el-table-column>
                <el-table-column label="Al2O3" align="left" header-align="center">
                  <el-table-column label="%" align="left" header-align="center" prop="al2o3Rate" />
                  <el-table-column label="kg" align="left" header-align="center" prop="al2o3Wt" v-if="wtShowFlag" />
                </el-table-column>

                <!-- <el-table-column label="S" align="left" header-align="center">
                  <el-table-column label="%" align="left" header-align="center" prop="sRate" />
                  <el-table-column label="kg" align="left" header-align="center" prop="sWt" v-if="wtShowFlag" />
                </el-table-column>

                <el-table-column label="P" align="left" header-align="center">
                  <el-table-column label="%" align="left" header-align="center" prop="pRate" />
                  <el-table-column label="kg" align="left" header-align="center" prop="pWt" v-if="wtShowFlag" />
                </el-table-column>

                <el-table-column label="Zn" align="left" header-align="center">
                  <el-table-column label="%" align="left" header-align="center" prop="znRate" />
                  <el-table-column label="kg" align="left" header-align="center" prop="znWt" v-if="wtShowFlag" />
                </el-table-column>

                <el-table-column label="Ti" align="left" header-align="center">
                  <el-table-column label="%" align="left" header-align="center" prop="tiRate" />
                  <el-table-column label="kg" align="left" header-align="center" prop="tiWt" v-if="wtShowFlag" />
                </el-table-column>

                <el-table-column label="Mn" align="left" header-align="center">
                  <el-table-column label="%" align="left" header-align="center" prop="mnRate" />
                  <el-table-column label="kg" align="left" header-align="center" prop="mnWt" v-if="wtShowFlag" />
                </el-table-column>

                <el-table-column label="K" align="left" header-align="center">
                  <el-table-column label="%" align="left" header-align="center" prop="kRate" />
                  <el-table-column label="kg" align="left" header-align="center" prop="kWt" v-if="wtShowFlag" />
                </el-table-column>

                <el-table-column label="Na" align="left" header-align="center">
                  <el-table-column label="%" align="left" header-align="center" prop="naRate" />
                  <el-table-column label="kg" align="left" header-align="center" prop="naWt" v-if="wtShowFlag" />
                </el-table-column> -->

                <el-table-column align="center" header-align="center" prop="density">
                  <template slot="header">
                    <div>密度</div>
                    <div>Kg/m3</div>
                  </template>
                </el-table-column>
                <el-table-column align="center" header-align="center" prop="vol">
                  <template slot="header">
                    <div>体积</div>
                    <div>m3</div>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </el-table-column>

          <el-table-column label="矿批" align="center" header-align="center" prop="oreSumWt" />
          <af-table-column label="焦炭" align="center" header-align="center" prop="cokeSumWt" />
          <af-table-column label="焦丁" align="center" header-align="center" prop="cokeNutSumWt" />
          <af-table-column label="煤" align="center" header-align="center" prop="coalSumWt" />

          <af-table-column label="R2" align="center" header-align="center" prop="mainTargets.r2" />
          <af-table-column label="R3" align="center" header-align="center" prop="mainTargets.r4" />
          <af-table-column label="富氧率" align="center" header-align="center" prop="mainTargets.oxRate" />
          <af-table-column label="综合负载" align="center" header-align="center" prop="mainTargets.comLoad" />
          <af-table-column label="综合品味" align="center" header-align="center" prop="mainTargets.comGrade" />
          <af-table-column label="焦比" align="center" header-align="center" prop="mainTargets.ratioOfCoke" />

          <af-table-column label="操作" align="center" header-align="center" fixed="right" width="200"
            class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleNext(scope.row)">流程</el-button>
            </template>
          </af-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>



  </div>
</template>

<script>

import { getPlanList,gonext } from "@/api/feedingplan/feedingpage";
import Treeselect from "@riophae/vue-treeselect";
import { getToken } from "@/utils/auth";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Material",
  dicts: ["effective_or_not", "material_tags"],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 列表主数据
      planlist: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        prodCode: null,
        status: null,
        beginTime: null,
      },
      // 重量显示标记
      wtShowFlag: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询物料管理列表 */
    getList() {
      this.loading = true;
      getPlanList(this.queryParams).then((response) => {
        this.planlist = response.data.rows;
        this.total = response.data.total;
        this.loading = false;
      }).catch(err => {
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      console.log("选中行:",selection);
      this.ids = selection.map((item) => item.planid);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加物料管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const materialId = row.materialId || this.ids;
      getSaveEntity(materialId).then((response) => {

        const obj = response.data;
        // if (obj.tags != null && obj.tags != "") {
        //   obj.tags = obj.tags.split(",");
        // }

        this.form = obj;
        this.open = true;
        this.title = "修改物料管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const obj = JSON.parse(JSON.stringify(this.form));
          // if (obj.tags != null) {
          //   obj.tags = obj.tags.join();
          // }
          saveOrUpdate(obj).then((response) => {
            this.$modal.msgSuccess("保存成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const materialIds = row.materialId || this.ids;
      this.$modal
        .confirm('是否确认删除物料？')
        .then(function () {
          return delMaterial(materialIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    // 计划业务流程
    handleNext(row){
      const planid = row.planid || this.ids;
      const status = row.status;
      if(status=='接收'){

      }else{
        gonext(planid).then(resp=>{
          if(resp.code==200){
            this.$message.success("计划流程已执行到");
          }
        }).catch(err=>{
          // this.$message.error(err);
        })
      }
    },
    // 表格样式
    tableRowClassName({ row, rowIndex }) {
      // console.log('orebg');
      return 'orebg';
    },
  },
};
</script>

<style scoped>
/* 单元格回行设置 */
::deep .el-table .cell {
  white-space: nowrap;
  padding-left: 5px;
  padding-right: 5px;
}

.el-table .orebg {
  height: 5px;
  background: #c72036;
}
</style>

<style>
.demo-table-expand {
  font-size: 0;
}

.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
</style>