<template>
  <div class="app-container" style="padding: 0px;">
    <el-form :model="queryParams" ref="queryForm" :inline="true" size="small" label-width="68px" style="margin-top: 10px;">
      <el-form-item label="业务日期">
        <el-date-picker v-model="workDateArr" type="datetimerange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
            <el-form-item label="高炉">
              <el-select v-model="queryParams.prodCenterCode" placeholder="请选择">
                <el-option v-for="item in prodCodeArr" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery" icon="el-icon-search">查询</el-button>
        <el-button type="primary" @click="exportData" icon="el-icon-download">导出</el-button>
        <el-button type="primary" @click="handleSave" icon="el-icon-success">保存数据</el-button>
      </el-form-item>
    </el-form>
    <div style="margin-top: -10px;">
      <vxe-table
        border
        align="center"
        :loading="loading"
        ref="tableRef"
        height="450"
        :header-cell-style="headerCellStyle"
        :edit-config="{trigger: 'click', mode: 'row'}"
        @edit-closed="editClosedEvent"
        @cell-click="cellClickEvent"
        @header-cell-click="headerCellClickEvent"
        :data="tableData">
        <vxe-column field="workDate" title="业务日期"  width='auto' fixed="left"></vxe-column>
        <vxe-column field="prodCenterCode" title="高炉"  width='auto' fixed="left"></vxe-column>
        <vxe-colgroup title="产量" field="production">
          <vxe-column field="theoreticalProduction" title="理论"  width='auto' :edit-render="{name: 'input'}"></vxe-column>
          <vxe-column field="actualProduction" title="实际"  width='auto' :edit-render="{name: 'input'}"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="一级品率" field="firstProduction" width='auto'>
          <vxe-column field="actualFirstProduction" title="实际"  width='auto'  :edit-render="{name: 'input'}"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="燃料比" field="fuel">
          <vxe-column field="theoreticalFuel" title="理论"  width='auto' :edit-render="{name: 'input'}"></vxe-column>
          <vxe-column field="actualFuel" title="实际"  width='auto' :edit-render="{name: 'input'}"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="焦比" field="coke">
          <vxe-column field="theoreticalCoke" title="理论"  width='auto' :edit-render="{name: 'input'}" ></vxe-column>
          <vxe-column field="actualCoke" title="实际"  width='auto' :edit-render="{name: 'input'}"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="煤比" field="coal">
          <vxe-column field="theoreticalCoal" title="理论" width='auto' :edit-render="{name: 'input'}"></vxe-column>
          <vxe-column field="actualCoal" title="实际" width='auto' :edit-render="{name: 'input'}"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="焦丁比" field="nutCoke">
          <vxe-column field="theoreticalNutCoke" title="理论" width='auto' :edit-render="{name: 'input'}"></vxe-column>
          <vxe-column field="actualNutCoke" title="实际" width='auto' :edit-render="{name: 'input'}"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="系数值" field="coefficient">
          <vxe-column field="theoreticalCoefficient" title="理论" width='auto' :edit-render="{name: 'input'}"></vxe-column>
          <vxe-column field="actualCoefficient" title="实际" width='auto' :edit-render="{name: 'input'}"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="冶强值" field="smeltStrength">
          <vxe-column field="theoreticalSmeltStrength" title="理论" width='auto' :edit-render="{name: 'input'}"></vxe-column>
          <vxe-column field="actualSmeltStrength" title="实际" width='auto' :edit-render="{name: 'input'}"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="煤气回收" field="gas">
          <vxe-column field="gasRecovery" title="煤气回收" width='auto' :edit-render="{name: 'input'}"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="吨铁煤气消耗" field="tonGasRecovery">
          <vxe-column field="tonIronGasConsumption" title="吨铁煤气消耗" width='auto' :edit-render="{name: 'input'}"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="吨铁煤气回收" field="tonGasRecovery">
          <vxe-column field="tonIronGasRecovery" title="吨铁煤气回收" width='auto' :edit-render="{name: 'input'}"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="矿耗" field="ore">
          <vxe-column field="oreConsumption" title="矿耗" width='auto' :edit-render="{name: 'input'}"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="富氧率" field="oxy">
          <vxe-column field="oxyRate" title="富氧率" width='auto' :edit-render="{name: 'input'}"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="Zn负荷" field="zn">
          <vxe-column field="znLoad" title="Zn负荷" width='auto' :edit-render="{name: 'input'}"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="碱负荷" field="alkali">
          <vxe-column field="alkaliLoad" title="碱负荷" width='auto' :edit-render="{name: 'input'}"></vxe-column>
        </vxe-colgroup>
      </vxe-table>
      <lineChart :LineChartData="LineChartData" class="linechartClass" />
    </div>
  </div>
</template>

<script>
  import lineChart from '../../../../components/linechart/lineChart';
  import { lineEchartsComm } from '@/api/chart/lineChartIndicationTemplate.js';
  import {
    listIndicatorSelect,
    indicationEditData
  } from "@/api/report/preview/blastFurnace/techEconomicIndication";
  import dayjs from "dayjs";
    export default {
      name: "techEconomicIndication",
      components: {
        lineChart
      },
      data(){
          return{
            loading:false,
            tableData:[],
            workDateArr:[],
            headerCellStyle:null,
            queryParams:{
              prodCenterCode:'IPES01',
            },
            prodCodeArr:[
              {
                label:"1#高炉",
                value:"IPES01"
              },
              {
                label:"2#高炉",
                value:"IPES02"
              }
            ],
            LineChartData: {
              xData:  [], // X轴数据
              yData:  [] ,// Y轴数据（曲线形高度）
              seriesData:[], // 展示多条曲线
              legendName:[], // 曲线图名称
            },
            // 编辑数据
            editDataArr:[],


          }
      },
      created() {
        this.workDateArr.push(dayjs(new Date()).add(-1, "day"));
        this.workDateArr.push(dayjs(new Date()).add(1, "day"));
        /* 展示页面 */
        this.queryLists();
        this.LineChartData.description = this.getFileName();  // 曲线元素名称
        let config = null;
        let lineConfig = lineEchartsComm().filter(x=>x.businessCode==this.getLineFileUrl());
        if(lineConfig != null || lineConfig.length > 0){
          config = lineConfig;
        }
        if(config==null || config.length==0){
          //  提示
          return  this.$modal.msgSuccess("曲线路径为空");
        }
        this.pageCharConfig=config[0];
        // 显示曲线得列填充颜色
        if(this.pageCharConfig.Y_line_axis.length>0){
          let tableFieldArr=[];
          let tableFieldFirstArr=[];
          for(let i=0;i<this.pageCharConfig.Y_line_axis.length;i++){
            tableFieldArr.push( this.pageCharConfig.Y_line_axis[i].tableField)
            tableFieldFirstArr.push(this.pageCharConfig.Y_line_axis[i].tableFieldFirst)
            this.headerCellStyle = ({ column }) => {
              if (tableFieldArr.includes(column.field ) ) {
                return {
                  backgroundColor: '#f60',
                  color: '#ffffff'
                }
              }else if(tableFieldFirstArr.includes(column.field )) {
                return {
                  backgroundColor: '#efbf33',
                  color: '#ffffff'
                }
              }
            }
          }
        }

      },
      methods:{
        /** 图形名称 */
        getFileName() {
          return this.$route.query.FileName;
        },
        /** 曲线图路径参数 */
        getLineFileUrl() {
          return this.$route.query.lineFileUrl;
        },
        /* 查询按钮 */
        handleQuery(){
          if(this.workDateArr != null){
            if (this.workDateArr.length == 2) {
              this.queryParams.dtStart = dayjs(this.workDateArr[0]).format(
                "YYYY-MM-DD HH:mm:ss"
              );
              this.queryParams.dtEnd = dayjs(this.workDateArr[1]).format(
                "YYYY-MM-DD HH:mm:ss"
              );
            }
          }
          this.queryLists();
        },
        queryLists(){
          if(this.workDateArr != null){
            if (this.workDateArr.length == 2) {
              this.queryParams.dtStart = dayjs(this.workDateArr[0]).format(
                "YYYY-MM-DD HH:mm:ss"
              );
              this.queryParams.dtEnd = dayjs(this.workDateArr[1]).format(
                "YYYY-MM-DD HH:mm:ss"
              );
            }
          }
          listIndicatorSelect(this.queryParams).then(response=>{
            this.tableData = response.rows
            console.log("this.tableData:",JSON.stringify(this.tableData ))
            if( this.tableData.length>0){
              this.LineChartData.seriesData=[]
              this.LineChartData.yData =[]
              this.LineChartData.xData =[]
              var xData=[];
              var yData=[];
              var legendNameArr=[];
              var seriesDataArr=[];
              let yitem = null;
              for (let i = 0; i < this.tableData.length ; i++) {
                const  item = this.tableData[i];
                this.LineChartData.seriesName1 = '理论产量'
                this.LineChartData.legendName = '理论产量'
                yData.push(item['theoreticalProduction'])
                if(this.pageCharConfig.echarType == "曲线图"){
                  xData.push(item[this.pageCharConfig.X_line_axis[0].tableField])
                  legendNameArr.push(this.LineChartData.legendName)
                  this.LineChartData.yData = yData
                  let seriesDataObj={}
                  seriesDataObj.name =  this.LineChartData.seriesName1;
                  seriesDataObj.type = 'line';
                  seriesDataObj.stack = '总量';
                  seriesDataObj.yAxisIndex = 0;  // 使用第一个Y轴（索引为0）
                  seriesDataObj.smooth = true; //  使折线平滑显示，可选属性，默认为false，即折线是直线。true为曲线。
                  seriesDataObj.data = this.LineChartData.yData || []
                  seriesDataArr.push(seriesDataObj)
                  yData=[]
                  this.LineChartData.seriesData = seriesDataArr
                  this.LineChartData.legendName = legendNameArr
                  this.LineChartData.yData = yData   // 曲线图 y1轴
                  this.LineChartData.xData = xData   // 曲线图 x轴
                }
              }
            }
          });
        },
        /* 导出按钮 */
        exportData(){
          this.download('/api/indicator/export', {
            ...this.queryParams
          }, `volume_${new Date().getTime()}.xlsx`)
        },
        /* 保存数据按钮 */
        handleSave(){
          indicationEditData(this.editDataArr).then(response=>{
            this.editDataArr = []
          });

        },
        /*关闭编辑框触发 失去焦点 */
        editClosedEvent(row) {
          this.editDataArr.push(row.row)
          console.log("editClosedEvent this.editDataArr:",this.editDataArr)

        },
        /*表头单元格点击事件*/
        headerCellClickEvent ({ column }) {
          console.log(`表头单元格点击${column.field}`)
          let tableFild = '';
          tableFild=column.field;
          this.chartPlay(tableFild);
        },
        /* // 点击单列*/
        cellClickEvent ({ row, column }) {
          console.log("column:",column)
          console.log("rowrow:",row)
        },

        //曲线图展示
        chartPlay(tableFild){
          let yitem = null;
          let yitemFirst = null;
          if(this.pageCharConfig.echarType == "曲线图"){
            yitem = this.pageCharConfig.Y_line_axis.find(x=>x.tableField==tableFild);
            yitemFirst = this.pageCharConfig.Y_line_axis.find(x=>x.tableFieldFirst==tableFild);
            this.LineChartData.description = this.pageCharConfig.description; // 曲线元素名称
          }
          var xData=[];
          var yData=[];
          var seriesDataArr = [];
          var legendNameArr = [];
          if(yitem==null || yitem.length==0){
            this.LineChartData.legendName =[]
            this.LineChartData.seriesData =[]
            this.LineChartData.xData=[]
            seriesDataArr = []
            yData =[]
            for (let i = 0; i < this.tableData.length ; i++) {
              const  item = this.tableData[i];
              if(this.pageCharConfig.echarType == "曲线图"){
                xData.push(item[this.pageCharConfig.X_line_axis[0].tableField])
                for(let j=0;j<this.pageCharConfig.Y_line_axis.length;j++){
                  if(yitemFirst.tableFieldFirst == this.pageCharConfig.Y_line_axis[j].tableFieldFirst){
                    legendNameArr.push(this.pageCharConfig.Y_line_axis[j].name)
                    yData.push(item[this.pageCharConfig.Y_line_axis[j].tableField])
                    this.LineChartData.yData = yData
                    let seriesDataObj={}
                    seriesDataObj.name = this.pageCharConfig.Y_line_axis[j].name;
                    seriesDataObj.type = 'line';
                    seriesDataObj.stack = '总量';
                    seriesDataObj.yAxisIndex = 0;  // 使用第一个Y轴（索引为0）
                    seriesDataObj.smooth = true; //  使折线平滑显示，可选属性，默认为false，即折线是直线。true为曲线。
                    seriesDataObj.data = this.LineChartData.yData || []
                    seriesDataArr.push(seriesDataObj)
                    yData=[]
                  }
                }
                this.LineChartData.seriesData = seriesDataArr
                this.LineChartData.legendName = legendNameArr
                this.LineChartData.xData = xData   // 曲线图 x轴
              }
            }
          }
          if(yitemFirst==null || yitemFirst.length==0){
            this.LineChartData.legendName =[]
            this.LineChartData.seriesData =[]
            this.LineChartData.xData=[]
            yData =[]
            seriesDataArr=[]
            this.LineChartData.seriesName1 = yitem.name
            this.LineChartData.legendName = yitem.name
            for (let i = 0; i < this.tableData.length ; i++) {
              const  item = this.tableData[i];
              if(this.pageCharConfig.echarType == "曲线图"){
                xData.push(item[this.pageCharConfig.X_line_axis[0].tableField])
                for(let j=0;j<this.pageCharConfig.Y_line_axis.length;j++){
                  if(yitem.tableField == this.pageCharConfig.Y_line_axis[j].tableField){
                    legendNameArr.push(this.pageCharConfig.Y_line_axis[j].name)
                    yData.push(item[this.pageCharConfig.Y_line_axis[j].tableField])
                    this.LineChartData.yData = yData
                    let seriesDataObj={}
                    seriesDataObj.name = this.pageCharConfig.Y_line_axis[j].name;
                    seriesDataObj.type = 'line';
                    seriesDataObj.stack = '总量';
                    seriesDataObj.yAxisIndex = 0;  // 使用第一个Y轴（索引为0）
                    seriesDataObj.smooth = true; //  使折线平滑显示，可选属性，默认为false，即折线是直线。true为曲线。
                    seriesDataObj.data = this.LineChartData.yData || []
                    seriesDataArr.push(seriesDataObj)
                    yData=[]
                  }
                }
                this.LineChartData.seriesData = seriesDataArr
                this.LineChartData.legendName = legendNameArr
                this.LineChartData.xData = xData   // 曲线图 x轴
              }
            }
          }
        },

      },

    }
</script>

<style scoped>

</style>
