import request from '@/utils/request'

// 查询内控标准列表
export function listStanad(query) {
  return request({
    url: '/api/md/internalStanad/list',
    method: 'get',
    params: query
  })
}

// 查询内控标准详细
export function getStanad(stanadId) {
  return request({
    url: '/api/md/internalStanad/getStanad/' + stanadId,
    method: 'get'
  })
}

// 新增内控标准
export function saveOrUpdate(data) {
  return request({
    url: '/api/md/internalStanad/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 删除内控标准
export function delStanad(stanadId) {
  return request({
    url: '/api/md/internalStanad/' + stanadId,
    method: 'delete'
  })
}
