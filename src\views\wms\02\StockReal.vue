<template>
    <div>
        <div style="height: 100px; ">
            搜索区域
        </div>

        <vxe-grid v-bind="gridOptions" style="margin: 10px;" @toolbar-button-click="toolbarButtonClickEvent"></vxe-grid>
    </div>
</template>

<script>
import { selectStockRealData } from "@/api/wms/stockreal";

export default {
    name: 'StockReal2',

    data() {
        return {
            gridOptions: {
                columns: [],
                data: [],

                border: true,
                stripe: true,
                align: 'center',

                // columnConfig: {
                //     resizable: true
                // },
                // rowConfig: {
                //     isHover: true,
                //     isCurrent: true,
                // },

                editConfig: {
                    trigger: 'click',
                    mode: 'row',
                },

                toolbarConfig: {
                    buttons: [
                        { name: '新增', code: 'myAdd', status: 'primary' },
                        { name: '删除', code: 'myDel', status: 'error' },
                        { name: '保存', code: 'mySave', status: 'success' }
                    ],
                    custom: true,
                    zoom: true
                },


                // mergeCells: [
                //     { row: 0, col: 1, rowspan: 3 },
                //     { row: 3, col: 1, rowspan: 3 },
                // ],
            },
            gridData: []
        }
    },

    mounted() {
        this.initGridData();
    },

    methods: {
        initGridData() {
            selectStockRealData().then(response => {
                this.gridData = response.data;
                this.gridOptions.data = this.gridData;
            });

            this.gridOptions.columns = [
                { type: 'seq', width: 70 },
                { field: 'crossRegion', title: '料条' },
                { field: 'stackingPosition', title: '垛位' },
                { field: 'mateCode', title: '物料编码' },
                { field: 'mateName', title: '物料名称' },
                { field: 'stockWeight', title: '库存重量' },
                { field: 'specCode', title: '规格型号' }
            ]

        },

        toolbarButtonClickEvent(params) {
            console.log(params);

            if (params.code === 'myAdd') {

            }

        }

        // formSubmitEvent() {
        //     console.log('form submit')
        // },
        // formResetEvent() {
        //     console.log('form reset')
        // }
    }
}
</script>
