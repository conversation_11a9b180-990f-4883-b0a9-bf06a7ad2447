<template>
    <div class="app-container" style="padding:10px;">

        <vxe-grid ref="gridRef" v-bind="gridOptions" @page-change="pageChangeEvent">
            <template #form>
                <vxe-form ref="searchFormRef" v-bind="searchFormOptions">
                    <template #action>
                        <vxe-button status="primary" @click="searchEvent">搜索</vxe-button>
                        <vxe-button @click="resetEvent">重置</vxe-button>
                    </template>
                </vxe-form>
            </template>

            <template #toolbarButtons>
                <vxe-button status="primary" @click="addEvent">新增</vxe-button>
            </template>

            <template #active="{ row }">
                <vxe-button mode="button" status="success" @click="saveRow(row)">保存</vxe-button>
                <vxe-button mode="button" status="error" @click="removeRow(row)">删除</vxe-button>
            </template>

        </vxe-grid>

        <!-- 添加或修改仓储-仓库对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">

                <el-form-item label="仓库编码" prop="storehouseCode">
                    <el-input v-model="form.storehouseCode" type="text" placeholder="请输入内容" />
                </el-form-item>

                <el-form-item label="仓库名称" prop="storehouseName">
                    <el-input v-model="form.storehouseName" type="text" placeholder="请输入内容" />
                </el-form-item>

                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                </el-form-item>
            </el-form>

            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listStorehouse, saveOrUpdate, deleteStorehouse } from "@/api/wms/storehouse";
import { VxeUI } from 'vxe-pc-ui'

export default {
    name: 'storehouse',
    data() {
        const gridOptions = {
            columns: [
                { type: 'checkbox', width: 50 },
                { field: 'seq', type: 'seq', width: 50 },
                { field: 'storehouseId', title: 'storehouseId', visible: false },
                { field: 'prodCenterCode', title: '加工中心编码', },
                { field: 'prodCenterName', title: '加工中心', },
                { field: 'storehouseCode', title: '仓库编码', },
                { field: 'storehouseName', title: '仓库名称', editRender: { name: 'VxeInput' } },
                { field: 'storehouseStatus', title: '仓库状态', },
                { field: 'remark', title: '备注', },
                { field: 'active', title: '操作', fixed: 'right', slots: { default: 'active' } }
            ],
            data: [],

            border: true,
            strip: true,
            align: 'center',
            showOverflow: true,
            keepSource: true,
            loading: false,

            columnConfig: {
                resizable: true
            },
            rowConfig: {
                isHover: true,
                isCurrent: true,
            },
            pagerConfig: {
                total: 0,
                currentPage: 1,
                pageSize: 10
            },
            toolbarConfig: {
                // custom: true,
                zoom: true,
                slots: {
                    buttons: 'toolbarButtons'
                }
            },
            editConfig: {
                mode: 'row',
                trigger: 'dblclick',
                showStatus: true,
            },
            editRules: {
                storehouseCode: [
                    { required: true }
                ],
                storehouseName: [
                    { required: true }
                ],
            },

        }

        const searchFormOptions = {
            data: {
                storehouseName: '',
            },
            items: [
                { field: 'storehouseName', title: '仓库名称', itemRender: { name: 'VxeInput' } },
                { slots: { default: 'action' } }
            ]
        }

        return {
            gridOptions,
            searchFormOptions,

            title: '',
            open: false,
            form: {},
            // 表单校验
            rules: {
                storehouseCode: [
                    {
                        required: true, message: "仓库编码不能为空", trigger: "blur"
                    }
                ],
                storehouseName: [
                    {
                        required: true, message: "仓库名称不能为空", trigger: "blur"
                    }
                ],
            },
        }
    },
    methods: {
        searchList() {
            this.handlePageData()
        },
        handlePageData() {
            this.gridOptions.loading = true
            listStorehouse(this.searchFormOptions.data).then(response => {
                let data = response.data
                const { pageSize, currentPage } = this.gridOptions.pagerConfig
                this.gridOptions.pagerConfig.total = data.length
                this.gridOptions.data = data.slice((currentPage - 1) * pageSize, currentPage * pageSize)
                this.gridOptions.loading = false
            })
        },
        pageChangeEvent({ pageSize, currentPage }) {
            this.gridOptions.pagerConfig.currentPage = currentPage
            this.gridOptions.pagerConfig.pageSize = pageSize
            this.handlePageData()
        },

        searchEvent() {
            this.searchList()
        },
        resetEvent() {
            const $form = this.$refs.searchFormRef
            if ($form) {
                $form.reset()
                this.searchList()
            }
        },

        addEvent() {
            this.open = true
            this.title = "添加仓储-仓库"
        },

        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    saveOrUpdate(this.form).then((response) => {
                        this.$modal.msgSuccess("修改成功");
                        this.cancel()
                        this.searchList()
                    });
                }
            });

            // saveOrUpdate(this.form).then(response => {
            //     this.cancel()
            //     this.searchList()
            //     VxeUI.modal.message({
            //         content: '新增成功',
            //         status: 'success'
            //     })
            // })
        },
        cancel() {
            this.open = false
            this.form = {}
        },


        async saveRow(row) {
            const $grid = this.$refs.gridRef
            if ($grid) {
                const errMap = await $grid.validate(true)
                if (errMap) {
                    VxeUI.modal.message({ status: 'error', content: '校验不通过！' })
                    return
                }
                const { updateRecords } = $grid.getRecordset()
                if (updateRecords.length != 1) {
                    return
                }
                saveOrUpdate(row).then(response => {
                    this.searchList()
                    VxeUI.modal.message({
                        content: '修改成功',
                        status: 'success'
                    })
                })
            }
        },

        removeRow(row) {
            const $grid = this.$refs.gridRef
            if ($grid) {
                if (row.storehouseId === null) {
                    $grid.remove(row)
                } else {
                    deleteStorehouse(row.storehouseId).then(response => {
                        this.searchList()
                    })
                }
            }
        },
    },
    created() {

    },
    mounted() {
        this.searchList()
    },
}
</script>