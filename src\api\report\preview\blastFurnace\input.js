import request from '@/utils/request'

// 查询操作日志记录列表
export function listIronWeightAndPackAgeWeight(query) {
  return request({
    url: '/api/blastFurnace/iron/listIronWeightAndPackAgeWeight',
    method: 'get',
    params: query
  })
}

// 点击倒灌站到站时间 向铁水包表中插入数据
export function insertPackAgeData(data) {
  return request({
    url: '/api/blastFurnace/iron/insertPackAgeData',
    method: 'post',
    data: data
  })
}

// 点击新增包号按钮 向铁水包表中插入数据
export function insertNewPackAgeData(data) {
  return request({
    url: '/api/blastFurnace/iron/insertNewPackAgeData',
    method: 'post',
    data: data
  })
}



// 混包
export function mixedPackAgeData(data) {
  return request({
    url: '/api/blastFurnace/iron/mixedPackAgeData',
    method: 'post',
    data: data
  })
}

// 高炉铁次号数据 编辑
export function ironWeightInputEdit(data) {
  return request({
    url: '/api/blastFurnace/iron/ironWeightInputEdit',
    method: 'put',
    data: data
  })
}

//  计算预测温度
export function calculatedWenDuMethod(data) {
  return request({
    url: '/api/blastFurnace/iron/calculatedWenDuMethod',
    method: 'post',
    data: data
  })
}

// 修改倒灌站到站时间标识
export function updateBackCanStationFlagMethod(data) {
  return request({
    url: '/api/blastFurnace/iron/updateBackCanStationFlagMethod',
    method: 'put',
    data: data
  })
}




