<template>
    <div>
        <div style="margin: 10px;">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
                <el-form-item label="工作日期">
                    <el-date-picker v-model="queryParams.workDate" type="date" placeholder="选择日期"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                </el-form-item>
            </el-form>
        </div>

        <vxe-grid v-bind="gridOptions"></vxe-grid>
    </div>
</template>

<script>
import { queryByMaterial } from "@/api/bin/consume";

import dayjs from "dayjs";

export default {
    name: "ConsumeReport2",

    data() {
        return {
            gridOptions: {
                emptyText: '没有更多数据了！',
                // columnConfig: {
                //     resizable: true
                // },
                height: 778,
                border: true,
                align: 'center',
                columns: [],
                data: []
            },
            // 遮罩层
            loading: false,

            // 查询参数
            queryParams: {
                prodCenterCode: null,
                workDate: this.getDate(),
            },

        }
    },

    created() {
        this.getList(null);
    },

    methods: {
        queryByMaterialList() {

            this.queryParams.prodCenterCode = this.getProdCenterCode();
            console.log(this.queryParams.workDate);

            queryByMaterial(this.queryParams).then(resp => {
                // console.log("resp:", JSON.stringify(resp.data));

                if (!resp.hasOwnProperty('data')) {
                    this.gridOptions.columns = null;
                    this.gridOptions.data = null;
                    return;
                }

                //  {"纽曼块":1353,"伊朗球":196,"工作日期":"2025-03-19 00:00:00","烧结矿":7518,"焦丁":240,"印度球":53,"焦炭":1775,"南非块":583,"料批":1}
                var arr = resp.data[0];
                for (let index = 1; index < resp.data.length; index++) {

                    var a = Object.keys(arr).length;
                    var b = Object.keys(resp.data[index]).length
                    if (b > a) {
                        arr = resp.data[index];
                    }
                }

                var columns = [];

                Object.keys(arr).forEach(key => {
                    var column = new Object();
                    column.field = key;
                    column.title = key;
                    if (key == "料批") {
                        column.width = 60;
                    }
                    columns.push(column);
                });

                this.gridOptions.columns = columns;
                this.gridOptions.data = resp.data;
                this.loading = false;
            })
        },

        /** 查询变料-消耗记录列表 */
        getList(selectVO) {
            this.loading = true;
            if (selectVO) {
                this.selectVO = selectVO;
            }
            this.queryByMaterialList();
        },

        /** 搜索按钮操作 */
        handleQuery() {
            this.getList();
        },

        getProdCenterCode() {
            return this.$route.query.prodCenterCode;
        },

        getDate() {
            var now = new Date();
            var year = now.getFullYear(); //得到年份
            var month = now.getMonth(); //得到月份
            var date = now.getDate(); //得到日期
            var hour = " 00:00:00"; //默认时分秒 如果传给后台的格式为年月日时分秒，就需要加这个，如若不需要，此行可忽略
            month = month + 1;
            month = month.toString().padStart(2, "0");
            date = date.toString().padStart(2, "0");
            var defaultDate = `${year}-${month}-${date}`;//
            return defaultDate;
        },



    }

}
</script>
