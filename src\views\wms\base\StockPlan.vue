<template>
    <div>
        <vxe-grid ref="gridRef" v-bind="gridOptions" @page-change="pageChangeEvent">
            <template #form>
                <vxe-form ref="searchFormRef" v-bind="formOptions">
                    <template #action>
                        <vxe-button status="primary" @click="searchEvent">搜索</vxe-button>
                        <vxe-button @click="resetEvent">重置</vxe-button>
                    </template>
                </vxe-form>
            </template>

            <template #toolbarButtons>
                <vxe-button status="primary" @click="addEvent">新增</vxe-button>
                <vxe-button status="primary" @click="toLws">下发检斤</vxe-button>
            </template>

            <template #active="{ row }">
                <vxe-button mode="button" status="success" @click="saveRow(row)">保存</vxe-button>
                <vxe-button mode="button" status="error" @click="removeRow(row)">删除</vxe-button>
            </template>
        </vxe-grid>
    </div>
</template>

<script>
import { initMaterialList } from "@/api/md/material";
import { listStorehouse } from "@/api/wms/storehouse";
import { selectStockPlanData, saveOrUpdate, deletePlan } from "@/api/wms/stockplan";
import { VxeUI } from 'vxe-pc-ui'

export default {
    name: 'StockPlan',
    data() {

        const materialEditRender = {
            name: 'VxeSelect',
            props: {
                filterable: true,
                clearable: true,
            },
            options: []
        }

        const storehouseEditRender = {
            name: 'VxeSelect',
            options: [
                // { value: 'YLC001', label: '原料库1#料场' },
                // { value: 'YLC002', label: '原料库2#料场' },
            ]
        }

        const stripEditRender = {
            name: 'VxeSelect',
            props: {
                clearable: true,
            },
            options: [
                { value: 'A', label: 'A' },
                { value: 'B', label: 'B' },
                { value: 'C', label: 'C' },
                { value: 'D', label: 'D' },
                { value: 'E', label: 'E' },
            ]
        }

        const gridOptions = {
            columns: [
                { type: 'checkbox', width: 50 },
                { field: 'seq', type: 'seq', width: 50 },
                { field: 'stockPlanId', title: 'stockPlanId', visible: false },
                { field: 'materialCode', title: '物料名称', editRender: materialEditRender },
                { field: 'warehouseCode', title: '原料场', editRender: storehouseEditRender },
                { field: 'strip', title: '料条', editRender: stripEditRender },
                { field: 'stack', title: '垛位', editRender: { name: 'VxeInput', props: { type: 'integer' } } },
                { field: 'startPosition', title: '开始位置', editRender: { name: 'VxeInput', props: { type: 'integer' } } },
                { field: 'endPosition', title: '结束位置', editRender: { name: 'VxeInput', props: { type: 'integer' } } },
                { field: 'active', title: '操作', fixed: 'right', slots: { default: 'active' } }
            ],
            data: [],

            border: true,
            strip: true,
            align: 'center',
            showOverflow: true,
            keepSource: true,
            loading: false,
            // height: 800,

            columnConfig: {
                resizable: true
            },
            rowConfig: {
                isHover: true,
                isCurrent: true,
            },

            pagerConfig: {
                total: 0,
                currentPage: 1,
                pageSize: 10
            },

            toolbarConfig: {
                // custom: true,
                zoom: true,
                slots: {
                    buttons: 'toolbarButtons'
                }
            },

            editConfig: {
                mode: 'row',
                trigger: 'dblclick',
                showStatus: true,
            },
            editRules: {
                materialName: [
                    { required: true }
                ],
                warehouseName: [
                    { required: true }
                ],
                strip: [
                    { required: true }
                ],
                stack: [
                    { required: true }
                ],
                startPosition: [
                    { required: true }
                ],
                endPosition: [
                    { required: true }
                ],
            },
        }

        const formOptions = {
            data: {
                materialCode: '',
                strip: '',
            },
            items: [
                { field: 'strip', title: '料条', itemRender: stripEditRender },
                { field: 'materialCode', title: '物料', itemRender: materialEditRender },
                { slots: { default: 'action' } }
            ]
        }

        return {
            gridOptions,
            formOptions,

            stripEditRender,
            materialEditRender,
            storehouseEditRender,
        }
    },
    methods: {

        searchList() {
            this.handlePageData()
        },

        handlePageData() {
            this.gridOptions.loading = true
            selectStockPlanData(this.formOptions.data).then(response => {
                let data = response.data
                const { pageSize, currentPage } = this.gridOptions.pagerConfig
                this.gridOptions.pagerConfig.total = data.length
                this.gridOptions.data = data.slice((currentPage - 1) * pageSize, currentPage * pageSize)
                this.gridOptions.loading = false
            })
        },

        pageChangeEvent({ pageSize, currentPage }) {
            this.gridOptions.pagerConfig.currentPage = currentPage
            this.gridOptions.pagerConfig.pageSize = pageSize
            this.handlePageData()
        },

        async saveRow(row) {
            const $grid = this.$refs.gridRef
            if ($grid) {
                const errMap = await $grid.validate(true)
                if (errMap) {
                    VxeUI.modal.message({ status: 'error', content: '校验不通过！' })
                    return
                }
                const { insertRecords, updateRecords } = $grid.getRecordset()
                // console.log(row);

                if (insertRecords.length === 1) {
                    // VxeUI.modal.message({ status: 'success', content: '新增成功' })
                    saveOrUpdate(insertRecords[0]).then(response => {
                        this.searchList()
                        VxeUI.modal.message({
                            content: '新增成功',
                            status: 'success'
                        })
                    })
                }

                if (updateRecords.length === 1) {
                    // VxeUI.modal.message({ status: 'success', content: '修改成功' })

                    saveOrUpdate(updateRecords[0]).then(response => {
                        this.searchList()
                        VxeUI.modal.message({
                            content: '修改成功',
                            status: 'success'
                        })
                    })
                }
            }
        },

        removeRow(row) {
            console.log(row);
            const $grid = this.$refs.gridRef
            if ($grid) {
                if (row.stockPlanId === null) {
                    $grid.remove(row)
                } else {
                    deletePlan(row.stockPlanId).then(response => {
                        this.searchList()
                    })
                }
            }
        },

        searchEvent() {
            // console.log(this.formOptions.data)
            this.searchList()
        },
        resetEvent() {
            const $form = this.$refs.searchFormRef
            if ($form) {
                $form.reset()
                this.searchList()
            }
        },

        async addEvent() {
            const $grid = this.$refs.gridRef
            if ($grid) {
                const record = {}
                const { row: newRow } = await $grid.insertAt(record, null)
                await $grid.setEditRow(newRow)
            }
        },

        initList() {
            initMaterialList().then(response => {
                let data = response.data
                let list = []
                for (let i = 0; i < data.length; i++) {
                    list.push({
                        value: `${data[i].materialNumber}`,
                        label: `${data[i].shortName ? data[i].shortName : data[i].materialName}`
                    })
                }
                this.materialEditRender.options = list
            })

            listStorehouse(null).then(response => {
                let data = response.data
                let list = []
                for (let i = 0; i < data.length; i++) {
                    list.push({
                        value: `${data[i].storehouseCode}`,
                        label: `${data[i].storehouseName}`
                    })
                }
                this.storehouseEditRender.options = list
            })
        },

        toLws() {
            console.log("下发检斤Lws");
        },
    },
    created() {
        this.initList()
    },
    mounted() {
        this.searchList()
    },
}
</script>

<!-- primary, success, info, warning, danger -->