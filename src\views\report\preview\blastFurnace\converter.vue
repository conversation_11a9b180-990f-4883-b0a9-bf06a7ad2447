<template>
  <div class="app-container" style="padding: 0px;">
    <div class="block" style="margin-top:8px;">
      <span style=" margin-left: 10px;">铁水包任务号:</span>
      <el-input v-model="selectParam.packageTaskNumber" placeholder="请输入内容" style="width: 200px;margin-left: 10px;"
                clearable></el-input>
      <span style=" margin-left: 10px;">混罐号:</span>
      <el-input v-model="selectParam.mixedCanNumber" placeholder="请输入内容" style="width: 200px;margin-left: 10px;"
                clearable></el-input>
      <span style=" margin-left: 10px;">倒灌站到达时间:</span>
      <el-date-picker style="margin-left: 10px;width: 392px;"
                      v-model="converterLadleTimeArr" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
      </el-date-picker>
      <span style=" margin-left: 10px;">转炉兑铁状态:</span>
      <el-select v-model="selectParam.converterLadleTimeStatus" placeholder="请选择" clearable style="width: 200px;margin-left: 10px;" >
        <el-option v-for="item in converterLadleTimeStatusList" :key="item.value" :label="item.label" :value="item.value" ></el-option>
      </el-select>
      <el-button type="primary" style="margin-left: 50px;" @click="handleQuery" size="mini">搜索</el-button>
      <el-button type="primary" style="margin-left: 50px;" @click="handleSave" size="mini">保存</el-button>
    </div>
    <div style="margin-top: 7px;">
      <vxe-table
        border
        align="center"
        :loading="loading"
        ref="tableRef"
        :edit-config="{trigger: 'click', mode: 'row'}"
        @edit-closed="editClosedEvent"
        :checkbox-config="{checkField: 'isChecked', indeterminateField: 'isIndeterminate'}"
        @checkbox-change="handleCheckboxChange"
        @cell-click="handleCellClickTime"
        :data="tableData">
        <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
        <vxe-column field="ironNumber" title="铁次号"  width='auto' fixed="left"></vxe-column>
        <vxe-column field="mixedPackageNumber" title="混包号" width='auto' fixed="left"></vxe-column>
        <vxe-column field="packageNumber" title="铁水包号" width='auto' fixed="left"></vxe-column>
        <vxe-column field="packageTaskNumber" title="铁水包任务号" width='auto' fixed="left"></vxe-column>
        <vxe-column field="estimateWeight" title="铁水包估重" width='auto'fixed="left" ></vxe-column>
        <vxe-column field="converterLadleTime" title="转炉兑铁时间" width='auto' :edit-render="{name: 'input'}" fixed="left"></vxe-column>
        <vxe-column field="backCanStationArriveTime" title="倒灌站到达时间" width='auto'></vxe-column>
        <vxe-column field="ladleTime" title="兑铁包时间" width='auto'></vxe-column>
        <vxe-column field="canBlockAdd" title="铁包压块加入量(吨)" width='auto'></vxe-column>
        <vxe-column field="highestPhtsicalHeat" title="铁水温度" width='auto'></vxe-column>
        <vxe-column field="canStatus1" title="鱼雷罐状态" width='auto'></vxe-column>
        <vxe-column field="steelScrapAmount1" title="废钢加入量(吨)" width='auto'></vxe-column>
        <vxe-column field="canStartIronTime" title="出铁开始时间" width='auto'></vxe-column>
        <vxe-column field="measuredTemperature" title="实测温度" width='auto' ></vxe-column>
        <vxe-column field="forecastLadlePhtsicalHeat" title="预测兑铁物理热" width='auto' fixed="right"></vxe-column>
        <vxe-column field="si" title="Si" width='auto' fixed="right"></vxe-column>
        <vxe-column field="mn" title="Mn" width='auto' fixed="right"></vxe-column>
        <vxe-column field="p" title="P" width='auto' fixed="right"></vxe-column>
        <vxe-column field="s" title="S" width='auto' fixed="right"></vxe-column>
        <vxe-column field="ti" title="Ti" width='auto' fixed="right"></vxe-column>
      </vxe-table>
      <vxe-pager
        :current-page.sync="mainTableConfig.pageConfig.pageNum"
        :page-size.sync="mainTableConfig.pageConfig.pageSize"
        :total="mainTableConfig.pageConfig.total"
        @page-change="pageChange">
      </vxe-pager>

    </div>
  </div>
</template>

<script>
  import dayjs from "dayjs";
  import {
    listIronWeightAndPackAgeWeight,
    calculatedWenDuMethod,
    ironWeightInputEdit,
    calculatedBackageaWeight,
  } from "@/api/report/preview/blastFurnace/converter";

  export default {
    name: "converter",
    data() {
      return {
        converterLadleTimeStatusList:[
          {label: '已兑铁', value: '已兑铁'},
          {label: '未兑铁', value: '未兑铁'}
        ],
        loading:true,
        // 转炉兑铁时间
        converterLadleTimeArr:[],
        activeNameCan: '罐数据',
        activeName: '化学成分',
        mainTableConfig: {
          tableData: [],
          selectVO: '',
          pageConfig: {
            pageNum: 1, // 页码
            pageSize: 10, // 每页显示条目个数
            total: 0, // 总数
            background: true, // 是否展示分页器背景色
            pageSizes: [10, 20, 50, 100]// 分页器分页待选项
          }
        },
        selectParam: {
          packageTaskNumber: '',
          mixedCanNumber: '',
          converterLadleTimeStart:'',
          converterLadleTimeEnd:'',
          converterLadleTimeStatus:'未兑铁',
          statusFlag:'',
        },
        // 业务时间
        busiNessTimeArr: [],
        // 显示提交条件
        showSubmit: true,
        // 开始时间
        startIronTime: '',
        // 见渣时间
        slagTime: '',
        // 堵口时间
        plugTime: '',
        ironNumber: '',
        teamGroup: '',
        teamClass: '',
        tableData: [],
        // 非多个禁用
        multiple: true,
        // 非单个禁用
        single: true,
        // 选中数组
        ids: [],
        // 鱼雷罐状态
        canStatusEditRender: {
          name: 'VxeSelect',
          options: [
            {label: '冷罐/修补罐', value: '冷罐/修补罐'},
            {label: '新罐', value: '新罐'},
            {label: '周转罐', value: '周转罐'}
          ]
        },
        // 化学成分
        LiQuidIronTableData: [],
        staveNoList: [{
          value: '1',
          label: '1高炉'
        }, {
          value: '2',
          label: '2高炉'
        }],
        tableDataCan: [],
        backCanFalgList: [{
          value: '已到达',
          label: '已到达'
        }, {
          value: '未到达',
          label: '未到达'
        }],
        formItems: [
          {
            packageNumber: '',
            estimateWeight: '',
          }
        ],
        // 增加新包标识
        submitAddPackAgeFlag: false,
        checkboxData: {},
        // 混包使用
        packageTaskNumberList: [],
        // 计算温度标识
        calculatedWenDuFlag: false,
        ironWeightInputCloseObj:{},
        ironWeightInputCloseArr:[],
        elementSi:'',
        elementP:'',
        elementMn:'',
        elementS:'',
        elementTi:'',
        elementIronNumber:'',

      }
    },
    mounted() {
      window.addEventListener('scroll', this.handleScroll);
    },
    beforeDestroy() {
      window.removeEventListener('scroll', this.handleScroll);
    },
    created() {
      this.converterLadleTimeArr.push(dayjs(new Date()).add(-1, "day"));
      this.converterLadleTimeArr.push(dayjs(new Date()).add(1, "day"));
      // 展示页面数据
      this.queryLists();
    },
    methods: {
      /*  获取路径 状态标识*/
      getStatusFlag() {
        return this.$route.query.statusFlag;
      },

      handleCellClickTime({row, column}) {
        if (column.property === 'converterLadleTime') { // 判断是否点击的是时间列
          if(row.converterLadleTime == null){
            row.converterLadleTime = this.getCurrentTime(); // 获取当前时间并更新到行数据中
          }
          this.calculatedWenDuFlag = true;
        }
      },
      getCurrentTime() {
        return dayjs(new Date()).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      },
      /*搜索按钮*/
      handleQuery() {
        if (this.converterLadleTimeArr.length == 2) {
          this.selectParam.converterLadleTimeStart = dayjs(this.converterLadleTimeArr[0]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
          this.selectParam.converterLadleTimeEnd = dayjs(this.converterLadleTimeArr[1]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
        }
        this.queryLists();
      },
      queryLists() {
        if (this.converterLadleTimeArr.length == 2) {
          this.selectParam.converterLadleTimeStart = dayjs(this.converterLadleTimeArr[0]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
          this.selectParam.converterLadleTimeEnd = dayjs(this.converterLadleTimeArr[1]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
        }
        this.selectParam.statusFlag = this.getStatusFlag();
        listIronWeightAndPackAgeWeight(this.selectParam).then(response => {
          if(response.rows != []){
            for(let  i=0; i<response.rows.length;i++){
              if(response.rows[i].ironNumber == this.elementIronNumber){
                response.rows[i].si = this.elementSi
                response.rows[i].p = this.elementP
                response.rows[i].mn = this.elementMn
                response.rows[i].s = this.elementS
                response.rows[i].ti = this.elementTi
              }
            }
            this.tableData = response.rows
          }
          this.loading = false
          this.mainTableConfig.pageConfig.total = response.total
        });
      },

      pageChange({pageSize, currentPage}) {
        this.mainTableConfig.pageConfig.pageNum = currentPage
        this.mainTableConfig.pageConfig.pageSize = pageSize
        this.selectParam.pageNum = this.mainTableConfig.pageConfig.pageNum
        this.selectParam.pageSize = this.mainTableConfig.pageConfig.pageSize
        this.queryLists()
      },

      /*关闭编辑框触发 失去焦点 进行保存数据*/
      editClosedEvent(row, column) {
        row.row.ironTemperature = row.row.highestPhtsicalHeat
        this.ironWeightInputCloseObj = row.row
        this.ironWeightInputCloseArr.push(this.ironWeightInputCloseObj)

      },
      /* 保存按钮 */
      handleSave(){
        if(this.ironWeightInputCloseArr != []){
          for (let i=0;i<this.ironWeightInputCloseArr.length;i++){
            ironWeightInputEdit(this.ironWeightInputCloseArr[i]).then(response => {
              calculatedWenDuMethod(this.ironWeightInputCloseArr[i]).then(response => {
                this.queryLists()
              });
            });
            // 计算包的权重
            calculatedBackageaWeight(this.ironWeightInputCloseArr[i]).then(response=>{
              console.log("calculatedBackageaWeight response:",JSON.stringify(response))
              this.elementSi = response.data.si
              this.elementP = response.data.p
              this.elementMn = response.data.mn
              this.elementS = response.data.s
              this.elementTi = response.data.ti
              this.elementIronNumber = response.data.ironNumber
              this.queryLists()
            })
          }
          this.$modal.msgSuccess("保存成功");
        }

      },
      // tab 切换
      handleClick(tab, event) {
        console.log(tab, event);
      },
      /*多选框触发事件*/
      handleCheckboxChange({records, rowIndex, row, checked}) {
        this.checkboxData = row
        console.log("row:", row)
      }


    },
  }


</script>


<style scoped>

</style>
