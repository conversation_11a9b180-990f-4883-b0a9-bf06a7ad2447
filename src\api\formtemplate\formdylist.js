import request from '@/utils/request'

// 查询字段模板列表
export function getChart(query, selectVO) {
  return request({
    url: '/api/lingxiao/details/chart',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询表头
export function getcols(pageKey) {
  return request({
    url: '/formtempmes/common/vxetable/getcols',
    method: 'get',
    params: { pageKey: pageKey }
  })
}

// 获取数据
export function gettabledata(pageKey,filter,tbconfig) {
  return request({
    url: '/formtempmes/common/vxetable/gettabledata',
    method: 'get',
    params: { pageKey: pageKey , filter:filter , tbConfig:tbconfig}
  })
}

// 查询表头
export function getcolsdy(pageKey,unitCode,filter) {
  return request({
    url: '/formtempmes/common/vxetable/getcolsdy',
    method: 'get',
    params: { pageKey: pageKey ,unitCode:unitCode, filter:filter }
  })
}

// 获取数据
export function gettabledatady(pageKey,unitCode,filter,tbconfig) {
  return request({
    url: '/formtempmes/common/vxetable/gettabledatady',
    method: 'get',
    params: { pageKey: pageKey ,unitCode:unitCode, filter:filter , tbConfig:tbconfig}
  })
}