<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="物料名称" prop="materialName">
        <el-input v-model="queryParams.materialName" placeholder="请输入物料名称" />
      </el-form-item>
      <el-form-item label="物料编码" prop="materialNumber">
        <el-input v-model="queryParams.materialNumber" placeholder="请输入物料编码" />
      </el-form-item>
      <el-form-item label="扩展分类" prop="expandType">
        <el-select v-model="queryParams.expandType" clearable placeholder="扩展分类" style="width: 240px">
          <el-option v-for="item in dictDataList" :key="item.dictLabel" :label="item.dictValue"
            :value="item.dictLabel" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="queryParams.expandStatus" clearable placeholder="请选择包含内容" style="width: 100%;">
          <el-option v-for="dict in dict.type.record_status" :key="dict.value" :label="dict.label"
            :value="dict.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport">导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <div class="mes_table">
    <el-table size="mini" border v-loading="loading" :data="typeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="物料编号" align="center" prop="materialExpandId" />
      <el-table-column label="物料编码" align="center" prop="materialNumber" :show-overflow-tooltip="true" />
      <el-table-column label="物料名称" align="center" prop="materialName" :show-overflow-tooltip="true" />
      <el-table-column label="扩展分类" align="center" prop="expandType" />
      <el-table-column label="扩展值" align="center" prop="materialValue" />
      <el-table-column label="状态" align="center" prop="expandStatus" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    </div>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="扩展分类" prop="expandType">
          <el-select v-model="form.expandType" placeholder="扩展分类" clearable filterable style="width: 100%;">
            <el-option v-for="item in dictDataList" :key="item.dictLabel" :label="item.dictValue"
              :value="item.dictLabel" />
          </el-select>
        </el-form-item>
        <el-form-item label="物料名称" prop="materialNumber">
          <el-select v-model="form.materialNumber" clearable filterable placeholder="请选择" style="width: 100%;">
            <el-option v-for="item in materialList" :key="item.materialNumber" :label="item.materialName"
              :value="item.materialNumber" v-if="item.tMdMaterialCategory != null" >
              <span style="float: left">{{ item.materialName }}-{{ item.materialNumber }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">

                {{ item.tMdMaterialCategory.shortName }}</span>
            </el-option>
          </el-select>
        </el-form-item>

<!--        <el-form-item label="扩展值" prop="materialValue">-->
<!--          <el-input v-model="form.materialValue" placeholder="请输入扩展值" style="width: 100%;" />-->
<!--        </el-form-item>-->
        <el-form-item label="扩展值" prop="materialValue">
          <el-select v-model="form.materialValue" placeholder="请输入扩展值" style="width: 100%;">
            <el-option v-for="dict in materialValueList" :key="dict.materialValue" :label="dict.materialValue"
                       :value="dict.materialValue"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="expandStatus">
          <el-select v-model="form.expandStatus" placeholder="请选择包含内容" style="width: 100%;">
            <el-option v-for="dict in dict.type.record_status" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" style="width: 100%;" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已存在的物料
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listData, addType, updateType, getType, dictList, delExpand, saveOrUpdate,searchByMateralValue,
  tMdMaterialExpandList} from "@/api/md/materialexpand";
import { queryAllUsed } from "@/api/md/material";
import { getToken } from "@/utils/auth";
import Treeselect from "@riophae/vue-treeselect";
import { treeselect } from "@/api/md/materialCategory";
export default {
  name: "materialexpand",
  dicts: ['form_template_config', 'record_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 字典表格数据
      typeList: [],
      // 物料数据表格
      materialList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialNumber: "",
        materialName: "",
        expandType: "",
        expandStatus: "",
        materialValue: ""
      },

      expandStatusValue: "", // 扩展状态数值
      // 动态设置是否启用编辑模式
      isEditMode: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        materialNumber: [
          { required: true, message: "物料编码不能为空", trigger: "blur" }
        ],
        expandType: [
          { required: true, message: "扩展分类不能为空", trigger: "blur" }
        ],
        expandStatus: [
          { required: true, message: "状态不能为空", trigger: "blur" }
        ],
        materialValue: [
          { required: true, message: "扩展值不能为空", trigger: "blur" }
        ]
      },
      // 字典数据列表
      dictDataList: [],
      materialListNew:[],
      materialValueList:[],
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",

        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/api/md/TMdMaterialExpand/importData"
      },
    };
  },
  created() {
    this.getList();
    queryAllUsed().then((response) => {
      this.materialList = response.data;
    })
    this.dictLists();
    this.searchMateralValue();
  },
  methods: {
    /** 查询表单类型列表 */
    getList() {
      this.loading = true;
      listData(this.queryParams).then(response => {
          // tMdMaterialExpandList(this.queryParams).then((response)=>{
            this.typeList = response.rows;
            this.total = response.total;
            this.loading = false;
          // })
      }
      );
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        materialNumber: "",
        materialName: "",
        expandType: "",
        expandStatus: "生效",
        materialValue: ""
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.isEditMode = true;
      this.open = true;
      this.title = "添加物料类型";
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.materialExpandId)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.isEditMode = true;
      const materialExpandId = row.materialExpandId || this.ids
      getType(materialExpandId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改物料类型";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          saveOrUpdate(this.form).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /* 扩展值下拉框 */
    searchMateralValue(){
      searchByMateralValue().then((rep)=>{
        let obj = {}
        if(rep.data.length>0){
          for(let i=0;i<rep.data.length;i++){
            obj={
              materialValue:rep.data[i].materialValue,
              materialLable:rep.data[i].materialValue
            }
            this.materialValueList.push(obj)
          }
        }
      });
    },


    /** 删除按钮操作 */
    handleDelete(row) {
      const materialExpandIds = row.materialExpandId || this.ids;
      console.log("handleDelete：", materialExpandIds)
      this.$modal.confirm('是否确认删除表单编号为"' + materialExpandIds + '"的数据项？').then(function () {
        return delExpand(materialExpandIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 刷新缓存按钮操作 */
    handleRefreshCache() {
      refreshCache().then(() => {
        this.$modal.msgSuccess("刷新成功");
        this.$store.dispatch('dict/cleanDict');
      });
    },


    /** 获取字典数据列表*/
    dictLists() {
      dictList().then(response => {
        this.dictDataList = response.rows;
      }
      );
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('/api/md/TMdMaterialExpand/export', {
        ...this.queryParams
      }, `expand_${new Date().getTime()}.xlsx`)
    },

    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "物料导入";
      this.upload.open = true;
    },

    /** 下载模板操作 */
    importTemplate() {
      this.download('/api/md/TMdMaterialExpand/importTemplate', {
      }, `物料模板_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

  }
};
</script>
