import request from '@/utils/request'

// 查询存储规则列表
export function list(query) {
  return request({
    url: '/api/summary/workClassPosition/list',
    method: 'get',
    params: query
  })
}

// 查询存储规则详细
export function getEdit(query) {
  return request({
    url: '/api/summary/workClassPosition/getEdit',
    method: 'get',
    params: query
  })
}

export function getDefaultList(query) {
  return request({
    url: '/api/summary/workClassPosition/getDefaultList',
    method: 'get',
    params: query
  })
}

// 新增存储规则
export function saveOrUpdate(data) {
  return request({
    url: '/api/summary/workClassPosition/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 删除存储规则
export function deletePostion(query) {
  return request({
    url: '/api/summary/workClassPosition/deletePostion',
    method: 'delete',
    params: query
  })
}
export function getPostion(query) {
  return request({
    url: '/api/summary/workClassPosition/getPostion',
    method: 'get',
    params: query
  })
}

