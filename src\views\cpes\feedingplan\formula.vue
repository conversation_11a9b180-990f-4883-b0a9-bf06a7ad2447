<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="配料单" name="planCalc">
        <plan-calc
          v-if="activeTab === 'planCalc'"
          :planid="planId"
          :srcplanid="srcPlanId"
          :propProdCode="prodCode"
        />
      </el-tab-pane>
      <el-tab-pane label="block" name="formulaBlock">
        <formula-block
          v-if="activeTab === 'formulaBlock'"
          :planid="planId"
          ref="formulaBlock"
        />
      </el-tab-pane>
      <el-tab-pane label="物料-料仓匹配" name="materialBinMatch">
        <material-bin-match
          v-if="activeTab === 'materialBinMatch'"
          :planid="planId"
          :prodCode="prodCode"
          :dataList2nd= "list2nd"
          ref="materialBinMatch"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import PlanCalc from './planCalc.vue'
import FormulaBlock from './formulaBlock.vue'
import MaterialBinMatch from './materialBinMatch.vue'

export default {
  name: "Formula",
  components: {
    PlanCalc,
    FormulaBlock,
    MaterialBinMatch
  },
  data() {
    return {
      activeTab: 'planCalc',
      // 将props转换为data属性，避免直接修改props
      planId: '',
      srcPlanId: '',
      prodCode: '',
      list2nd: []
    }
  },
  watch: {
    activeTab(newVal) {
      console.log('切换到选项卡:', newVal);
      // 切换选项卡时刷新数据
      this.$nextTick(() => {
        this.refreshData();
      });
    },
    // 监听路由参数变化
    '$route.query': {
      handler(newQuery, oldQuery) {
        console.log('路由参数变化:', oldQuery, '->', newQuery);

        // 重新初始化路由参数
        this.initFromRoute();

        // 刷新数据
        this.$nextTick(() => {
          this.refreshData();
        });
      },
      immediate: false // 不需要立即执行，因为created中已经调用了initFromRoute
    }
  },
  created() {
    // 初始化路由参数
    this.initFromRoute();
  },
  methods: {
    // 从路由参数初始化数据
    initFromRoute() {
      const query = this.$route.query;
      console.log('初始化路由参数:', query);

      // 从路由参数初始化数据属性
      if (query.planId) this.planId = query.planId;
      if (query.srcPlanId) this.srcPlanId = query.srcPlanId;
      if (query.prodCode) this.prodCode = query.prodCode;
      if (query.tab) this.activeTab = query.tab;
 if (query.dataList2nd) {
    try {
      // 如果dataList2nd是字符串形式的JSON，尝试解析
      if (typeof query.dataList2nd === 'string') {
        this.list2nd = JSON.parse(query.dataList2nd);
      } else {
        // 否则直接赋值
        this.list2nd = query.dataList2nd;
      }
    } catch (e) {
      console.error('解析dataList2nd失败:', e);
      this.list2nd = [];
    }
  }
      console.log('初始化后的参数:', {
        planId: this.planId,
        srcPlanId: this.srcPlanId,
        prodCode: this.prodCode,
        activeTab: this.activeTab
      });


    },

    refreshData() {
      // 刷新当前选项卡的数据
      if (this.activeTab === 'planCalc') {
        // 刷新配料单数据
        if (this.$refs.planCalc && this.$refs.planCalc.getList) {
          console.log('刷新配料单数据');
          this.$refs.planCalc.getList();
        }
      } else if (this.activeTab === 'formulaBlock') {
        // 刷新block数据
        if (this.$refs.formulaBlock && this.$refs.formulaBlock.refreshData) {
          console.log('刷新block数据，planId:', this.planId);
          this.$refs.formulaBlock.refreshData();
        }
      } else if (this.activeTab === 'materialBinMatch') {
        // 刷新物料料仓匹配数据
        if (this.$refs.materialBinMatch && this.$refs.materialBinMatch.handleRefresh) {
          console.log('刷新物料料仓匹配数据，planId:', this.planId);
          this.$refs.materialBinMatch.handleRefresh();
        }
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 10px;
  background-color: #f5f7fa;
}

/* 选项卡样式 */
/deep/ .el-tabs__header {
  margin-bottom: 15px;
}

/deep/ .el-tabs__item {
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  font-weight: 500;
}

/deep/ .el-tabs__item.is-active {
  color: #409EFF;
  font-weight: bold;
}

/deep/ .el-tabs__content {
  background-color: #fff;
  border-radius: 0 0 4px 4px;
  padding: 0;
  overflow: visible;
}


.app-container {
  padding-bottom: 20px;
}
</style>