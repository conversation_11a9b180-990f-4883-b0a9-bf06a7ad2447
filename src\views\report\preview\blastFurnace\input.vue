<template>
  <div class="app-container" style="padding: 0px;">

    <div class="block" style="margin-top:8px;">
      <span >高炉号:</span>
      <el-select v-model="selectParam.staveNo" placeholder="请选择" clearable style="width: 200px;margin-left: 10px;"  clearable>
        <el-option v-for="item in staveNoList" :key="item.value" :label="item.label" :value="item.value" ></el-option>
      </el-select>
      <span style=" margin-left: 10px;">混罐号:</span>
      <el-input v-model="selectParam.mixedCan" placeholder="请输入内容"  style="width: 200px;margin-left: 10px;"  clearable></el-input>
      <span style=" margin-left: 10px;">出铁时间:</span>
      <el-date-picker style="margin-left: 10px;width: 392px;"
                      v-model="startIronTimeArr" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
      </el-date-picker>
      <span style=" margin-left: 10px;">转炉兑铁状态:</span>
      <el-select v-model="selectParam.converterLadleTimeStatus" placeholder="请选择" clearable style="width: 200px;margin-left: 10px;" >
        <el-option v-for="item in converterLadleTimeStatusList" :key="item.value" :label="item.label" :value="item.value" ></el-option>
      </el-select>
      <el-button type="primary" style="margin-left: 10px;" @click="handleQuery" size="mini">搜索</el-button>
      <el-button  type="primary" size="mini" @click="submitAddPackAge">新增包号</el-button>
      <el-button  type="primary" size="mini" @click="submitMixedPackAge">混包</el-button>
      <el-button  type="primary" size="mini" @click="submitSave">保存</el-button>
    </div>
    <div style="margin-top: 7px;">
      <vxe-table
        show-overflow
        keep-source
        ref="tableRef"
        :row-config="{keyField: 'id'}"
        :edit-config="{trigger: 'click', mode: 'row', showStatus: true}"
        :loading="loading"
        resizable
        border
        @edit-closed="editClosedEvent"
        :checkbox-config="{checkField: 'isChecked', indeterminateField: 'isIndeterminate'}"
        @checkbox-change="handleCheckboxChange"
        @cell-click="handleCellClickTime"
        :data="tableData">
        <vxe-column type="checkbox" width="auto" fixed="left"></vxe-column>
        <vxe-column field="ironNumber" title="铁次号"  width='auto' fixed="left"></vxe-column>
        <vxe-column field="mixedCanNumber" title="混罐号"  width='auto' fixed="left"></vxe-column>
        <vxe-column field="canNumber1" title="罐号"  width='auto' fixed="left"></vxe-column>
<!--        <vxe-column field="canStartIronTime" title="罐接铁时间"  width='auto' fixed="left"></vxe-column>-->
        <vxe-column field="backCanStationArriveTime" title="倒灌站到达时间" width='auto' fixed="left" :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="packageNumber" title="铁水包号"  width='auto' :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="ladleTime" title="兑铁包时间" width='180' :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="canBlockAdd" title="铁包压块加入量(吨)" width='auto' type="number" :edit-render="{name: 'input'}" ></vxe-column>
        <vxe-column field="converterLadleTime" title="转炉兑铁包时间" width='auto' ></vxe-column>
        <vxe-column field="estimateWeight" title="铁水包估重" width='auto' :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="measuredTemperature" title="实测温度" width='auto' :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="packageTaskNumber" title="铁水包任务号"  width='auto' ></vxe-column>
        <vxe-column field="mixedPackageNumber" title=混包号  width='auto' ></vxe-column>
        <vxe-column field="highestPhtsicalHeat" title="铁水温度"  width='auto'></vxe-column>
        <vxe-column field="canStatus1" title="鱼雷罐状态"  width='auto'></vxe-column>
        <vxe-column field="steelScrapAmount1" title="废钢加入量(吨)" width='auto'  ></vxe-column>
        <vxe-column field="canStartIronTime" title="出铁开始时间"  width='auto' ></vxe-column>

      </vxe-table>
      <vxe-pager
        :current-page.sync="mainTableConfig.pageConfig.pageNum"
        :page-size.sync="mainTableConfig.pageConfig.pageSize"
        :total="mainTableConfig.pageConfig.total"
        @page-change="pageChange">
      </vxe-pager>
    </div>
  </div>
</template>

<script>
  import XEUtils from 'xe-utils';
  import {
    listIronWeightAndPackAgeWeight,
    ironWeightInputEdit,
    insertPackAgeData,
    insertNewPackAgeData,
    mixedPackAgeData,
    calculatedWenDuMethod,
    updateBackCanStationFlagMethod
  } from "@/api/report/preview/blastFurnace/input";
  import dayjs from "dayjs";
  export default {
    name: "Input",
    data() {
      return {
        converterLadleTimeStatusList:[
          {label: '已兑铁', value: '已兑铁'},
          {label: '未兑铁', value: '未兑铁'}
        ],
        // 出铁时间段
        startIronTimeArr:[],
        loading:true,
        activeNameCan: '罐数据',
        activeName:'化学成分',
        mainTableConfig: {
          tableData: [],
          selectVO: '',
          pageConfig: {
            pageNum: 1, // 页码
            pageSize: 10, // 每页显示条目个数
            total: 0, // 总数
            background: true, // 是否展示分页器背景色
            pageSizes: [10, 20, 50, 100]// 分页器分页待选项
          }
        },


        selectParam:{
          ironNumber:'',
          mixedCan:'',
          statusFlag:'',
          converterLadleTimeStatus:'未兑铁',
        },
        // 业务时间
        busiNessTimeArr:[],
        // 显示提交条件
        showSubmit: true,
        // 开始时间
        startIronTime:'',
        // 见渣时间
        slagTime:'',
        // 堵口时间
        plugTime:'',
        ironNumber:'',
        teamGroup:'',
        teamClass:'',
        tableData:[],
        // 非多个禁用
        multiple: true,
        // 非单个禁用
        single: true,
        // 选中数组
        ids: [],
        // 鱼雷罐状态
        canStatusEditRender : {
          name: 'VxeSelect',
          options: [
            { label: '冷罐/修补罐', value: '冷罐/修补罐' },
            { label: '新罐', value: '新罐' },
            { label: '周转罐', value: '周转罐' }
          ]
        },
        // 化学成分
        LiQuidIronTableData:[],
        staveNoList:[{
          value: '1',
          label: '1高炉'
        }, {
          value: '2',
          label: '2高炉'
        }],
        tableDataCan:[],
        backCanFalgList: [{
          value: '已到达',
          label: '已到达'
        }, {
          value: '未到达',
          label: '未到达'
        }],
        formItems: [
          { packageNumber: '',
            estimateWeight: '',
          }
        ],
        // 增加新包标识
        submitAddPackAgeFlag:false,
        checkboxData:{},
        // 混包使用
        packageTaskNumberList:[],
        // 计算温度标识
        calculatedWenDuFlag:false,
        ironWeightInputCloseObj:{},
        ironWeightInputCloseArr:[],

      }
    },
    mounted() {
      window.addEventListener('scroll', this.handleScroll);
    },
    beforeDestroy() {
      window.removeEventListener('scroll', this.handleScroll);
    },
    created(){
      this.startIronTimeArr.push(dayjs(new Date()).add(-1, "day"));
      this.startIronTimeArr.push(dayjs(new Date()).add(1, "day"));
      // 展示页面数据
      this.queryLists();
    },
    methods:{
      /*  获取路径 状态标识*/
      getStatusFlag() {
        return this.$route.query.statusFlag;
      },

      /* 新增包信息 */
      submitAddPackAge(){
        // 点击新增包号按钮  向铁水包表中插入数据
        if(JSON.stringify(this.checkboxData) != '{}'){
          insertNewPackAgeData(this.checkboxData).then(response=> {
            this.queryLists();
          });
        }else{
          return  this.$modal.msgError("未选中混罐号");
        }
      },
      /* 混包 */
      submitMixedPackAge(){
        if(this.packageTaskNumberList.length<=0){
          this.$modal.msgError("未选中包");
          return;
        }
        for(let i=0;i<this.packageTaskNumberList.length;i++){
          console.log("this.packageTaskNumberList:",JSON.stringify(this.packageTaskNumberList[i]))
          if(this.packageTaskNumberList[i].estimateWeight == null || this.packageTaskNumberList[i].estimateWeight == ''){
            this.$modal.msgError("铁水包估重为空");
            return;
          }
        }
        mixedPackAgeData(this.packageTaskNumberList).then(response=>{
          this.$modal.msgSuccess("混包成功");
          this.packageTaskNumberList = []
          this.queryLists()
        });

      },

      submitForm(){},
      handleCellClickTime({ row, column }) {
        console.log("column:",column)
        console.log("row:",row)
        if (column.property === 'backCanStationArriveTime') { // 判断是否点击的是时间列
          if(row.backCanStationArriveTime == null){
            row.backCanStationArriveTime = this.getCurrentTime(); // 获取当前时间并更新到行数据中
          }
          this.calculatedWenDuFlag = true;
          // 修改 铁水罐 倒灌站到站时间标识
          updateBackCanStationFlagMethod(row).then(response=>{

          })
        }
        if (column.property === 'ladleTime') { // 判断是否点击的是时间列
          if(row.ladleTime == null){
            row.ladleTime = this.getCurrentTime(); // 获取当前时间并更新到行数据中
          }
          this.calculatedWenDuFlag = true;
        }

        if (column.property === 'canBlockAdd') { // 判断是否点击的是时间列
          this.calculatedWenDuFlag = true;
        }
        if(this.calculatedWenDuFlag == true){
          calculatedWenDuMethod(row).then(response=>{
            this.calculatedWenDuFlag = false
          });
        }


      },
      getCurrentTime() {
        return  dayjs(new Date()).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      },
      /*搜索按钮*/
      handleQuery(){
        this.queryLists();
      },
      queryLists(){
        if (this.startIronTimeArr.length == 2) {
          this.selectParam.startIronTimeStart = dayjs(this.startIronTimeArr[0]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
          this.selectParam.startIronTimeEnd = dayjs(this.startIronTimeArr[1]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
        }
        this.selectParam.statusFlag = this.getStatusFlag();
        listIronWeightAndPackAgeWeight(this.selectParam).then(response=>{
          this.tableData = response.rows
          console.log("this.tableData：",JSON.stringify(this.tableData))
          this.loading = false
          this.mainTableConfig.pageConfig.total = response.total
        });
      },

      pageChange ({ pageSize, currentPage }) {
        this.mainTableConfig.pageConfig.pageNum = currentPage
        this.mainTableConfig.pageConfig.pageSize = pageSize
        this.selectParam.pageNum=this.mainTableConfig.pageConfig.pageNum
        this.selectParam.pageSize=this.mainTableConfig.pageConfig.pageSize
        this.queryLists()
      },

      /*关闭编辑框触发 失去焦点 进行保存数据*/
      editClosedEvent(row, column) {
        row.row.ironTemperature =  row.row.highestPhtsicalHeat
        this.ironWeightInputCloseObj = row.row
        this.ironWeightInputCloseArr.push(this.ironWeightInputCloseObj)
      },
      /* 保存数据按钮 */
      submitSave(){
        if(this.ironWeightInputCloseArr != []){
          for(let i=0;i<this.ironWeightInputCloseArr.length;i++){
            ironWeightInputEdit(this.ironWeightInputCloseArr[i]).then(response=>{
              this.queryLists()
            });
          }
          this.$modal.msgSuccess("保存成功");
        }

      },
      // tab 切换
      handleClick(tab, event) {
        console.log(tab, event);
      },
      /*多选框触发事件*/
      handleCheckboxChange({ records, rowIndex, row ,checked}) {
        this.checkboxData = row
        console.log("row:",row)

        if(checked  == true){
          let obj ={packageTaskNumber:row.packageTaskNumber,mixedCanNumber:row.mixedCanNumber,estimateWeight:row.estimateWeight}
          this.packageTaskNumberList.push(obj)
        }else{
          const $table = this.$refs.tableRef
          if ($table) {
            $table.clearCheckboxRow()
          }
          this.packageTaskNumberList = []
        }
      }


    },

  }
</script>

<style lang="scss" scoped>

  .el-input el-input--suffix{
    width: 10px;
  }

  .scrollable-container {
    width: 374px; /* 定义宽度 */
    height: 498px; /* 定义高度 */
    overflow: auto; /* 启用滚动条 */
    margin-top: -575px;
    margin-left: 1221px;
    z-index: auto;
    position: fixed;
  }


</style>
