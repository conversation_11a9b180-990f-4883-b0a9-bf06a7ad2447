<template>
    <div class="app-container">
        <vxe-grid ref="gridRef" v-bind="gridOptions" @page-change="pageChangeEvent">
            <!-- 查询表单 -->
            <template #form>
                <vxe-form ref="searchFormRef" v-bind="searchFormOptions">
                    <template #action>
                        <vxe-button status="primary" content="搜索" @click="searchEvent"></vxe-button>
                        <vxe-button content="重置" @click="resetEvent"></vxe-button>
                    </template>
                </vxe-form>
            </template>

            <!-- 工具栏 -->
            <template #toolbarButtons>
                <vxe-button status="primary" @click="addEvent">新增</vxe-button>
            </template>

            <template #active="{ row }">
                <vxe-button mode="button" status="success" @click="saveRow(row)">保存</vxe-button>
                <vxe-button mode="button" status="error" @click="removeRow(row)">删除</vxe-button>
            </template>
        </vxe-grid>
    </div>
</template>

<script>
import { VxeUI } from 'vxe-pc-ui'
import { listStorehouse, saveOrUpdate, deleteStorehouse, listProdCenter } from "@/api/wms/storehouse";

export default {
    name: 'storehouse',
    data() {

        const prodCenterEditRender = {
            name: 'VxeSelect',
            options: []
        }

        const gridOptions = {
            columns: [
                { type: 'checkbox', width: 50 },
                { field: 'seq', type: 'seq', width: 50 },
                { field: 'storehouseId', title: 'storehouseId', visible: false },
                { field: 'prodCenterCode', title: '加工中心', editRender: prodCenterEditRender },
                { field: 'storehouseCode', title: '仓库编码', editRender: { name: 'VxeInput' } },
                { field: 'storehouseName', title: '仓库名称', editRender: { name: 'VxeInput' } },
                { field: 'storehouseStatus', title: '仓库状态', },
                { field: 'remark', title: '备注', editRender: { name: 'VxeInput' } },
                { field: 'active', title: '操作', fixed: 'right', slots: { default: 'active' } }
            ],
            data: [],

            border: true,
            strip: true,
            align: 'center',
            showOverflow: true,
            keepSource: true,
            loading: false,

            columnConfig: {
                resizable: true
            },
            rowConfig: {
                isHover: true,
                isCurrent: true,
            },
            pagerConfig: {
                total: 0,
                currentPage: 1,
                pageSize: 10
            },
            toolbarConfig: {
                // custom: true,
                zoom: true,
                slots: {
                    buttons: 'toolbarButtons'
                }
            },
            editConfig: {
                mode: 'row',
                trigger: 'dblclick',
                showStatus: true,
            },
            editRules: {
                prodCenterCode: [
                    { required: true }
                ],
                storehouseCode: [
                    { required: true }
                ],
                storehouseName: [
                    { required: true }
                ]
            },
        }

        const searchFormOptions = {
            data: {
                storehouseName: '',
            },
            items: [
                { field: 'storehouseName', title: '仓库名称', itemRender: { name: 'VxeInput' } },
                { slots: { default: 'action' } }
            ]
        }

        return {
            gridOptions,
            searchFormOptions,
            prodCenterEditRender,

        }
    },
    methods: {
        searchGridList() {
            this.handlePageData()
        },
        handlePageData() {
            this.gridOptions.loading = true
            listStorehouse(this.searchFormOptions.data).then(response => {
                let data = response.data
                const { pageSize, currentPage } = this.gridOptions.pagerConfig
                this.gridOptions.pagerConfig.total = data.length
                this.gridOptions.data = data.slice((currentPage - 1) * pageSize, currentPage * pageSize)
                this.gridOptions.loading = false
            })
        },
        pageChangeEvent({ pageSize, currentPage }) {
            this.gridOptions.pagerConfig.currentPage = currentPage
            this.gridOptions.pagerConfig.pageSize = pageSize
            this.handlePageData()
        },
        async saveRow(row) {
            const $grid = this.$refs.gridRef
            if ($grid) {
                const errMap = await $grid.validate(true)
                if (errMap) {
                    VxeUI.modal.message({ status: 'error', content: '校验不通过！' })
                    return
                }
                const { insertRecords, updateRecords } = $grid.getRecordset()

                if (insertRecords.length === 1) {
                    // VxeUI.modal.message({ status: 'success', content: '新增成功' })
                    saveOrUpdate(insertRecords[0]).then(response => {
                        this.searchGridList()
                        VxeUI.modal.message({
                            content: '新增成功',
                            status: 'success'
                        })
                    })
                }

                if (updateRecords.length === 1) {
                    saveOrUpdate(updateRecords[0]).then(response => {
                        this.searchGridList()
                        VxeUI.modal.message({
                            content: '修改成功',
                            status: 'success'
                        })
                    })
                }
            }
        },
        removeRow(row) {
            VxeUI.modal.confirm({
                content: '是否确定删除',
            }).then(type => {
                // console.log(`操作类型 ${type}`)
                if (type === 'confirm') {
                    const $grid = this.$refs.gridRef
                    if ($grid) {
                        if (row.storehouseId === null) {
                            $grid.remove(row)
                        } else {
                            deleteStorehouse(row.storehouseId).then(response => {
                                this.searchGridList()
                            })
                        }
                    }
                }
            })
        },

        searchEvent() {
            this.searchGridList()
        },
        resetEvent() {
            const $form = this.$refs.searchFormRef
            if ($form) {
                $form.reset()
                this.searchGridList()
            }
        },

        async addEvent() {
            const $grid = this.$refs.gridRef
            if ($grid) {
                const record = {}
                const { row: newRow } = await $grid.insertAt(record, null)
                await $grid.setEditRow(newRow)
            }
        },


        initData() {
            listProdCenter().then(response => {
                let data = response.data
                let list = []
                for (let i = 0; i < data.length; i++) {
                    list.push({
                        value: `${data[i].prodCenterCode}`,
                        label: `${data[i].prodCenterName}`
                    })
                }
                this.prodCenterEditRender.options = list
            })
        },

    },
    created() {
        this.initData()
    },
    mounted() {
        this.searchGridList()
    }
}
</script>