<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas堆取料机绘制</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(to bottom, #87CEEB 0%, #B0E0E6 50%, #F0F8FF 100%);
            font-family: Arial, sans-serif;
            min-height: 100vh;
        }
        .container {
            text-align: center;
            max-width: 1000px;
            margin: 0 auto;
        }
        canvas {
            border: 2px solid #333;
            background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 70%, #8FBC8F 100%);
            border-radius: 10px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.4);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .controls {
            margin-top: 20px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Canvas堆取料机绘制</h1>
        <canvas id="excavatorCanvas" width="800" height="600"></canvas>
        <div class="controls">
            <button onclick="drawStackerReclaimer()">重新绘制堆取料机</button>
            <button onclick="animateStackerReclaimer()">堆取料机动画</button>
            <button onclick="clearCanvas()">清空画布</button>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('excavatorCanvas');
        const ctx = canvas.getContext('2d');

        let animationId;
        let boomAngle = 0;
        let conveyorOffset = 0;
        let isAnimating = false;

        // 创建渐变色函数
        function createGradient(x1, y1, x2, y2, color1, color2, color3 = null) {
            const gradient = ctx.createLinearGradient(x1, y1, x2, y2);
            gradient.addColorStop(0, color1);
            if (color3) {
                gradient.addColorStop(0.5, color2);
                gradient.addColorStop(1, color3);
            } else {
                gradient.addColorStop(1, color2);
            }
            return gradient;
        }

        // 创建径向渐变
        function createRadialGradient(x, y, r1, r2, color1, color2) {
            const gradient = ctx.createRadialGradient(x, y, r1, x, y, r2);
            gradient.addColorStop(0, color1);
            gradient.addColorStop(1, color2);
            return gradient;
        }

        // 添加光照效果
        function addLighting(x, y, width, height, intensity = 0.3) {
            const lightGradient = createRadialGradient(x + width * 0.3, y + height * 0.2, 0, Math.max(width, height),
                `rgba(255, 255, 255, ${intensity})`, 'rgba(255, 255, 255, 0)');
            ctx.fillStyle = lightGradient;
            ctx.fillRect(x, y, width, height);
        }

        // 添加阴影效果
        function addShadow(x, y, width, height, blur = 5, offsetX = 2, offsetY = 2) {
            ctx.save();
            ctx.shadowColor = 'rgba(0, 0, 0, 0.4)';
            ctx.shadowBlur = blur;
            ctx.shadowOffsetX = offsetX;
            ctx.shadowOffsetY = offsetY;
            ctx.fillRect(x, y, width, height);
            ctx.restore();
        }

        // 绘制金属纹理
        function drawMetalTexture(x, y, width, height, baseColor, highlightColor) {
            // 基础金属色
            ctx.fillStyle = baseColor;
            ctx.fillRect(x, y, width, height);

            // 金属高光
            const metalGradient = createGradient(x, y, x, y + height, highlightColor, baseColor, baseColor);
            ctx.fillStyle = metalGradient;
            ctx.fillRect(x, y, width, height);

            // 金属纹理线条
            ctx.strokeStyle = highlightColor;
            ctx.lineWidth = 0.5;
            for (let i = 0; i < width; i += 3) {
                ctx.globalAlpha = 0.3;
                ctx.beginPath();
                ctx.moveTo(x + i, y);
                ctx.lineTo(x + i, y + height);
                ctx.stroke();
            }
            ctx.globalAlpha = 1;
        }

        // 绘制堆取料机函数
        function drawStackerReclaimer() {
            clearCanvas();

            // 绘制地面和料堆
            drawGroundAndMaterial();

            // 绘制轨道
            drawRails();

            // 绘制底盘台车
            drawTrolley();

            // 绘制主塔架
            drawMainTower();

            // 绘制悬臂
            drawBoom();

            // 绘制输送带系统
            drawConveyorSystem();

            // 绘制堆料装置
            drawStackingDevice();

            // 绘制取料装置
            drawReclaimingDevice();

            // 绘制配重
            drawCounterweight();

            // 绘制细节
            drawDetails();

            // 添加阴影效果
            drawShadows();
        }

        // 绘制地面和料堆
        function drawGroundAndMaterial() {
            // 地面基础层
            const groundGradient = createGradient(0, 480, 0, 600, '#8B7355', '#654321', '#4A3728');
            ctx.fillStyle = groundGradient;
            ctx.fillRect(0, 480, canvas.width, 120);

            // 绘制大型料堆（煤炭堆）
            drawMaterialPile(100, 480, 150, 80, '#2F2F2F', '#1A1A1A'); // 煤炭堆
            drawMaterialPile(300, 480, 120, 60, '#8B4513', '#654321'); // 矿石堆
            drawMaterialPile(500, 480, 180, 90, '#2F2F2F', '#1A1A1A'); // 另一个煤炭堆

            // 地面纹理
            ctx.fillStyle = 'rgba(139, 115, 85, 0.3)';
            for (let i = 0; i < canvas.width; i += 8) {
                for (let j = 480; j < 600; j += 8) {
                    if (Math.random() > 0.8) {
                        ctx.fillRect(i, j, 3, 3);
                    }
                }
            }
        }

        // 绘制料堆
        function drawMaterialPile(x, y, width, height, color1, color2) {
            // 料堆阴影
            ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
            ctx.beginPath();
            ctx.ellipse(x + width/2 + 5, y + 5, width/2, height/4, 0, 0, 2 * Math.PI);
            ctx.fill();

            // 料堆主体
            const pileGradient = createRadialGradient(x + width*0.3, y - height*0.3, 0, width, color1, color2);
            ctx.fillStyle = pileGradient;
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.quadraticCurveTo(x + width/2, y - height, x + width, y);
            ctx.lineTo(x + width, y);
            ctx.lineTo(x, y);
            ctx.fill();

            // 料堆纹理
            ctx.fillStyle = color2;
            for (let i = 0; i < 20; i++) {
                const px = x + Math.random() * width;
                const py = y - Math.random() * height * 0.8;
                const size = 1 + Math.random() * 3;
                ctx.fillRect(px, py, size, size);
            }

            // 料堆高光
            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.beginPath();
            ctx.moveTo(x + width*0.2, y - height*0.8);
            ctx.quadraticCurveTo(x + width*0.4, y - height*0.9, x + width*0.6, y - height*0.7);
            ctx.quadraticCurveTo(x + width*0.3, y - height*0.5, x + width*0.2, y - height*0.8);
            ctx.fill();
        }

        // 绘制轨道
        function drawRails() {
            // 轨道基础
            ctx.fillStyle = '#696969';
            ctx.fillRect(0, 470, canvas.width, 10);

            // 钢轨
            const railGradient = createGradient(0, 472, 0, 478, '#C0C0C0', '#808080');
            ctx.fillStyle = railGradient;
            ctx.fillRect(0, 472, canvas.width, 3);
            ctx.fillRect(0, 477, canvas.width, 3);

            // 轨枕
            ctx.fillStyle = '#8B4513';
            for (let i = 0; i < canvas.width; i += 25) {
                ctx.fillRect(i, 470, 20, 10);

                // 轨枕阴影
                ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
                ctx.fillRect(i + 1, 471, 20, 10);
                ctx.fillStyle = '#8B4513';
            }

            // 道钉
            ctx.fillStyle = '#2F2F2F';
            for (let i = 5; i < canvas.width; i += 25) {
                ctx.fillRect(i, 473, 2, 2);
                ctx.fillRect(i + 10, 473, 2, 2);
                ctx.fillRect(i, 478, 2, 2);
                ctx.fillRect(i + 10, 478, 2, 2);
            }
        }

        // 绘制底盘台车
        function drawTrolley() {
            // 台车阴影
            ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
            ctx.fillRect(352, 442, 120, 30);

            // 台车主体
            drawMetalTexture(350, 440, 120, 30, '#4682B4', '#6495ED');
            ctx.strokeStyle = '#2F4F4F';
            ctx.lineWidth = 3;
            ctx.strokeRect(350, 440, 120, 30);

            // 台车轮组
            const trolleyWheels = [
                {x: 365, y: 470, size: 12},
                {x: 385, y: 470, size: 12},
                {x: 425, y: 470, size: 12},
                {x: 445, y: 470, size: 12}
            ];

            trolleyWheels.forEach(wheel => {
                // 轮子阴影
                ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
                ctx.beginPath();
                ctx.arc(wheel.x + 1, wheel.y + 1, wheel.size, 0, 2 * Math.PI);
                ctx.fill();

                // 轮子主体
                const wheelGradient = createRadialGradient(wheel.x - 3, wheel.y - 3, 0, wheel.size, '#C0C0C0', '#808080');
                ctx.fillStyle = wheelGradient;
                ctx.beginPath();
                ctx.arc(wheel.x, wheel.y, wheel.size, 0, 2 * Math.PI);
                ctx.fill();

                // 轮子边框
                ctx.strokeStyle = '#4A4A4A';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(wheel.x, wheel.y, wheel.size, 0, 2 * Math.PI);
                ctx.stroke();
            });

            // 台车支撑结构
            ctx.strokeStyle = '#2F4F4F';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(360, 440);
            ctx.lineTo(360, 460);
            ctx.moveTo(440, 440);
            ctx.lineTo(440, 460);
            ctx.stroke();
        }

        // 绘制主塔架
        function drawMainTower() {
            // 塔架阴影
            ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
            ctx.fillRect(407, 152, 25, 290);

            // 主塔架
            drawMetalTexture(405, 150, 25, 290, '#FF6B47', '#FF4500');
            ctx.strokeStyle = '#CD5C5C';
            ctx.lineWidth = 3;
            ctx.strokeRect(405, 150, 25, 290);

            // 塔架加强筋
            ctx.strokeStyle = '#8B0000';
            ctx.lineWidth = 2;
            for (let i = 0; i < 8; i++) {
                const y = 160 + i * 35;
                ctx.beginPath();
                ctx.moveTo(405, y);
                ctx.lineTo(430, y);
                ctx.stroke();
            }

            // 塔架斜撑
            ctx.strokeStyle = '#CD5C5C';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(405, 200);
            ctx.lineTo(380, 250);
            ctx.moveTo(430, 200);
            ctx.lineTo(455, 250);
            ctx.moveTo(405, 300);
            ctx.lineTo(380, 350);
            ctx.moveTo(430, 300);
            ctx.lineTo(455, 350);
            ctx.stroke();

            // 旋转机构
            const rotationGradient = createRadialGradient(417, 440, 0, 30, '#FFD700', '#DAA520');
            ctx.fillStyle = rotationGradient;
            ctx.beginPath();
            ctx.arc(417, 440, 30, 0, 2 * Math.PI);
            ctx.fill();

            ctx.strokeStyle = '#B8860B';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(417, 440, 30, 0, 2 * Math.PI);
            ctx.stroke();

            // 旋转机构细节
            ctx.fillStyle = '#2F2F2F';
            ctx.beginPath();
            ctx.arc(417, 440, 15, 0, 2 * Math.PI);
            ctx.fill();

            // 操作室
            drawMetalTexture(395, 180, 45, 40, '#4682B4', '#6495ED');
            ctx.strokeStyle = '#2F4F4F';
            ctx.lineWidth = 2;
            ctx.strokeRect(395, 180, 45, 40);

            // 操作室窗户
            ctx.fillStyle = 'rgba(176, 224, 230, 0.7)';
            ctx.fillRect(400, 185, 35, 15);
            ctx.fillRect(400, 205, 35, 10);

            // 窗框
            ctx.strokeStyle = '#2F2F2F';
            ctx.lineWidth = 1;
            ctx.strokeRect(400, 185, 35, 15);
            ctx.strokeRect(400, 205, 35, 10);
        }

        // 绘制悬臂
        function drawBoom() {
            ctx.save();

            // 悬臂连接点
            const pivotX = 417;
            const pivotY = 200;

            ctx.translate(pivotX, pivotY);
            ctx.rotate(boomAngle);

            // 悬臂阴影
            ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
            ctx.fillRect(2, -12, 300, 25);

            // 悬臂主体
            drawMetalTexture(0, -12, 300, 25, '#FF6B47', '#FF4500');
            ctx.strokeStyle = '#CD5C5C';
            ctx.lineWidth = 3;
            ctx.strokeRect(0, -12, 300, 25);

            // 悬臂桁架结构
            ctx.strokeStyle = '#8B0000';
            ctx.lineWidth = 2;
            for (let i = 0; i < 10; i++) {
                const x = i * 30;
                ctx.beginPath();
                ctx.moveTo(x, -12);
                ctx.lineTo(x + 15, 0);
                ctx.lineTo(x + 30, -12);
                ctx.moveTo(x + 15, 0);
                ctx.lineTo(x + 15, 13);
                ctx.stroke();
            }

            // 悬臂上弦杆
            ctx.strokeStyle = '#CD5C5C';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(0, -12);
            ctx.lineTo(300, -12);
            ctx.stroke();

            // 悬臂下弦杆
            ctx.beginPath();
            ctx.moveTo(0, 13);
            ctx.lineTo(300, 13);
            ctx.stroke();

            // 悬臂端部滑轮组
            const pulleyGradient = createRadialGradient(295, 0, 0, 15, '#C0C0C0', '#808080');
            ctx.fillStyle = pulleyGradient;
            ctx.beginPath();
            ctx.arc(295, 0, 15, 0, 2 * Math.PI);
            ctx.fill();

            ctx.strokeStyle = '#4A4A4A';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(295, 0, 15, 0, 2 * Math.PI);
            ctx.stroke();

            // 滑轮组细节
            for (let i = 0; i < 6; i++) {
                const angle = (i * Math.PI) / 3;
                ctx.beginPath();
                ctx.moveTo(295, 0);
                ctx.lineTo(295 + Math.cos(angle) * 10, Math.sin(angle) * 10);
                ctx.stroke();
            }

            ctx.restore();
        }

        // 绘制输送带系统
        function drawConveyorSystem() {
            // 主输送带（从悬臂到塔架）
            ctx.strokeStyle = '#2F2F2F';
            ctx.lineWidth = 8;
            ctx.beginPath();
            ctx.moveTo(417 + Math.cos(boomAngle) * 150, 200 + Math.sin(boomAngle) * 150);
            ctx.lineTo(417, 200);
            ctx.stroke();

            // 输送带边缘
            ctx.strokeStyle = '#1A1A1A';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(417 + Math.cos(boomAngle) * 150, 200 + Math.sin(boomAngle) * 150 - 4);
            ctx.lineTo(417, 196);
            ctx.moveTo(417 + Math.cos(boomAngle) * 150, 200 + Math.sin(boomAngle) * 150 + 4);
            ctx.lineTo(417, 204);
            ctx.stroke();

            // 输送带运动效果
            ctx.strokeStyle = '#4A4A4A';
            ctx.lineWidth = 1;
            for (let i = 0; i < 10; i++) {
                const offset = (conveyorOffset + i * 20) % 150;
                const x = 417 + Math.cos(boomAngle) * offset;
                const y = 200 + Math.sin(boomAngle) * offset;
                ctx.beginPath();
                ctx.moveTo(x - 2, y);
                ctx.lineTo(x + 2, y);
                ctx.stroke();
            }

            // 垂直输送带（塔架内部）
            ctx.strokeStyle = '#2F2F2F';
            ctx.lineWidth = 6;
            ctx.beginPath();
            ctx.moveTo(417, 200);
            ctx.lineTo(417, 440);
            ctx.stroke();

            // 输送带支撑滚筒
            const drums = [
                {x: 417, y: 200, size: 8},
                {x: 417, y: 280, size: 6},
                {x: 417, y: 360, size: 6},
                {x: 417, y: 440, size: 10}
            ];

            drums.forEach(drum => {
                const drumGradient = createRadialGradient(drum.x - 2, drum.y - 2, 0, drum.size, '#C0C0C0', '#808080');
                ctx.fillStyle = drumGradient;
                ctx.beginPath();
                ctx.arc(drum.x, drum.y, drum.size, 0, 2 * Math.PI);
                ctx.fill();

                ctx.strokeStyle = '#4A4A4A';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.arc(drum.x, drum.y, drum.size, 0, 2 * Math.PI);
                ctx.stroke();
            });
        }

        // 绘制堆料装置
        function drawStackingDevice() {
            // 计算堆料装置位置（悬臂末端）
            const endX = 417 + Math.cos(boomAngle) * 300;
            const endY = 200 + Math.sin(boomAngle) * 300;

            ctx.save();
            ctx.translate(endX, endY);
            ctx.rotate(boomAngle);

            // 堆料漏斗
            const funnelGradient = createGradient(-15, -20, 15, 20, '#FFD700', '#DAA520');
            ctx.fillStyle = funnelGradient;
            ctx.beginPath();
            ctx.moveTo(-15, -20);
            ctx.lineTo(15, -20);
            ctx.lineTo(10, 0);
            ctx.lineTo(-10, 0);
            ctx.closePath();
            ctx.fill();

            // 漏斗边框
            ctx.strokeStyle = '#B8860B';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(-15, -20);
            ctx.lineTo(15, -20);
            ctx.lineTo(10, 0);
            ctx.lineTo(-10, 0);
            ctx.closePath();
            ctx.stroke();

            // 堆料出口
            ctx.fillStyle = '#2F2F2F';
            ctx.fillRect(-5, 0, 10, 8);

            // 堆料控制阀门
            const valveGradient = createRadialGradient(0, 4, 0, 6, '#C0C0C0', '#808080');
            ctx.fillStyle = valveGradient;
            ctx.beginPath();
            ctx.arc(0, 4, 6, 0, 2 * Math.PI);
            ctx.fill();

            ctx.restore();
        }

        // 绘制取料装置
        function drawReclaimingDevice() {
            // 计算取料装置位置（悬臂末端下方）
            const endX = 417 + Math.cos(boomAngle) * 280;
            const endY = 200 + Math.sin(boomAngle) * 280 + 30;

            ctx.save();
            ctx.translate(endX, endY);
            ctx.rotate(boomAngle);

            // 取料刮板
            drawMetalTexture(-25, -8, 50, 16, '#FF6B47', '#FF4500');
            ctx.strokeStyle = '#CD5C5C';
            ctx.lineWidth = 2;
            ctx.strokeRect(-25, -8, 50, 16);

            // 刮板齿
            ctx.fillStyle = '#2F2F2F';
            for (let i = 0; i < 8; i++) {
                const x = -20 + i * 5;
                ctx.beginPath();
                ctx.moveTo(x, 8);
                ctx.lineTo(x + 2, 15);
                ctx.lineTo(x - 2, 15);
                ctx.closePath();
                ctx.fill();
            }

            // 取料链条
            ctx.strokeStyle = '#4A4A4A';
            ctx.lineWidth = 3;
            for (let i = 0; i < 5; i++) {
                const y = -20 + i * 8;
                ctx.beginPath();
                ctx.moveTo(-2, y);
                ctx.lineTo(2, y);
                ctx.stroke();
            }

            // 取料驱动轮
            const driveGradient = createRadialGradient(-2, -25, 0, 8, '#C0C0C0', '#808080');
            ctx.fillStyle = driveGradient;
            ctx.beginPath();
            ctx.arc(0, -25, 8, 0, 2 * Math.PI);
            ctx.fill();

            ctx.strokeStyle = '#4A4A4A';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.arc(0, -25, 8, 0, 2 * Math.PI);
            ctx.stroke();

            ctx.restore();
        }

        // 绘制配重
        function drawCounterweight() {
            // 配重位置（塔架后方）
            const counterweightGradient = createGradient(320, 300, 360, 400, '#708090', '#2F4F4F');
            ctx.fillStyle = counterweightGradient;
            ctx.beginPath();
            ctx.roundRect(320, 300, 40, 100, 5);
            ctx.fill();

            // 配重阴影
            ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
            ctx.fillRect(322, 302, 40, 100);

            // 配重纹理和加强筋
            ctx.strokeStyle = '#1C1C1C';
            ctx.lineWidth = 2;
            for (let i = 0; i < 8; i++) {
                ctx.beginPath();
                ctx.moveTo(325, 310 + i * 12);
                ctx.lineTo(355, 310 + i * 12);
                ctx.stroke();
            }

            // 配重块细节
            ctx.fillStyle = '#4A4A4A';
            ctx.fillRect(325, 320, 30, 10);
            ctx.fillRect(325, 340, 30, 10);
            ctx.fillRect(325, 360, 30, 10);
            ctx.fillRect(325, 380, 30, 10);

            // 配重标识
            ctx.fillStyle = '#FFFF00';
            ctx.font = '12px Arial';
            ctx.fillText('BALLAST', 325, 315);
        }

        // 绘制细节
        function drawDetails() {
            // 塔架顶部航空障碍灯
            ctx.fillStyle = '#FF0000';
            ctx.beginPath();
            ctx.arc(417, 140, 4, 0, 2 * Math.PI);
            ctx.fill();

            // 闪烁效果
            if (Math.sin(Date.now() * 0.01) > 0) {
                ctx.fillStyle = 'rgba(255, 0, 0, 0.5)';
                ctx.beginPath();
                ctx.arc(417, 140, 8, 0, 2 * Math.PI);
                ctx.fill();
            }

            // 工作照明灯
            const workLights = [
                {x: 400, y: 160, size: 3},
                {x: 434, y: 160, size: 3},
                {x: 380, y: 250, size: 3},
                {x: 454, y: 250, size: 3}
            ];

            workLights.forEach(light => {
                const lightGradient = createRadialGradient(light.x, light.y, 0, light.size, '#FFFF99', '#FFD700');
                ctx.fillStyle = lightGradient;
                ctx.beginPath();
                ctx.arc(light.x, light.y, light.size, 0, 2 * Math.PI);
                ctx.fill();

                ctx.strokeStyle = '#B8860B';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.arc(light.x, light.y, light.size, 0, 2 * Math.PI);
                ctx.stroke();
            });

            // 品牌标识
            ctx.fillStyle = '#000';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('STACKER/RECLAIMER', 320, 365);

            // 型号标识
            ctx.font = '12px Arial';
            ctx.fillText('SR-2000', 395, 195);

            // 警示标识
            ctx.fillStyle = '#FF0000';
            ctx.font = '14px Arial';
            ctx.fillText('⚠', 370, 170);
            ctx.fillText('⚠', 460, 170);

            // 电缆卷筒
            const cableGradient = createRadialGradient(380, 420, 0, 12, '#4A4A4A', '#2F2F2F');
            ctx.fillStyle = cableGradient;
            ctx.beginPath();
            ctx.arc(380, 420, 12, 0, 2 * Math.PI);
            ctx.fill();

            // 电缆
            ctx.strokeStyle = '#1A1A1A';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(380, 420);
            ctx.quadraticCurveTo(360, 440, 340, 460);
            ctx.stroke();

            // 维护平台
            ctx.fillStyle = '#6A6A6A';
            ctx.fillRect(390, 250, 40, 3);
            ctx.fillRect(390, 320, 40, 3);

            // 平台护栏
            ctx.strokeStyle = '#4A4A4A';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(390, 250);
            ctx.lineTo(390, 240);
            ctx.moveTo(430, 250);
            ctx.lineTo(430, 240);
            ctx.moveTo(390, 320);
            ctx.lineTo(390, 310);
            ctx.moveTo(430, 320);
            ctx.lineTo(430, 310);
            ctx.stroke();
        }

        // 绘制阴影效果
        function drawShadows() {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';

            // 台车阴影
            ctx.beginPath();
            ctx.ellipse(410, 485, 60, 8, 0, 0, 2 * Math.PI);
            ctx.fill();

            // 塔架阴影
            ctx.beginPath();
            ctx.ellipse(417, 485, 25, 8, 0, 0, 2 * Math.PI);
            ctx.fill();

            // 悬臂阴影
            const endX = 417 + Math.cos(boomAngle) * 150;
            const endY = 485;
            ctx.beginPath();
            ctx.ellipse(endX, endY, 40, 10, boomAngle, 0, 2 * Math.PI);
            ctx.fill();

            // 配重阴影
            ctx.beginPath();
            ctx.ellipse(340, 485, 20, 6, 0, 0, 2 * Math.PI);
            ctx.fill();
        }

        // 动画函数
        function animateStackerReclaimer() {
            if (isAnimating) {
                cancelAnimationFrame(animationId);
                isAnimating = false;
                return;
            }

            isAnimating = true;

            function animate() {
                // 悬臂缓慢旋转
                boomAngle = Math.sin(Date.now() * 0.0008) * 0.4;

                // 输送带运动
                conveyorOffset = (conveyorOffset + 2) % 150;

                drawStackerReclaimer();

                if (isAnimating) {
                    animationId = requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // 清空画布
        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            if (isAnimating) {
                cancelAnimationFrame(animationId);
                isAnimating = false;
            }
        }

        // 初始绘制
        drawStackerReclaimer();
    </script>
</body>
</html>
