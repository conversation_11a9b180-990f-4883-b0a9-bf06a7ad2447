<template>
  <div class="block-table-container" v-loading="loading" :edit-mode="isEditMode">
    <div class="block-title">
      {{ blockName }}
      <span v-if="isEditMode" class="edit-mode-indicator">编辑模式</span>
    </div>
    <el-table
      :data="tableData"
      size="mini"
      border
      style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266', padding: '5px 0' }"
      :cell-style="{ padding: '5px 0' }"
      :empty-text="'暂无数据'"
      :show-summary="true"
      :summary-method="getSummaries"
    >
      <el-table-column
        prop="schemeinfo"
        label="圈盘号"
        width="70"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="materialname"
        label="料种"
        min-width="120"
        width="140"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <!-- 编辑状态 -->
          <div v-if="editingRow[scope.$index]" class="material-edit-container">
            <el-select
              v-model="currentMaterial"
              placeholder="请选择料种"
              size="mini"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="item in materialOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <div class="material-edit-buttons">
              <el-button
                type="primary"
                icon="el-icon-check"
                size="mini"
                circle
                @click="handleSaveMaterial(scope.row, scope.$index)">
              </el-button>
              <el-button
                type="info"
                icon="el-icon-close"
                size="mini"
                circle
                @click="handleCancelEdit(scope.$index)">
              </el-button>
            </div>
          </div>

          <!-- 非编辑状态 -->
          <div v-else class="material-cell" :class="{ 'editable': isEditMode }" @click="isEditMode ? handleEditMaterial(scope.row, scope.$index) : null">
            {{ getMaterialDisplayName(scope.row) }}
            <i v-if="isEditMode" class="el-icon-edit material-edit-icon"></i>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="actVal"
        label="湿料量"
        width="85"
        align="center"
      >
        <template slot-scope="scope">
          <el-input
            v-if="isEditMode"
            v-model="scope.row.actVal"
            size="mini"
            type="number"
            :step="0.01"
            @change="handleValueChange"
            style="width: 100%;"
          ></el-input>
          <span v-else>{{ formatNumber(scope.row.actVal) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="settingVal"
        label="切出量"
        width="85"
        align="center"
      >
        <template slot-scope="scope">
          <el-input
            v-if="isEditMode"
            v-model="scope.row.settingVal"
            size="mini"
            type="number"
            :step="0.01"
            @change="handleValueChange"
            style="width: 100%;"
          ></el-input>
          <span v-else>{{ formatNumber(scope.row.settingVal) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="wetRate"
        label="配比"
        width="70"
        align="center"
      >
        <template slot-scope="scope">
          {{ formatNumber(scope.row.wetRate) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="elemSio2"
        label="硅含量"
        width="75"
        align="center"
      >
        <template slot-scope="scope">
          {{ formatNumber(scope.row.elemSio2) }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getBlockInfo, getMaterialOptions } from "@/api/feedingplan/feedingplan";
export default {
  name: "BlockTable",
  props: {
    planid: {
      type: [String, Number],
      default: ''
    },
    blockType: {
      type: String,
      default: 'block1'
    },
    editMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      blockData: {},
      materialOptions: [], // 料种参照数据
      editingRow: {}, // 当前正在编辑的行状态
      currentMaterial: '' // 当前选中的料种
    };
  },
  computed: {
    blockName() {
      const blockNames = {
      };
      return blockNames[this.blockType] || this.blockType.toUpperCase();
    },
    // 确保 editMode 被正确识别
    isEditMode() {
      return this.editMode === true;
    }
  },
  watch: {
    planid: {
      handler(newVal, oldVal) {
        if (newVal && newVal !== oldVal) {
          this.fetchData();
          this.fetchMaterialOptions();
        }
      },
      immediate: true
    },
    editMode: {
      handler(newVal, oldVal) {
        console.log(`${this.blockType} 编辑模式变化:`, oldVal, '->', newVal);
      },
      immediate: true
    },
    isEditMode: {
      handler(newVal, oldVal) {
        console.log(`${this.blockType} computed 编辑模式变化:`, oldVal, '->', newVal);
      },
      immediate: true
    }
  },
  mounted() {
    console.log(`${this.blockType} 组件挂载，editMode:`, this.editMode);
  },
  methods: {
    fetchData() {
      if (!this.planid) {
        return;
      }

      this.loading = true;
      getBlockInfo(this.planid)
        .then(response => {
          console.log(`${this.blockType} API响应:`, response);

          if (response.code === 200 && response.data) {
            this.blockData = response.data;
            const rawData = this.blockData[this.blockType] || [];
            console.log(`${this.blockType} 原始数据:`, rawData);

            // 处理和映射数据字段
            this.tableData = this.processBlockData(rawData);
            console.log(`${this.blockType} 处理后数据:`, this.tableData);

            // 通知父组件数据已更新
            this.$nextTick(() => {
              this.emitDataChange();
            });
          } else {
            this.tableData = [];
          }
        })
        .catch(error => {
          console.error(`${this.blockType} 数据获取失败:`, error);
          this.tableData = [];
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 处理block数据，确保字段映射正确
    processBlockData(rawData) {
      if (!Array.isArray(rawData)) {
        return [];
      }

      return rawData.map(item => {
        // 创建标准化的数据对象
        const processedItem = {
          // 圈盘号
          schemeinfo: item.schemeinfo || item.schemeInfo || item.scheme || item.id || '',

          // 料种名称
          materialname: item.materialname || item.materialName || item.material || item.mateName || '',
          materialcode: item.materialcode || item.materialCode || item.mateCode || '',
          materialvalue: item.materialvalue || item.materialValue || item.mateValue || '',

          // 湿料量
          actVal: this.parseNumber(item.actVal || item.actualValue || item.wetWeight || item.weight || 0),

          // 切出量
          settingVal: this.parseNumber(item.settingVal || item.settingValue || item.cutWeight || item.output || 0),

          // 配比
          wetRate: this.parseNumber(item.wetRate || item.ratio || item.proportion || item.rate || 0),

          // 硅含量
          elemSio2: this.parseNumber(item.elemSio2 || item.sio2 || item.silicon || item.si || 0),


        };

        return processedItem;
      });
    },

    // 解析数字值
    parseNumber(value) {
      if (value === null || value === undefined || value === '') {
        return 0;
      }

      if (typeof value === 'number') {
        return value;
      }

      if (typeof value === 'string') {
        const parsed = parseFloat(value);
        return isNaN(parsed) ? 0 : parsed;
      }

      return 0;
    },

    refreshData() {
      this.fetchData();
    },

    fetchMaterialOptions() {
      if (!this.planid) {
        this.materialOptions = [

        ];
        return;
      }

      getMaterialOptions(this.planid)
        .then(response => {
          if (response.code === 200 && response.data && Array.isArray(response.data)) {
            this.materialOptions = response.data.map(item => ({
              value: item.value,
              label: item.lable,
              mateinfo: item.mateinfo
            }));
          } else {
            this.materialOptions = [

            ];
          }
        })
        .catch(error => {
          this.materialOptions = [

          ];
        });
    },

    handleEditMaterial(row, index) {
      this.$set(this.editingRow, index, true);
      this.currentMaterial = row.materialcode || row.materialvalue || row.materialname || row.materialName || row.material || '';
    },

    handleSaveMaterial(row, index) {
      const selectedMaterial = this.materialOptions.find(option => option.value === this.currentMaterial);

      if (selectedMaterial) {
        this.$set(row, 'materialcode', selectedMaterial.value);
        this.$set(row, 'materialname', selectedMaterial.label);
        this.$set(row, 'materialvalue', selectedMaterial.value);

        if (selectedMaterial.mateinfo) {
          this.$set(row, 'mateinfo', selectedMaterial.mateinfo);

          if (selectedMaterial.mateinfo.sio2 !== undefined && selectedMaterial.mateinfo.sio2 !== null) {
            this.$set(row, 'elemSio2', selectedMaterial.mateinfo.sio2);
          }

          if (selectedMaterial.mateinfo.p !== undefined && selectedMaterial.mateinfo.p !== null) {
            this.$set(row, 'elemP', selectedMaterial.mateinfo.p);
          }
        }
      } else {
        this.$set(row, 'materialname', this.currentMaterial);
        this.$set(row, 'materialcode', this.currentMaterial);
      }

      this.$set(this.editingRow, index, false);
      this.$message.success('料种修改成功');

      // 通知父组件数据已更新
      this.emitDataChange();
    },

    handleCancelEdit(index) {
      this.$set(this.editingRow, index, false);
    },

    // 处理数值变化（湿料量、切出量等）
    handleValueChange() {
      // 通知父组件数据已更新
      this.emitDataChange();
    },

    getMaterialDisplayName(row) {
      if (row.materialname) {
        return row.materialname;
      }

      const materialCode = row.materialcode || row.materialvalue || row.material;
      if (materialCode && this.materialOptions.length > 0) {
        const option = this.materialOptions.find(opt => opt.value === materialCode);
        if (option) {
          return option.label;
        }
      }

      return materialCode || '未设置';
    },
    formatNumber(value) {
      if (value === null || value === undefined) {
        return '-';
      }

      if (typeof value === 'number') {
        return value.toFixed(2);
      }
      if (typeof value === 'string' && !isNaN(parseFloat(value))) {
        return parseFloat(value).toFixed(2);
      }

      return value;
    },

    getSummaries({ columns, data }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '统计';
          return;
        }
        if (index === 1) {
          sums[index] = '';
          return;
        }

        const values = data.map(item => {
          let value = item[column.property];
          if (value === null || value === undefined) {
            return 0;
          }
          if (typeof value === 'string') {
            value = parseFloat(value);
            if (isNaN(value)) {
              return 0;
            }
          }
          return value;
        });

        if (index === 2 || index === 3 || index === 4) {
          const sum = values.reduce((prev, curr) => {
            return prev + curr;
          }, 0);
          sums[index] = this.formatNumber(sum);
        } else if (index === 5) {
          const totalWeight = data.reduce((sum, item) => {
            const weight = item.wetRate || 0;
            return sum + (typeof weight === 'string' ? parseFloat(weight) || 0 : weight);
          }, 0);

          if (totalWeight === 0) {
            sums[index] = '-';
          } else {
            const weightedSum = data.reduce((sum, item) => {
              const sio2 = item.elemSio2 || 0;
              const weight = item.wetRate || 0;
              const sio2Value = typeof sio2 === 'string' ? parseFloat(sio2) || 0 : sio2;
              const weightValue = typeof weight === 'string' ? parseFloat(weight) || 0 : weight;
              return sum + (sio2Value * weightValue);
            }, 0);

            const weightedAvg = weightedSum / totalWeight;
            sums[index] = this.formatNumber(weightedAvg);
          }
        } else {
          sums[index] = '-';
        }
      });

      return sums;
    },

    // 通知父组件数据已更新
    emitDataChange() {
      this.$emit('data-change', {
        blockType: this.blockType,
        data: this.tableData
      });
    }
  }
};
</script>

<style scoped>
.block-table-container {
  margin: 0;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: calc(33.33% - 15px); /* 调整为3列布局，给更多空间 */
  display: inline-block;
  vertical-align: top;
  box-sizing: border-box;
  overflow: hidden;
  padding: 8px;
  min-width: 380px; /* 增加最小宽度，确保内容完整显示 */
  margin-bottom: 20px; /* 添加底部间距 */
}

.block-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
  padding-left: 5px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.edit-mode-indicator {
  font-size: 10px;
  color: #fff;
  background: #E6A23C;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: normal;
}


/deep/ .el-table {
  font-size: 12px;
  table-layout: fixed; /* 固定表格布局，确保列宽生效 */
}

/deep/ .el-table th {
  padding: 8px 4px;
  font-weight: bold;
  background-color: #f5f7fa;
  font-size: 12px;
  line-height: 1.2;
}

/deep/ .el-table td {
  padding: 6px 4px;
  font-size: 12px;
  line-height: 1.3;
  word-break: break-all; /* 允许长文字断行 */
}

/* 确保表格内容不会溢出 */
/deep/ .el-table .cell {
  word-break: break-all;
  line-height: 1.3;
  padding: 0 4px;
}

/deep/ .el-table--border {
  border-radius: 4px;
  overflow: hidden;
}


/deep/ .el-table__footer td {
  background-color: #f5f7fa;
  font-weight: bold;
  color: #606266;
}

/deep/ .el-table__footer td:first-child {
  text-align: center;
}

/* 料种单元格样式 */
.material-cell {
  position: relative;
  padding: 5px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 12px;
  line-height: 1.3;
  word-break: break-all; /* 长文字可以断行 */
  text-align: center;
  min-height: 32px; /* 确保有足够高度显示文字 */
}

.material-cell.editable {
  cursor: pointer;
  border-radius: 4px;
}

.material-cell.editable:hover {
  background-color: #f0f9ff;
  border: 1px dashed #409EFF;
}

.material-cell:not(.editable) {
  cursor: default;
}

.material-edit-icon {
  position: absolute;
  right: 5px;
  color: #409EFF;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s;
}

.material-cell:hover .material-edit-icon {
  opacity: 1;
}

/* 料种编辑容器样式 */
.material-edit-container {
  padding: 5px;
}

.material-edit-buttons {
  display: flex;
  justify-content: center;
  margin-top: 5px;
  gap: 5px;
}

/* 下拉选择框样式 */
/deep/ .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
  font-size: 12px;
}

/deep/ .el-select-dropdown__item {
  padding: 0 8px;
  height: auto;
  min-height: 30px;
  line-height: 1.4;
  font-size: 12px;
  white-space: normal; /* 允许文字换行 */
  word-break: break-all; /* 长单词可以断行 */
}

/deep/ .el-select-dropdown {
  min-width: 200px; /* 确保下拉框有足够宽度 */
  max-width: 300px;
}

/* 编辑按钮样式 */
/deep/ .el-button--mini.is-circle {
  padding: 5px;
}

/* 非编辑模式下的数值显示样式 */
.block-table-container:not([edit-mode]) .el-table td span {
  color: #606266;
  font-weight: 500;
}

/* 编辑模式下的表格边框高亮 */
.block-table-container[edit-mode] .el-table {
  border: 2px solid #E6A23C;
  border-radius: 6px;
}

/* 适配不同屏幕宽度 */
@media screen and (min-width: 1800px) {
  .block-table-container {
    width: calc(33.33% - 15px); /* 超大屏幕：一行显示3个 */
    min-width: 380px;
  }
}

@media screen and (max-width: 1799px) and (min-width: 1400px) {
  .block-table-container {
    width: calc(33.33% - 15px); /* 大屏幕：一行显示3个 */
    min-width: 380px;
  }
}

@media screen and (max-width: 1399px) and (min-width: 1200px) {
  .block-table-container {
    width: calc(50% - 15px); /* 中等屏幕：一行显示2个 */
    min-width: 380px;
  }
}

@media screen and (max-width: 1199px) and (min-width: 800px) {
  .block-table-container {
    width: calc(100% - 15px); /* 小屏幕：一行显示1个 */
    min-width: 380px;
    max-width: 600px; /* 限制最大宽度，避免过宽 */
  }
}

@media screen and (max-width: 799px) {
  .block-table-container {
    width: 100%; /* 移动设备：一行显示1个 */
    min-width: 320px; /* 移动设备最小宽度 */
    margin-right: 0;
  }

  /* 移动设备上调整表格字体和间距 */
  /deep/ .el-table {
    font-size: 11px;
  }

  /deep/ .el-table th,
  /deep/ .el-table td {
    padding: 3px 0;
  }
}
</style>