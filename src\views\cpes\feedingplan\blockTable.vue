<template>
  <div class="block-table-container" v-loading="loading">
    <div class="block-title">{{ blockName }}</div>
    <el-table
      :data="tableData"
      size="mini"
      border
      style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266', padding: '5px 0' }"
      :cell-style="{ padding: '5px 0' }"
      :empty-text="'暂无数据'"
      :show-summary="true"
      :summary-method="getSummaries"
    >
      <el-table-column
        prop="schemeinfo"
        label="圈盘号"
        min-width="60"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="materialname"
        label="料种"
        min-width="90"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <!-- 编辑状态 -->
          <div v-if="editingRow[scope.$index]" class="material-edit-container">
            <el-select
              v-model="currentMaterial"
              placeholder="请选择料种"
              size="mini"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="item in materialOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <div class="material-edit-buttons">
              <el-button
                type="primary"
                icon="el-icon-check"
                size="mini"
                circle
                @click="handleSaveMaterial(scope.row, scope.$index)">
              </el-button>
              <el-button
                type="info"
                icon="el-icon-close"
                size="mini"
                circle
                @click="handleCancelEdit(scope.$index)">
              </el-button>
            </div>
          </div>

          <!-- 非编辑状态 -->
          <div v-else class="material-cell" @click="handleEditMaterial(scope.row, scope.$index)">
            {{ scope.row.materialname || scope.row.materialName || scope.row.material || '未设置' }}
            <i class="el-icon-edit material-edit-icon"></i>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="actVal"
        label="湿料量"
        min-width="80"
        align="center"
      >
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.actVal"
            size="mini"
            type="number"
            :step="0.01"
            @change="handleValueChange"
            style="width: 100%;"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column
        prop="settingVal"
        label="切出量"
        min-width="80"
        align="center"
      >
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.settingVal"
            size="mini"
            type="number"
            :step="0.01"
            @change="handleValueChange"
            style="width: 100%;"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column
        prop="wetRate"
        label="配比"
        min-width="60"
        align="center"
      >
        <template slot-scope="scope">
          {{ formatNumber(scope.row.wetRate) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="elemSio2"
        label="硅含量"
        min-width="60"
        align="center"
      >
        <template slot-scope="scope">
          {{ formatNumber(scope.row.elemSio2) }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getBlockInfo, getMaterialOptions } from "@/api/feedingplan/feedingplan";
export default {
  name: "BlockTable",
  props: {
    planid: {
      type: [String, Number],
      default: ''
    },
    blockType: {
      type: String,
      default: 'block1'
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      blockData: {},
      materialOptions: [], // 料种参照数据
      editingRow: {}, // 当前正在编辑的行状态
      currentMaterial: '' // 当前选中的料种
    };
  },
  computed: {
    blockName() {
      const blockNames = {
      };
      return blockNames[this.blockType] || this.blockType.toUpperCase();
    }
  },
  watch: {
    planid: {
      handler(newVal, oldVal) {
        if (newVal && newVal !== oldVal) {
          this.fetchData();
          this.fetchMaterialOptions();
        }
      },
      immediate: true
    }
  },
  mounted() {
  },
  methods: {
    fetchData() {
      if (!this.planid) {
        return;
      }

      this.loading = true;
      getBlockInfo(this.planid)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.blockData = response.data;
            this.tableData = this.blockData[this.blockType] || [];
          } else {
            this.tableData = [];
          }
        })
        .catch(error => {
          this.tableData = [];
        })
        .finally(() => {
          this.loading = false;
        });
    },

    refreshData() {
      this.fetchData();
    },

    fetchMaterialOptions() {
      if (!this.planid) {
        this.materialOptions = [

        ];
        return;
      }

      getMaterialOptions(this.planid)
        .then(response => {
          if (response.code === 200 && response.data && Array.isArray(response.data)) {
            this.materialOptions = response.data.map(item => ({
              value: item.value,
              label: item.lable,
              mateinfo: item.mateinfo
            }));
          } else {
            this.materialOptions = [

            ];
          }
        })
        .catch(error => {
          this.materialOptions = [

          ];
        });
    },

    handleEditMaterial(row, index) {
      this.$set(this.editingRow, index, true);
      this.currentMaterial = row.materialcode || row.materialvalue || row.materialname || row.materialName || row.material || '';
    },

    handleSaveMaterial(row, index) {
      const selectedMaterial = this.materialOptions.find(option => option.value === this.currentMaterial);

      if (selectedMaterial) {
        this.$set(row, 'materialcode', selectedMaterial.value);
        this.$set(row, 'materialname', selectedMaterial.label);
        this.$set(row, 'materialvalue', selectedMaterial.value);

        if (selectedMaterial.mateinfo) {
          this.$set(row, 'mateinfo', selectedMaterial.mateinfo);

          if (selectedMaterial.mateinfo.sio2 !== undefined && selectedMaterial.mateinfo.sio2 !== null) {
            this.$set(row, 'elemSio2', selectedMaterial.mateinfo.sio2);
          }

          if (selectedMaterial.mateinfo.p !== undefined && selectedMaterial.mateinfo.p !== null) {
            this.$set(row, 'elemP', selectedMaterial.mateinfo.p);
          }
        }
      } else {
        this.$set(row, 'materialname', this.currentMaterial);
        this.$set(row, 'materialcode', this.currentMaterial);
      }

      this.$set(this.editingRow, index, false);
      this.$message.success('料种修改成功');

      // 通知父组件数据已更新
      this.emitDataChange();
    },

    handleCancelEdit(index) {
      this.$set(this.editingRow, index, false);
    },

    // 处理数值变化（湿料量、切出量等）
    handleValueChange() {
      // 通知父组件数据已更新
      this.emitDataChange();
    },

    getMaterialDisplayName(row) {
      if (row.materialname) {
        return row.materialname;
      }

      const materialCode = row.materialcode || row.materialvalue || row.material;
      if (materialCode && this.materialOptions.length > 0) {
        const option = this.materialOptions.find(opt => opt.value === materialCode);
        if (option) {
          return option.label;
        }
      }

      return materialCode || '未设置';
    },
    formatNumber(value) {
      if (value === null || value === undefined) {
        return '-';
      }

      if (typeof value === 'number') {
        return value.toFixed(2);
      }
      if (typeof value === 'string' && !isNaN(parseFloat(value))) {
        return parseFloat(value).toFixed(2);
      }

      return value;
    },

    getSummaries({ columns, data }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '统计';
          return;
        }
        if (index === 1) {
          sums[index] = '';
          return;
        }

        const values = data.map(item => {
          let value = item[column.property];
          if (value === null || value === undefined) {
            return 0;
          }
          if (typeof value === 'string') {
            value = parseFloat(value);
            if (isNaN(value)) {
              return 0;
            }
          }
          return value;
        });

        if (index === 2 || index === 3 || index === 4) {
          const sum = values.reduce((prev, curr) => {
            return prev + curr;
          }, 0);
          sums[index] = this.formatNumber(sum);
        } else if (index === 5) {
          const totalWeight = data.reduce((sum, item) => {
            const weight = item.wetRate || 0;
            return sum + (typeof weight === 'string' ? parseFloat(weight) || 0 : weight);
          }, 0);

          if (totalWeight === 0) {
            sums[index] = '-';
          } else {
            const weightedSum = data.reduce((sum, item) => {
              const sio2 = item.elemSio2 || 0;
              const weight = item.wetRate || 0;
              const sio2Value = typeof sio2 === 'string' ? parseFloat(sio2) || 0 : sio2;
              const weightValue = typeof weight === 'string' ? parseFloat(weight) || 0 : weight;
              return sum + (sio2Value * weightValue);
            }, 0);

            const weightedAvg = weightedSum / totalWeight;
            sums[index] = this.formatNumber(weightedAvg);
          }
        } else {
          sums[index] = '-';
        }
      });

      return sums;
    },

    // 通知父组件数据已更新
    emitDataChange() {
      this.$emit('data-change', {
        blockType: this.blockType,
        data: this.tableData
      });
    }
  }
};
</script>

<style scoped>
.block-table-container {
  margin: 0;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: calc(25% - 15px);
  display: inline-block;
  vertical-align: top;
  box-sizing: border-box;
  overflow: hidden;
  padding: 8px;
  min-width: 280px; /* 设置最小宽度，防止在小屏幕上变得太窄 */
  margin-bottom: 20px; /* 添加底部间距 */
}

.block-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
  padding-left: 5px;
  text-align: center;
}


/deep/ .el-table {
  font-size: 12px;
}

/deep/ .el-table th {
  padding: 5px 0;
  font-weight: bold;
  background-color: #f5f7fa;
}

/deep/ .el-table td {
  padding: 5px 0;
}

/deep/ .el-table--border {
  border-radius: 4px;
  overflow: hidden;
}

/* 表格脚注行样式 */
/deep/ .el-table__footer td {
  background-color: #f5f7fa;
  font-weight: bold;
  color: #606266;
}

/deep/ .el-table__footer td:first-child {
  text-align: center;
}

/* 料种单元格样式 */
.material-cell {
  position: relative;
  padding: 5px;
  cursor: pointer;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.material-edit-icon {
  position: absolute;
  right: 5px;
  color: #409EFF;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s;
}

.material-cell:hover .material-edit-icon {
  opacity: 1;
}

/* 料种编辑容器样式 */
.material-edit-container {
  padding: 5px;
}

.material-edit-buttons {
  display: flex;
  justify-content: center;
  margin-top: 5px;
  gap: 5px;
}

/* 下拉选择框样式 */
/deep/ .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

/deep/ .el-select-dropdown__item {
  padding: 0 10px;
  height: 30px;
  line-height: 30px;
  font-size: 12px;
}

/* 编辑按钮样式 */
/deep/ .el-button--mini.is-circle {
  padding: 5px;
}

/* 适配不同屏幕宽度 */
@media screen and (min-width: 1600px) {
  .block-table-container {
    width: calc(25% - 15px);
  }
}

@media screen and (max-width: 1599px) and (min-width: 1440px) {
  .block-table-container {
    width: calc(25% - 15px);
  }
}

@media screen and (max-width: 1439px) and (min-width: 1366px) {
  .block-table-container {
    width: calc(25% - 15px);
  }
}

@media screen and (max-width: 1365px) {
  .block-table-container {
    width: calc(50% - 10px); /* 在小屏幕上一行显示2个 */
  }
}

@media screen and (max-width: 768px) {
  .block-table-container {
    width: 100%; /* 在移动设备上一行显示1个 */
    margin-right: 0;
  }
}
</style>