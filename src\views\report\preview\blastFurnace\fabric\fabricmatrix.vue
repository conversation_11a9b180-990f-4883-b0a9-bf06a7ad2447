<template>
  <div class="app-container" style="padding: 0px;">
    <div class="block" style="margin-top:8px;">
      <span >业务时间:</span>
      <el-date-picker style="margin-left: 10px;width: 392px;"
                      v-model="workDateArr" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
      </el-date-picker>
      <el-button type="primary" style="margin-left: 100px;" @click="handleQuery" size="mini">搜索</el-button>
    </div>
    <div class="buttonOpe">
      <el-button type="primary"  @click="handleAdd" size="mini">新增</el-button>
      <el-button type="danger"  @click="handleDelete" size="mini">删除</el-button>
      <el-button type="primary"  @click="handleUpdate" size="mini">保存</el-button>
      <el-button type="primary"  @click="daochu" size="mini">导出</el-button>
    </div>
    <div style="margin-top: 7px;">
      <vxe-table
        border
        align="center"
        :loading="loading"
        ref="tableRef"
        :edit-config="{trigger: 'click', mode: 'row'}"
        @edit-closed="editClosedEvent"
        :checkbox-config="{checkField: 'isChecked', indeterminateField: 'isIndeterminate'}"
        @checkbox-change="handleCheckboxChange"
        @cell-click="handleCellClickTime"
        :data="tableData"
        :merge-cells="mergeCells"
        >
        <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
        <vxe-column field="workDate" title="业务时间"  width='120' fixed="left" :formatter="formatDate"></vxe-column>
        <vxe-column field="batch" title="批次"  width='120' :edit-render="{name: 'input'}"></vxe-column>
        <vxe-colgroup title="料线" col-key="group1">
          <vxe-column field="cokeStockLine" title="C"  width='120' :edit-render="{name: 'input'}"></vxe-column>
          <vxe-column field="oreStockLine" title="O"  width='120' :edit-render="{name: 'input'}"></vxe-column>
        </vxe-colgroup>
        <vxe-column field="circle"   width='80'></vxe-column>
        <vxe-colgroup title="布料矩阵C" col-key="group2">
          <vxe-column field="cokeCircle1" title="角度1"  width='100' :edit-render="{name: 'input'}" header-class-name="custom-span">
<!--            <vxe-column field="cokeCircle1" title="" width='100'   :edit-render="{name: 'input'}"></vxe-column>-->
          </vxe-column>
          <vxe-column field="cokeCircle2" title="角度2"  width='100' :edit-render="{name: 'input'}">
<!--            <vxe-column field="cokeCircle2" title="" width='100'  :edit-render="{name: 'input'}"></vxe-column>-->
          </vxe-column>
          <vxe-column field="cokeCircle3" title="角度3"  width='100' :edit-render="{name: 'input'}">
<!--            <vxe-column field="cokeCircle3" title="" width='100'  :edit-render="{name: 'input'}"></vxe-column>-->
          </vxe-column>
          <vxe-column field="cokeCircle4" title="角度4"  width='100' :edit-render="{name: 'input'}">
<!--            <vxe-column field="cokeCircle4" title="" width='100'  :edit-render="{name: 'input'}"></vxe-column>-->
          </vxe-column>
          <vxe-column field="cokeCircle5" title="角度5"  width='100' :edit-render="{name: 'input'}">
<!--            <vxe-column field="cokeCircle5" title="" width='100'  :edit-render="{name: 'input'}"></vxe-column>-->
          </vxe-column>
          <vxe-column field="cokeCircle6" title="角度6"  width='100' :edit-render="{name: 'input'}">
<!--            <vxe-column field="cokeCircle6" title="" width='100'  :edit-render="{name: 'input'}"></vxe-column>-->
          </vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="布料矩阵O" col-key="group3">
          <vxe-column field="oreCircle1" title="角度1"  width='100' :edit-render="{name: 'input'}">
<!--            <vxe-column field="oreCircle1" title="" width='100'  :edit-render="{name: 'input'}"></vxe-column>-->
          </vxe-column>
          <vxe-column field="oreCircle2" title="角度2"  width='100' :edit-render="{name: 'input'}">
<!--            <vxe-column field="oreCircle2" title="" width='100'  :edit-render="{name: 'input'}"></vxe-column>-->
          </vxe-column>
          <vxe-column field="oreCircle3" title="角度3"  width='100' :edit-render="{name: 'input'}">
<!--            <vxe-column field="oreCircle3" title="" width='100'  :edit-render="{name: 'input'}"></vxe-column>-->
          </vxe-column>
          <vxe-column field="oreCircle4" title="角度4"  width='100' :edit-render="{name: 'input'}">
<!--            <vxe-column field="oreCircle4" title="" width='100'  :edit-render="{name: 'input'}"></vxe-column>-->
          </vxe-column>
          <vxe-column field="oreCircle5" title="角度5"  width='100' :edit-render="{name: 'input'}">
<!--            <vxe-column field="oreCircle5" title="" width='100'  :edit-render="{name: 'input'}"></vxe-column>-->
          </vxe-column>
        </vxe-colgroup>
        <vxe-column field="operator" title="操作人"  width='100' :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="operationTime" title="操作时间"  width='180' :edit-render="{name: 'input'}"></vxe-column>
      </vxe-table>
      <vxe-pager
        :current-page.sync="mainTableConfig.pageConfig.pageNum"
        :page-size.sync="mainTableConfig.pageConfig.pageSize"
        :total="mainTableConfig.pageConfig.total"
        @page-change="pageChange">
      </vxe-pager>
    </div>
  </div>
</template>

<script>
  import {
    listFabricMatrixSelect,
    fabricmatrixAdd,
    fabricmatrixUpdateData,
    fabricmatrixDeleteData,
  } from "@/api/report/preview/blastFurnace/fabricmatrix";
  import dayjs from "dayjs";

  export default {
    name: "windVolument",
    data(){
      return{
        mergeCells : [],
        // 加载
        loading:true,
        workDateArr:[],
        tableData: [],
        mainTableConfig: {
          tableData: [],
          selectVO: '',
          pageConfig: {
            pageNum: 1, // 页码
            pageSize: 10, // 每页显示条目个数
            total: 0, // 总数
            background: true, // 是否展示分页器背景色
            pageSizes: [10, 20, 50, 100]// 分页器分页待选项
          }
        },
        selectParam:{
          workDateStart:'',
          workDateEnd:'',
          prodCenterCode:'',
        },
        updateDataObj_Angle:{},
        updateDataObj_Circle:{},
        updateDataArr:[],

        fabricDeleteDataArr:[],
        // 焦 角度标识
        cokeAngle1Flag:false,
        cokeAngle2Flag:false,
        cokeAngle3Flag:false,
        cokeAngle4Flag:false,
        cokeAngle5Flag:false,
        cokeAngle6Flag:false,

        // 矿 角度标识
        oreAngle1Flag:false,
        oreAngle2Flag:false,
        oreAngle3Flag:false,
        oreAngle4Flag:false,
        oreAngle5Flag:false,

        // 焦 圈数标识
        cokeCircle1Flag:false,
        cokeCircle2Flag:false,
        cokeCircle3Flag:false,
        cokeCircle4Flag:false,
        cokeCircle5Flag:false,
        cokeCircle6Flag:false,

        // 矿 圈数标识
        oreCircle1Flag:false,
        oreCircle2Flag:false,
        oreCircle3Flag:false,
        oreCircle4Flag:false,
        oreCircle5Flag:false,

        // 操作人标识
        operatorFlag:false,
        // 操作时间标识
        operationTimeFlag:false,
        // 焦 料线标识
        cokeStockLineFlag:false,
        // 矿 料线标识
        oreStockLineFlag:false,
        // 主键标识
        fidFlag:false,
        // 批次标识
        batchFlag:false,
      }
    },
    created(){
      this.workDateArr.push(dayjs(new Date()).add(-1, "day"));
      this.workDateArr.push(dayjs(new Date()).add(1, "day"));
      // 展示页面数据
      this.queryLists();
    },
    methods:{
      /*  获取高炉号*/
      getBlastFurnaceNumber() {
        return this.$route.query.prodCode;
      },
      /*搜索按钮*/
      handleQuery(){
        if(this.workDateArr != null){
          if (this.workDateArr.length == 2) {
            this.selectParam.workDateStart = dayjs(this.workDateArr[0]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.selectParam.workDateEnd = dayjs(this.workDateArr[1]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }
        }
        this.queryLists();
      },

      queryLists(){

        this.selectParam.prodCenterCode = this.getBlastFurnaceNumber();
        listFabricMatrixSelect(this.selectParam).then(response=>{
          this.tableData = response.rows
          this.loading = false;
          this.mainTableConfig.pageConfig.total = response.total
          if(response.total>0 ){
            for(let i=0;i<response.total;i++){
              if(i%2==0){
                let obj1 = { row: i, col: 0, rowspan: 2, colspan: 1 }
                let obj2 = { row: i, col: 1, rowspan: 2, colspan: 1 }
                let obj3 = { row: i, col: 2, rowspan: 2, colspan: 1 }
                let obj4 = { row: i, col: 3, rowspan: 2, colspan: 1 }
                let obj5 = { row: i, col: 4, rowspan: 2, colspan: 1 }
                let obj6 = { row: i, col: 17, rowspan: 2, colspan: 1 }
                let obj7 = { row: i, col: 18, rowspan: 2, colspan: 1 }
                this.mergeCells.push(obj1)
                this.mergeCells.push(obj2)
                this.mergeCells.push(obj3)
                this.mergeCells.push(obj4)
                this.mergeCells.push(obj5)
                this.mergeCells.push(obj6)
                this.mergeCells.push(obj7)
              }
            }
          }
        });
      },
      /*关闭编辑框触发 失去焦点 进行保存数据*/
      editClosedEvent(row, column,rowIndex) {

        // 焦 角度
        if(this.cokeAngle1Flag == true){
          let cokeAngle1 = row.row.cokeCircle1
          this.updateDataObj_Angle.cokeAngle1 = cokeAngle1
          this.cokeAngle1Flag = false
        }
        if(this.cokeAngle2Flag == true){
          let cokeAngle2 = row.row.cokeCircle2
          this.updateDataObj_Angle.cokeAngle2 = cokeAngle2
          this.cokeAngle2Flag = false
        }
        if(this.cokeAngle3Flag == true){
          let cokeAngle3 = row.row.cokeCircle3
          this.updateDataObj_Angle.cokeAngle3 = cokeAngle3
          this.cokeAngle3Flag = false
        }
        if(this.cokeAngle4Flag == true){
          let cokeAngle4 = row.row.cokeCircle4
          this.updateDataObj_Angle.cokeAngle4 = cokeAngle4
          this.cokeAngle4Flag = false
        }
        if(this.cokeAngle5Flag == true){
          let cokeAngle5 = row.row.cokeCircle5
          this.updateDataObj_Angle.cokeAngle5 = cokeAngle5
          this.cokeAngle5Flag = false
        }
        if(this.cokeAngle6Flag == true){
          let cokeAngle6 = row.row.cokeCircle6
          this.updateDataObj_Angle.cokeAngle6 = cokeAngle6
          this.cokeAngle6Flag = false
        }

        //矿 角度
        if(this.oreAngle1Flag == true){
          let oreAngle1 = row.row.oreCircle1
          this.updateDataObj_Angle.oreAngle1 = oreAngle1
          this.oreAngle1Flag = false
        }
        if(this.oreAngle2Flag == true){
          let oreAngle2 = row.row.oreCircle2
          this.updateDataObj_Angle.oreAngle2 = oreAngle2
          this.oreAngle2Flag = false
        }
        if(this.oreAngle3Flag == true){
          let oreAngle3 = row.row.oreCircle3
          this.updateDataObj_Angle.oreAngle3 = oreAngle3
          this.oreAngle3Flag = false
        }
        if(this.oreAngle4Flag == true){
          let oreAngle4 = row.row.oreCircle4
          this.updateDataObj_Angle.oreAngle4 = oreAngle4
          this.oreAngle4Flag = false
        }
        if(this.oreAngle5Flag == true){
          let oreAngle5 = row.row.oreCircle5
          this.updateDataObj_Angle.oreAngle5 = oreAngle5
          this.oreAngle5Flag = false
        }

        // 焦 圈数
        if(this.cokeCircle1Flag == true){
          let cokeCircle1 = row.row.cokeCircle1
          this.updateDataObj_Circle.cokeCircle1 = cokeCircle1
          this.cokeCircle1Flag = false
        }
        if(this.cokeCircle2Flag == true){
          let cokeCircle2 = row.row.cokeCircle2
          this.updateDataObj_Circle.cokeCircle2 = cokeCircle2
          this.cokeCircle2Flag = false
        }
        if(this.cokeCircle3Flag == true){
          let cokeCircle3 = row.row.cokeCircle3
          this.updateDataObj_Circle.cokeCircle3 = cokeCircle3
          this.cokeCircle3Flag = false
        }
        if(this.cokeCircle4Flag == true){
          let cokeCircle4 = row.row.cokeCircle4
          this.updateDataObj_Circle.cokeCircle4 = cokeCircle4
          this.cokeCircle4Flag = false
        }
        if(this.cokeCircle5Flag == true){
          let cokeCircle5 = row.row.cokeCircle5
          this.updateDataObj_Circle.cokeCircle5 = cokeCircle5
          this.cokeCircle5Flag = false
        }
        if(this.cokeCircle6Flag == true){
          let cokeCircle6 = row.row.cokeCircle6
          this.updateDataObj_Circle.cokeCircle6 = cokeCircle6
          this.cokeCircle6Flag = false
        }

        // 矿 圈数
        if(this.oreCircle1Flag == true){
          let oreCircle1 = row.row.oreCircle1
          this.updateDataObj_Circle.oreCircle1 = oreCircle1
          this.cokeCircle1Flag = false
        }
        if(this.oreCircle2Flag == true){
          let oreCircle2 = row.row.oreCircle2
          this.updateDataObj_Circle.oreCircle2 = oreCircle2
          this.oreCircle2Flag = false
        }
        if(this.oreCircle3Flag == true){
          let oreCircle3 = row.row.oreCircle3
          this.updateDataObj_Circle.oreCircle3 = oreCircle3
          this.oreCircle3Flag = false
        }
        if(this.oreCircle4Flag == true){
          let oreCircle4 = row.row.oreCircle4
          this.updateDataObj_Circle.oreCircle4 = oreCircle4
          this.oreCircle4Flag = false
        }
        if(this.oreCircle5Flag == true){
          let oreCircle5 = row.row.oreCircle5
          this.updateDataObj_Circle.oreCircle5 = oreCircle5
          this.oreCircle5Flag = false
        }
        // 操作人
        if(this.operatorFlag == true){
          let operator = row.row.operator
          this.updateDataObj_Circle.operator = operator
          this.operatorFlag = false
        }
        // 操作时间
        if(this.operationTimeFlag == true){
          let operationTime = row.row.operationTime
          this.updateDataObj_Circle.operationTime = operationTime
          this.operationTimeFlag = false
        }
        // 焦 料线
        if(this.cokeStockLineFlag == true){
          let cokeStockLine = row.row.cokeStockLine
          this.updateDataObj_Circle.cokeStockLine = cokeStockLine
          this.cokeStockLineFlag = false
        }
        // 矿 料线
        if(this.oreStockLineFlag == true){
          let oreStockLine = row.row.oreStockLine
          this.updateDataObj_Circle.oreStockLine = oreStockLine
          this.oreStockLineFlag = false
        }
        // 主键
        let fid = row.row.fid
        this.updateDataObj_Circle.fid = fid
        this.updateDataObj_Angle.fid = fid

        // 批次
        let batch = row.row.batch
        this.updateDataObj_Circle.batch = batch
        this.updateDataObj_Angle.batch = batch

      },

      /*多选框触发事件*/
      handleCheckboxChange({ records, rowIndex, row,checked }) {
        if(checked  == true){
          let obj = {fid:row.fid}
          this.fabricDeleteDataArr.push(obj)
        }else{
          const $table = this.$refs.tableRef
          if ($table) {
            $table.clearCheckboxRow()
          }
          this.fabricDeleteDataArr = []
        }
      },
      handleCellClickTime({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, triggerRadio, triggerCheckbox, triggerTreeNode, triggerExpandNode, $event }) {

        if (column.property === 'operationTime') { // 判断是否点击的是时间列
          if(row.operationTime == null){
            row.operationTime = this.getCurrentTime(); // 获取当前时间并更新到行数据中
          }
        }

        // rowIndex == 0 代表是 角度, 1代表是圈数
        if(rowIndex % 2 == 0){
          // 焦 角度
          if(column.property === 'cokeCircle1'){
            this.cokeAngle1Flag = true;
          }
          if(column.property === 'cokeCircle2'){
            row.cokeAngle2 = row.cokeCircle2
            this.cokeAngle2Flag = true;
          }
          if(column.property === 'cokeCircle3'){
            row.cokeAngle3 = row.cokeCircle3
            this.cokeAngle3Flag = true;
          }
          if(column.property === 'cokeCircle4'){
            row.cokeAngle4 = row.cokeCircle4
            this.cokeAngle4Flag = true;
          }
          if(column.property === 'cokeCircle5'){
            row.cokeAngle5 = row.cokeCircle5
            this.cokeAngle5Flag = true;
          }
          if(column.property === 'cokeCircle6'){
            row.cokeAngle6 = row.cokeCircle6
            this.cokeAngle6Flag = true;
          }

          // 矿 角度
          if(column.property === 'oreCircle1'){
            this.oreAngle1Flag = true;
          }
          if(column.property === 'oreCircle2'){
            row.oreAngle2 = row.oreCircle2
            this.oreAngle2Flag = true;
          }
          if(column.property === 'oreCircle3'){
            row.oreAngle3 = row.oreCircle3
            this.oreAngle3Flag = true;
          }
          if(column.property === 'oreCircle4'){
            row.oreAngle4 = row.oreCircle4
            this.oreAngle4Flag = true;
          }
          if(column.property === 'oreCircle5'){
            row.oreAngle5 = row.oreCircle5
            this.oreAngle5Flag = true;
          }

          // 操作人
          if(column.property === 'operator'){
            row.operator = row.operator
            this.operatorFlag = true;
          }
          // 操作时间
          if(column.property === 'operationTime'){
            row.operationTime = row.operationTime
            this.operationTimeFlag = true;
          }
          // 焦 料线
          if(column.property === 'cokeStockLine'){
            row.cokeStockLine = row.cokeStockLine
            this.cokeStockLineFlag = true;
          }
          // 矿 料线
          if(column.property === 'oreStockLine'){
            row.oreStockLine = row.oreStockLine
            this.oreStockLineFlag = true;
          }
          if(column.property === 'fid'){
            row.fid = row.fid
            this.fidFlag = true;
          }
          if(column.property === 'batch'){
            row.batch = row.batch
            this.batchFlag = true;
          }


        }else{
          console.log("row111:",row)
          // 焦 圈数
          if(column.property === 'cokeCircle1'){
            this.cokeCircle1Flag = true;
          }
          if(column.property === 'cokeCircle2'){
            this.cokeCircle2Flag = true;
          }
          if(column.property === 'cokeCircle3'){
            this.cokeCircle3Flag = true;
          }
          if(column.property === 'cokeCircle4'){
            this.cokeCircle4Flag = true;
          }
          if(column.property === 'cokeCircle5'){
            this.cokeCircle5Flag = true;
          }
          if(column.property === 'cokeCircle6'){
            this.cokeCircle6Flag = true;
          }
          // 矿圈数
          if(column.property === 'oreCircle1'){
            this.oreCircle1Flag = true;
          }
          if(column.property === 'oreCircle2'){
            this.oreCircle2Flag = true;
          }
          if(column.property === 'oreCircle3'){
            this.oreCircle3Flag = true;
          }
          if(column.property === 'oreCircle4'){
            this.oreCircle4Flag = true;
          }
          if(column.property === 'oreCircle5'){
            this.oreCircle5Flag = true;
          }
        }
      },
      getCurrentTime() {
        return  dayjs(new Date()).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      },
      pageChange ({ pageSize, currentPage }) {
        this.mainTableConfig.pageConfig.pageNum = currentPage
        this.mainTableConfig.pageConfig.pageSize = pageSize
        this.selectParam.pageNum=this.mainTableConfig.pageConfig.pageNum
        this.selectParam.pageSize=this.mainTableConfig.pageConfig.pageSize
        this.queryLists()
      },
      /* 新增按钮 */
      handleAdd(){
        let obj ={prodCenterCode:this.getBlastFurnaceNumber()}
        fabricmatrixAdd(obj).then(response=>{
          if(response.code == 200){
            this.$modal.msgSuccess("新增成功");
            this.queryLists()
          }
        });

      },
      /* 删除按钮 */
      handleDelete(){
        console.log("windDataObj:",JSON.stringify(this.fabricDeleteDataArr))
        if(this.fabricDeleteDataArr.length == 0 || this.fabricDeleteDataArr == null){
          this.$message({
            message: '请选择要删除的数据',
            type:'warning'
          })
          return
        }
        fabricmatrixDeleteData(this.fabricDeleteDataArr).then(response=>{
          if(response.code == 200){
            this.$modal.msgSuccess("删除成功");
            this.queryLists()
          }
        });

      },
      /* 修改保存按钮 */
      handleUpdate(){
        this.updateDataArr.push(this.updateDataObj_Circle)
        this.updateDataArr.push(this.updateDataObj_Angle)
        fabricmatrixUpdateData(this.updateDataArr).then(response=>{
          if(response.code == 200){
            this.$modal.msgSuccess("保存成功");
            this.queryLists()
            this.updateDataArr = []
          }
        });

      },
      /* 创建时间 显示 年月日 */
      formatDate({ cellValue }) {
        // 使用 JavaScript 的 Date 对象来格式化日期
        const date = new Date(cellValue);
        const year = date.getFullYear();
        const month = ('0' + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的，所以加1
        const day = ('0' + date.getDate()).slice(-2);
        return `${year}-${month}-${day}`; // 或者直接使用 `date.toISOString().slice(0, 10)`
      },
      /* 导出按钮 */
      daochu(){
        this.download('/api/fabricmatrix/export', {
          ...this.selectParam
        }, `volume_${new Date().getTime()}.xlsx`)
      },
    },

  }
</script>

<style scoped>
  .buttonOpe{
    margin-top: 10px;
    margin-left: 5px;
  }
  /deep/.custom-span .vex-cell .vex-cell--title{
    width: 99% !important;
    position: absolute !important;
    background-color: rgb(244,246,251) !important;
    bottom: 38px !important;
    left: 0 !important;
  }

</style>
