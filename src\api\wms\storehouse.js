import request from '@/utils/request'

//查询 
export function listStorehouse(query) {
    return request({
        url: '/api/wms/storehouse/list',
        method: 'get',
        params: query,
    })
}

export function saveOrUpdate(data) {
    return request({
        url: '/api/wms/storehouse/saveOrUpdate',
        method: 'post',
        data: data,
    })
}

export function deleteStorehouse(storehouseId) {
    return request({
        url: '/api/wms/storehouse/' + storehouseId,
        method: 'delete',
    })
}

export function listProdCenter() {
    return request({
        url: '/api/wms/storehouse/listProdCenter',
        method: 'get',
    })
}

