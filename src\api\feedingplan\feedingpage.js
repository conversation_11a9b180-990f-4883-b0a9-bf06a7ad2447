import request from '@/utils/request'

// 获取计划数据模型
export function getemptydatamodel(planid, srcplanid, prodcode) {
  return request({
    url: '/feed/base/getemptydatamodel',
    method: 'get',
    params: { planid: planid, srcplanid: srcplanid, prodCode: prodcode }
  })
}

// 获取矿料
export function getOrelist() {
  return request({
    url: '/feed/page/getorelist',
    method: 'get',
  })
}

// 获取焦丁物料
export function getCokeList() {
  return request({
    url: '/feed/page/getcokelist',
    method: 'get',
  })
}

// 获取非金属溶剂
export function getSolventList() {
  return request({
    url: '/feed/page/getsolventlist',
    method: 'get',
  })
}

// 获取煤
export function getCoalList() {
  return request({
    url: '/feed/page/getcoallist',
    method: 'get',
  })
}

// 获取密度
export function getdensity(mateNum, schemeid, prodcode) {
  return request({
    url: '/feed/page/getDensity/' + mateNum,
    method: 'get',
    params: { schemeid: schemeid, prodcode: prodcode },
  })
}

// 获取焦丁
export function getCokeNutList() {
  return request({
    url: '/feed/page/getcokenutlist',
    method: 'get',
  })
}

// 获取矿和溶剂
export function getOreAndSolvent() {
  return request({
    url: '/feed/page/getoreandsolvent',
    method: 'get',
  })
}

// 获取物料化学成分(技术中心样)
export function getMateElemJS(mateNum) {
  return request({
    url: '/feed/page/getelemjs/' + mateNum,
    method: 'get',
  })
}

// 获取物料化学成分(炼铁厂样)
export function getMateElemLT(mateNum) {
  return request({
    url: '/feed/page/getelemlt/' + mateNum,
    method: 'get',
  })
}

// 获取物料化学成分(炼铁厂样)
export function getMateElemALL(mtype, mateNum) {
  return request({
    url: '/feed/page/getelem/' + mtype + '/' + mateNum,
    method: 'get',
  })
}

// 计算
export function calc(planObj) {
  return request({
    url: '/feed/plan/calc',
    method: 'post',
    data: planObj
  })
}

// 保存方案
export function saveplan(planObj) {
  return request({
    url: '/feed/plan/save',
    method: 'post',
    data: planObj
  })
}

// 获取计划列表
export function getPlanList(queryObj) {
  return request({
    url: '/feed/plan/list',
    method: 'get',
    params: { queryjson: JSON.stringify(queryObj) }
  })
}

// 获取计划列表(物料)
export function getPlanMateList(queryObj) {
  return request({
    url: '/feed/bat/list',
    method: 'get',
    params: { queryjson: JSON.stringify(queryObj) }
  })
}

// 计划流程
export function gonext(roletag, planid, currnode, workdate, batchnum) {
  // console.log("currnode=",currnode);
  return request({
    url: '/feed/plan/gonext',
    method: 'get',
    params: {
      roletag: roletag,
      planid: planid,
      currnode: currnode,
      workdate: workdate,
      batchnum: batchnum
    }
  })
}

// 计划作废
export function obsplan(roletag, planid, currnode, workdate, batchnum) {
  return request({
    url: '/feed/plan/obsplan',
    method: 'get',
    params: {
      roletag: roletag,
      planid: planid,
      currnode: currnode
    }
  })
}

// 页面下拉框绑定
export function bindserchbox(prodcode, pgroup) {
  return request({
    url: '/feed/page/bindserchbox',
    method: 'get',
    params: {
      prodcode: prodcode,
      pgroup: pgroup,
    }
  })
}

// 获取页面可操作的任务节点
export function getallownode(roletag) {
  return request({
    url: '/feed/page/getallownode',
    method: 'get',
    params: {
      roletag: roletag,
    }
  })
}







///////////////////////////////////////////////////////////////////////////////
// 保存页面json
export function saveformjson(key, formjson) {
  formjson["pagekey"] = key;
  return request({
    url: '/formtemp/common/formjson/save',
    method: 'post',
    data: formjson
  })
}



// 表单数据初始化
export function getformdata(filterStr, pageKey) {
  return request({
    url: '/formtemp/common/data/query',
    method: 'get',
    params: { filterStr: JSON.stringify(filterStr), pageKey: pageKey }
  })
}

// 更新数据
export function saveformdata(filterParam, bizDatajson) {
  const postBody = {
    filterParam: filterParam,
    data: bizDatajson
  };

  return request({
    url: '/formtemp/common/data/save',
    method: 'post',
    data: postBody
  })
}