<template>
  <!--  <div class="main" @wheel.prevent="handleWheel" :style="{ transform: `scale(${scale})`, transformOrigin: 'center center' }"> -->
  <div class="main">
    <div class="header-bar">一原料仓储管理</div>
    <div class="bottom-barBg">
      <div class="leftBarBg"></div>
      <div class="rightBarBg"></div>
    </div>
    <div class="box">
      <div class="left">
        <div class="coordinate_axis1">
          <div v-for="i in 15" :key="i">
            <!--  <div :style="{ left: (i - 1) * (100 / 14) + '%' }" class="tick"></div>
            <div :style="{ left: (i - 1) * (100 / 14) + '%' }" class="tick-label">
              <span>{{ (15 - i) * 50 }}</span>
            </div> -->
          </div>
        </div>
      </div>
      <div class="right">
        <div class="tab-bar">库存信息</div>
        <div class="tab-bar">出入库履历</div>
        <div class="tab-bar">作业指令</div>
      </div>
    </div>
    <GirdLayoutTest0 :layoutData='layoutData' mainTitle='混匀' style="margin: 22px 0 0 0;" />
    <div class="boxDJ">
      <div class="lineDJ">
      </div>
      <div class="lineDJ1"></div>
    </div>
    <!--   <div class="container" ref="container">
      <div ref="div1" class="bigMon" :style="{ marginLeft: width + '%'}"></div>
      <div ref="div2" :style="{ marginLeft: width1 + '%',backgroundColor: name == '取料' ? '#00ed00' : '#19acff'}" class="materialIn"></div>
      <svg class="connector">
        <line :x1="line.x1" :y1="line.y1" :x2="line.x2" :y2="line.y2" stroke="black" stroke-width="2" />
      </svg>
    </div> -->
    <GirdLayoutTest1 :layoutData='layoutData1' mainTitle="D" />
    <div class="boxDJ">
      <div class="lineDJ"></div>
      <div class="lineDJ1"></div>
    </div>
    <GirdLayoutTest2 :layoutData='layoutData2' mainTitle='C' />
    <div class="boxDJ">
      <div class="lineDJ"></div>
      <div class="lineDJ1"></div>
    </div>
    <GirdLayoutTest3 :layoutData='layoutData3' mainTitle='B' />
    <div class="boxDJ">
      <div class="lineDJ"></div>
      <div class="lineDJ1"></div>
    </div>
    <GirdLayoutTest4 :layoutData='layoutData4' mainTitle='A' />
    <div class="boxDJ">
      <div class="lineDJ"></div>
      <div class="lineDJ1"></div>
    </div>
  </div>
</template>
    
  <script>
import GirdLayoutTest0 from '@/components/GirdLayoutTest/index'
import GirdLayoutTest1 from '@/components/GirdLayoutTest/index'
import GirdLayoutTest2 from '@/components/GirdLayoutTest/index'
import GirdLayoutTest3 from '@/components/GirdLayoutTest/index'
import GirdLayoutTest4 from '@/components/GirdLayoutTest/index'

export default {
  components: { GirdLayoutTest0, GirdLayoutTest1, GirdLayoutTest2, GirdLayoutTest3, GirdLayoutTest4 },
  data() {
    return {
      layoutData: [
        {
          "x": 518, "y": 0, "w": 150, "h": 2, "i": "18", minH: 2, maxH: 2, materialName: '焦粉', background: '#a4ffe8',
          "x1": 580, "w1": 80
        },
        {
          "x": 333, "y": 0, "w": 100, "h": 2, "i": "20", minH: 2, maxH: 2, materialName: '石灰石粉', background: '#ffd28a',
          "x1": 275, "w1": 90
        },
        {
          "x": 168, "y": 0, "w": 82, "h": 2, "i": "21", minH: 2, maxH: 2, materialName: '磁石', background: '#aaffff',
          "x1": 158, "w1": 99
        },
        {
          "x": 30, "y": 0, "w": 99, "h": 2, "i": "22", minH: 2, maxH: 2, materialName: '萤石', background: '#49c449',
          "x1": 20, "w1": 130
        },
      ],
      layoutData1: [
        {
          "x": 10, "y": 0, "w": 140, "h": 2, "i": "5", minH: 2, maxH: 2, materialName: '氧化铁皮', background: '#ca99ff',
          "x1": 0, "w1": 160
        },
        {
          "x": 210, "y": 0, "w": 60, "h": 2, "i": "6", minH: 2, maxH: 2, materialName: '混合块', background: '#e2e200',
          "x1": 200, "w1": 90
        },
        {
          "x": 330, "y": 0, "w": 150, "h": 2, "i": "7", minH: 2, maxH: 2, materialName: '石灰石圆', background: '#b7ff8c',
          "x1": 320, "w1": 170
        },
        {
          "x": 550, "y": 0, "w": 120, "h": 2, "i": "8", minH: 2, maxH: 2, materialName: '超星转矿', background: '#aaffff',
          "x1": 530, "w1": 150
        },
      ],
      layoutData2: [
        {
          "x": 600, "y": 0, "w": 50, "h": 2, "i": "9", minH: 2, maxH: 2, materialName: '焦粉', background: '#a4ffe8',
          "x1": 580, "w1": 90
        },
        {
          "x": 320, "y": 0, "w": 160, "h": 2, "i": "10", minH: 2, maxH: 2, materialName: '清炉矿', background: '#d2d2ff',
          "x1": 300, "w1": 200
        },
        {
          "x": 30, "y": 0, "w": 185, "h": 2, "i": "11", minH: 2, maxH: 2, materialName: '石灰石粉', background: '#ffd28a',
          "x1": 10, "w1": 220
        },
      ],
      layoutData3: [
        {
          "x": 517, "y": 0, "w": 180, "h": 2, "i": "12", minH: 2, maxH: 2, materialName: '混合矿', background: '#fbfb2d',
          "x1": 512, "w1": 188
        },
        {
          "x": 0, "y": 0, "w": 500, "h": 2, "i": "13", minH: 2, maxH: 2, materialName: '无烟煤', background: '#9ed1fd',
          "x1": 0, "w1": 508
        }
      ],
      layoutData4: [
        {
          "x": 530, "y": 0, "w": 160, "h": 2, "i": "14", minH: 2, maxH: 2, materialName: '焦粉', background: '#d2d2ff',
          "x1": 500, "w1": 200
        },
        {
          "x": 350, "y": 0, "w": 85, "h": 2, "i": "15", minH: 2, maxH: 2, materialName: '石灰石粉', background: '#ffd28a',
          "x1": 340, "w1": 110
        },
        {
          "x": 200, "y": 0, "w": 82, "h": 2, "i": "16", minH: 2, maxH: 2, materialName: '磁石', background: '#d2d2ff',
          "x1": 180, "w1": 110
        },
        {
          "x": 50, "y": 0, "w": 90, "h": 2, "i": "17", minH: 2, maxH: 2, materialName: '萤石', background: '#49c449',
          "x1": 30, "w1": 130
        },
      ],
      width: 23,
      width1: 28,
      line: {
        x1: 0,
        y1: 0,
        x2: 0,
        y2: 0
      },
      name: '取料',
      scale: 1,           // 初始缩放比例
      scaleStep: 0.05,    // 每次缩放的步长
      minScale: 0.5,      // 最小缩放比例
      maxScale: 1       // 最大缩放比例
    }
  },
  mounted() {
    this.updateLinePosition();
    // 监听窗口变化，重新计算连线位置
    window.addEventListener('resize', this.updateLinePosition);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateLinePosition);
  },
  methods: {
    handleWheel(event) {
      // 判断滚轮方向（正数为向上滚动，负数为向下滚动）
      const delta = event.deltaY > 0 ? -1 : 1;
      // 计算新的缩放值
      const newScale = this.scale + delta * this.scaleStep;
      // 限制缩放范围
      if (newScale >= this.minScale && newScale <= this.maxScale) {
        this.scale = newScale;
      }
    },
    updateLinePosition() {
      // 等待DOM更新完成
      this.$nextTick(() => {
        const node1 = this.$refs.div1;
        const node2 = this.$refs.div2;
        if (node1 && node2) {
          const rect1 = this.$refs.div1.getBoundingClientRect();
          const rect2 = this.$refs.div2.getBoundingClientRect();
          const containerRect = this.$refs.container.getBoundingClientRect();
          // 计算两个节点的中心点相对于容器的位置
          this.line = {
            x1: rect1.left + rect1.width / 2 - containerRect.left,
            y1: rect1.top + rect1.height / 2 - containerRect.top,
            x2: rect2.left + rect2.width / 2 - containerRect.left,
            y2: rect2.top + rect2.height / 2 - containerRect.top
          };
        }
      });
    }
  },
  watch: {
  }
}
  </script>
  <style lang="scss" scoped>
.main {
  margin: 0;
  padding: 0 0 12% 0;
  background: url("~@/assets/images/imageKQT/bg.png") center/100% 100% no-repeat;
  .header-bar {
    position: absolute;
    height: 6vh;
    width: 100%;
    text-align: center;
    font-size: 1.6rem;
    font-weight: bolder;
    letter-spacing: 10px;
    background: url("~@/assets/images/imageKQT/headerBg.png") center/100% 100%
      no-repeat;
  }
  .bottom-barBg {
    display: flex;
    position: absolute;
    width: 100%;
    height: 91.8%;
    margin: 10vh 0 0 0;
    .leftBarBg {
      flex: 1;
      background: url("~@/assets/images/imageKQT/2.png") 55% 100%/102% 100%
        no-repeat;
    }
    .rightBarBg {
      width: 310px;
      background: url("~@/assets/images/imageKQT/3.png") 80% 100%/120% 100%
        no-repeat;
    }
  }
  .box {
    display: flex;
    height: 99px;
    position: sticky;
    top: 5vh;
    .left {
      flex: 1;
      padding: 0 4vw 0 1.3vw;
      margin: 0 0 0 10px;
      background: url("~@/assets/images/imageKQT/11.png") center/100% 100%
        no-repeat;
      .coordinate_axis1 {
        direction: rtl;
        display: flex;
        /*  height: 20px; */
        position: relative;
        .tick {
          position: absolute;
          top: 0;
          width: 1px;
          height: 8px;
          background-color: #ccc;
        }
        .tick-label {
          position: absolute;
          top: 0px;
          color: #666;
          transform: translateX(-50%) translateY(-136%);
          text-align: center;
        }
      }
    }

    .right {
      width: 310px;
      flex-shrink: 0;
      flex-grow: 0;
      margin: 0 0 0 10px;
      height: 37px;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      align-content: space-around;
      justify-content: space-between;
      align-items: center;
      .tab-bar {
        line-height: 30px;
        width: 30%;
        height: 100%;
        color: #233263;
        font-size: 14px;
        font-weight: bolder;
        text-align: center;
        cursor: pointer;
        background: url("~@/assets/images/imageKQT/5.png") center/100% 100%
          no-repeat;
        &:hover,
        &.active {
          color: #fff;
          background: url("~@/assets/images/imageKQT/4.png") center/100% 100%
            no-repeat;
        }
      }
    }
  }
  .boxDJ {
    display: flex;
    height: 7.6%;
    position: absolute;
    width: 100%;
    .lineDJ {
      flex: 1;
      height: 18px;
      background: url("~@/assets/images/imageKQT/22.png") center/100% 100%
        no-repeat;
      margin: 30px 10px 20px 38px;
    }
    .lineDJ1 {
      width: 320px;
      flex-shrink: 0;
      flex-grow: 0;
      background: transparent;
    }
  }
  .container {
    width: 79.8%;
    height: 108px;
    position: absolute;
    margin: -20px 0 0 30px;
    overflow: hidden;
    .bigMon {
      width: 38px;
      height: 36px;
      border-radius: 80px;
      position: absolute;
      margin: 2.6% 0 0 0;
      border: 2px solid #000000;
      background: rgb(253 33 33 / 100%);
      z-index: 2;
    }
    .materialIn {
      width: 18px;
      height: 18px;
      border-radius: 80px;
      margin: 1% 0 0 0;
      position: absolute;
      z-index: 2;
      border: 2px solid #000000;
    }
    .connector {
      position: absolute;
      width: 1400px;
      height: 80px;
      pointer-events: none;
      z-index: 1;
    }
  }
}
</style>