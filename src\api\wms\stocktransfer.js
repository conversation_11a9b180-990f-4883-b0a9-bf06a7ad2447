import request from '@/utils/request'

export function selectTransferData(query){
    return request({
        url: '/api/wms/stockTransfer/list',
        method: 'get',
        params: query
      });
}

export function insertTransferData(data){
  return request({
      url: '/api/wms/stockTransfer/insert',
      method: 'post',
      data: data
    });
}

export function updateTransferData(data){
  return request({
      url: '/api/wms/stockTransfer/update',
      method: 'put',
      data: data
    });
}

export function deleteTransferData(ids) {
  const idArray = Array.isArray(ids) ? ids : [ids]
  const encodedIds = idArray
    .map(id => encodeURIComponent(id))
    .join(',') 
  return request({
    url: `/api/wms/stockTransfer/delete/${encodedIds}`, 
    method: 'post',
  });
}