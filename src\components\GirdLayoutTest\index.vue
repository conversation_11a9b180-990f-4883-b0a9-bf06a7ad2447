<template>
  <div class="grid_box">
    <div class="grid_title" :style="{ 'writing-mode': mainTitle == '混匀' ? 'vertical-rl' : 'normal'  }">{{ mainTitle }}</div>
    <div class="grid_left">
      <grid-layout :layout.sync="layoutData" :col-num="700" :row-height="43" :is-draggable="true" :is-resizable="true" :responsive="false" :is-mirrored='true'
                   :vertical-compact="false" :prevent-collision="true" :margin="[0, 0]" :use-css-transforms="true" @layout-created="layoutCreatedEvent"
                   @layout-before-mount="layoutBeforeMountEvent" @layout-mounted="layoutMountedEvent" @layout-ready="layoutReadyEvent"
                   @layout-updated="layoutUpdatedEvent" @dragstop="onDragStop">
        <grid-item v-for="(item,index) in layoutData" :static="false" :style="{ 'zIndex':1}" :x="item.x" :y="0" :w="item.w" :h="item.h" :i="item.i"
                   :key="item.i" :min-h="item.minH" :max-h="item.maxH" :resizable-handles="['l', 'r']" @resize="resizeEvent" @move="moveEvent"
                   @resized="resizedEvent" @moved="movedEvent" @contextmenu.prevent="onRightClick(index, $event,item)"
                   @contextmenu.native.prevent="onRightClick(index, $event,item)" @mouseenter.native="showInfo(item)" @mouseleave.native="hideInfo">
          <div class="text" @contextmenu.prevent="onRightClick(index, $event,item)">
            <div>{{ item.materialName }}</div>
            <div class="numMark">{{ 495 }} <span>T</span></div>
          </div>
          <div class="item-content">
            <transition name="fade">
              <div v-if="hoverItem === item" class="item-tooltip">
                <div>位置: ({{ item.x }}, {{ item.y }}) - 位置: ({{ item.x }}, {{ item.y }})</div>
                <div>尺寸: {{ item.w }} × {{ item.h }} - 尺寸: {{ item.w }} × {{ item.h }}</div>
                <div>位置: ({{ item.x }}, {{ item.y }}) - 位置: ({{ item.x }}, {{ item.y }})</div>
                <div>尺寸: {{ item.w }} × {{ item.h }} - 尺寸: {{ item.w }} × {{ item.h }}</div>
              </div>
            </transition>
          </div>
        </grid-item>
      </grid-layout>
    </div>
    <div class="grid_right">
      <div class="data_list scrollbar_box">
        <div class="list_item" v-for="item in layoutData" :key="item.i">
          <div class="item_title" v-html="item.materialName"></div>
          <div class="item_Num" style="margin: 0 20px 0 0;">{{ item.x  +' - '+  (item.x + item.w) }} </div>
          <div class="item_Num">X <el-input-number v-model="item.x" size="mini" controls-position="right" /> </div>
          <div class="item_Num">W <el-input-number v-model="item.w" size="mini" controls-position="right" /> </div>
        </div>
      </div>
    </div>
    <div v-show="contextMenu.visible" class="context-menu" :style="{
        left: contextMenu.x + 'px',
        top: contextMenu.y + 'px'
      }">
      <div class="menu-item">入库</div>
      <div class="menu-item">出库</div>
      <div class="menu-item">盘点数据</div>
      <div class="menu-item">倒垛</div>
      <!-- <div class="menu-item" @click="handleAction('detail')">查看详情</div>
      <div class="menu-item" @click="handleAction('edit')">编辑</div>
      <div class="menu-item" @click="handleAction('delete')">删除</div> -->
    </div>
  </div>
</template>
<script>
import { GridLayout, GridItem } from "vue-grid-layout"

export default {
  name: 'GirdLayoutTest',
  props: {
    layoutData: {
      type: Array,
      default: null,
    },
    mainTitle: {
      type: String,
      default: "",
    }
  },
  components: { GridLayout, GridItem },
  data() {
    return {
      contextMenu: {
        visible: false,
        x: 0,
        y: 0
      },
      hoverItem: null
    }
  },
  computed: {
  },
  methods: {
    showInfo(item) {
      this.hoverItem = item;
    },
    hideInfo() {
      this.hoverItem = null;
    },
    /**
     * 对应Vue生命周期的created
     * */
    layoutCreatedEvent: function (newLayout) {
      // console.log("Created layout: ", newLayout)
    },
    /**
     * 对应Vue生命周期的beforeMount
     * */
    layoutBeforeMountEvent: function (newLayout) {
      // console.log("beforeMount layout: ", newLayout)
    },
    /**
     * 对应Vue生命周期的mounted
     * */
    layoutMountedEvent: function (newLayout) {
      // console.log("Mounted layout: ", newLayout)
    },
    /**
     * 当完成mount中的所有操作时生成的事件
     * */
    layoutReadyEvent: function (newLayout) {
      // console.log("Ready layout: ", newLayout)
    },
    /**
     * 更新事件（布局更新或栅格元素的位置重新计算）
     * */
    layoutUpdatedEvent: function (newLayout) {
      // console.log("Updated layout: ", newLayout)
    },
    /**
     * 移动时的事件
     * */
    moveEvent: function (i, newX, newY) {
      // console.log("MOVE i=" + i + ", X=" + newX + ", Y=" + newY);
    },
    /**
     * 调整大小时的事件
     * */
    resizeEvent: function (i, newH, newW, newHPx, newWPx) {
      // console.log("RESIZE i=" + i + ", H=" + newH + ", W=" + newW + ", H(px)=" + newHPx + ", W(px)=" + newWPx);
    },
    /**
     * 移动后的事件
     * */
    movedEvent: function (i, newX, newY) {
      // console.log("MOVED i=" + i + ", X=" + newX + ", Y=" + newY);
    },
    /**
     * 调整大小后的事件
     * @param i the item id/index
     * @param newH new height in grid rows
     * @param newW new width in grid columns
     * @param newHPx new height in pixels
     * @param newWPx new width in pixels
     *
     */
    resizedEvent: function (i, newH, newW, newHPx, newWPx) {
      //console.log("RESIZED i=" + i + ", H=" + newH + ", W=" + newW + ", H(px)=" + newHPx + ", W(px)=" + newWPx);
    },
    checkCollision(newItem) {
      return this.layoutData.some(item => {
        return (
          item.i !== newItem.i && // 排除自身
          newItem.x < item.x + item.w &&
          newItem.x + newItem.w > item.x &&
          newItem.y < item.y + item.h &&
          newItem.y + newItem.h > item.y
        );
      });
    },
    onDragStop(newLayout) {
      // 拖拽结束后强制重置 y=0，防止用户手动拖到纵向位置
      this.layoutData = newLayout.map(item => ({ ...item, y: 0 }));
      const movedItem = newLayout.find(item => item.isMoved);
      if (movedItem && this.checkCollision(movedItem)) {
        this.$message.error("不允许覆盖其他模块！");
        // 恢复原位置
        this.layoutData = [...this.layoutData];
      }
    },
    onRightClick(index, event, item) {
      // 计算菜单位置
      const { clientX, clientY } = event
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight
      const menuWidth = 160
      const menuHeight = 120

      this.contextMenu = {
        visible: true,
        x: clientX + menuWidth > viewportWidth ? viewportWidth - menuWidth : clientX,
        y: clientY + menuHeight > viewportHeight ? viewportHeight - menuHeight : clientY
      }

      // 绑定全局点击关闭
      document.addEventListener('click', this.closeContextMenu)
    },
    closeContextMenu() {
      this.contextMenu.visible = false
      document.removeEventListener('click', this.closeContextMenu)
    },
    handleAction(action) {
      this.$emit('contextmenu-action', {
        action,
        data: this.layoutData
      })
      this.closeContextMenu()
    },
  },
  beforeDestroy() {
    // 清理事件监听
    document.removeEventListener('click', this.closeContextMenu)
  }
}
</script>
<style lang="scss">
.grid_box {
  font-family: Arial, Helvetica, sans-serif;
  display: flex;
  height: 98px;
  position: relative;
  margin: 68px 0 0 0;
  .grid_title {
    height: 100%;
    text-align: center;
    font-size: 22px;
    font-weight: bolder;
    position: absolute;
    letter-spacing: 5px;
    color: #fff;
    text-shadow: 0 0 5px #80add6, /* 外发光效果 */ 0 0 10px #80add6,
      0 0 15px #80add6, 0 0 20px #80add6;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 0 0 0 15px;
  }
  .grid_left {
    flex: 1;
    margin: 0 4vw 0 2vw;
    padding: 10px 0;
    justify-content: space-evenly;
    display: flex;
    flex-direction: column;
    .coordinate_axis {
      display: flex;
      .axis_item {
        flex: 1;
        position: relative;
        height: 8px;
        border-top: 2px solid #ccc;
        border-right: 2px solid #ccc;
        &:first-child {
          border-left: 2px solid #ccc;
          &::before {
            content: "0";
            position: absolute;
            display: inline-block;
            top: 0;
            left: 0;
            transform: translate(-50%, -100%);
            color: #666;
          }
        }
        span {
          position: absolute;
          display: inline-block;
          top: 0;
          right: 0;
          transform: translate(50%, -100%);
          color: #666;
        }
      }
    }
    .vue-grid-layout {
      .vue-grid-item {
        touch-action: pan-x; /* 只允许横向触摸拖拽 */
        user-select: none;
        border: 1px solid #40E4FF;
        background: url("~@/assets/images/imageKQT/singleBG.png") center/100%
          100% no-repeat;
        &:not(.vue-grid-placeholder) {
          /* border: none; */
          /* border-radius: 6px; */
        }
        .text {
          font-size: 18px;
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          height: 100%;
          width: 100%;
          display: flex;
          flex-direction: column;
          flex-wrap: nowrap;
          align-content: center;
          justify-content: center;
          align-items: center;
          color: #000;
          z-index: 2;
          .numMark {
            color: #33abf2;
            font-size: 22px;
            > span {
              color: rgb(36, 36, 36);
              font-size: 13px;
            }
          }
        }
        .item-content {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          position: relative;
          padding: 10px;
          box-sizing: border-box;
          .item-tooltip {
            position: absolute;
            bottom: calc(100% + 0px);
            left: 50%;
            transform: translateX(-22%);
            background: url("~@/assets/images/imageKQT/messageR.png") center /
              100% 100% no-repeat;
            color: white;
            padding: 6px 12px 22px 12px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 20;
          }
        }
        :hover,
        &.active {
          color: #fff;
          background: url("~@/assets/images/imageKQT/singleBgClick.png")
            center/100% 100% no-repeat;
          border: none;
          .numMark {
            color: #fff;
            > span {
              color: #fff;
            }
          }
        }
        .vue-resizable-handle {
          background: transparent;
          z-index: 3;
          &::after {
            content: "";
            position: absolute;
            bottom: 3px;
            left: 2px;
            width: 10px;
            height: 10px;
            background: linear-gradient(
              to bottom left,
              transparent 0%,
              transparent calc(75% - 1px),
              #72eaff 75%,
              transparent calc(75% + 1px),
              transparent calc(90% - 1px),
              #72eaff 90%,
              transparent calc(90% + 1px),
              transparent 100%
            );
          }
        }
      }
    }
  }
  .grid_right {
    width: 320px;
    height: 150px;
    flex-shrink: 0;
    flex-grow: 0;
    padding: 0 10px 0;
    .data_list {
      padding: 0 10px 0 10px;
      margin: -20px 0 0 0;
      height: calc(110% - 0px);
      overflow-y: scroll;
      display: flex;
      flex-direction: column;
      flex-wrap: nowrap;
      align-items: stretch;
      justify-content: center;
      &::-webkit-scrollbar {
        display: none;
      }
      .list_item {
        color: #000;
        font-size: 14px;
        display: flex;
        justify-content: flex-end;
        padding: 5px 0;
        border-bottom: 1px dashed rgb(0, 0, 0);
        > div {
          display: flex;
          align-items: center;
          &.item_title {
            white-space: nowrap;
            flex: 1;
          }
          .el-input-number {
            margin: 0 2px;
            width: 40px;
            > span {
              width: 8px;
              i {
                font-size: 8px;
              }
            }
            .el-input {
              input {
                padding: 0 10px 0 2px;
              }
            }
          }
        }
      }
    }
  }
  .context-menu {
    position: fixed;
    background: url("~@/assets/images/imageKQT/messageL.png") 58% 100%/128% 100%
      no-repeat;
    z-index: 9999;
    padding: 10px 0 6px 0;
    font-family: Arial, Helvetica, sans-serif;
    .menu-item { 
      margin: 3px 3px 0px 16px;
      font-size: 14px;
      padding: 2px 16px 2px 2px;
      color: #1C63AF;
      cursor: pointer;
      transition: background-color 0.3s;
      background-color: #DDEAFD;

      &:hover {
        background-color: #488FF7;
        color: #fff;
      }
    }
  }

  * {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
  }
}
</style>