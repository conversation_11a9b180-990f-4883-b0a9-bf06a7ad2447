import request from '@/utils/request'

// 查询启停机管理列表
export function listStop(query, selectVO) {
  return request({
    url: '/api/cpes/startStop/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询启停机管理详细
export function getStop(startStopId) {
  return request({
    url: '/api/cpes/startStop/' + startStopId,
    method: 'get'
  })
}

// 新增启停机管理
export function addStop(data) {
  return request({
    url: '/api/cpes/startStop',
    method: 'post',
    data: data
  })
}

// 修改启停机管理
export function updateStop(data) {
  return request({
    url: '/api/cpes/startStop',
    method: 'put',
    data: data
  })
}

// 删除启停机管理
export function delStop(startStopId) {
  return request({
    url: '/api/cpes/startStop/' + startStopId,
    method: 'delete'
  })
}

export function queryforManger(query) {
  return request({
    url: '/api/cpes/startStop/queryforManger',
    method: 'get',
    params: query
  })
}

export function getEdit(query) {
  return request({
    url: '/api/cpes/startStop/getEdit',
    method: 'get',
    params: query
  })
}

export function deleteByMoPar(query) {
  return request({
    url: '/api/cpes/startStop/deleteByMoPar',
    method: 'get',
    params: query
  })
}

export function saveOrUpdate(data) {
  return request({
    url: '/api/cpes/startStop/saveOrUpdate',
    method: 'post',
    data: data
  })
}
