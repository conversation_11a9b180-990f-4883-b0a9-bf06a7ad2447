<template>
  <div class="app-container" style="padding: 0px;">
    <div class="furnace-selector" style="margin-top:8px; padding: 8px 12px; background: #f8f9fa; border-radius: 6px; border: 1px solid #e9ecef;">
      <div class="furnace-buttons" style="display: flex; align-items: center; gap: 10px;">
        <!-- <span style="font-weight: 600; color: #2c3e50; font-size: 13px;">高炉监控:</span> -->
        <el-button
          :type="selectParam.prodCenterCode === 'IPES01' ? 'primary' : 'default'"
          :class="{ 'active-furnace': selectParam.prodCenterCode === 'IPES01' }"
          @click="switchFurnace('IPES01')"
          size="small"
          class="compact-button"
          style="min-width: 70px; height: 26px; padding: 2px 8px; font-size: 12px;">
          1#高炉
        </el-button>
        <el-button
          :type="selectParam.prodCenterCode === 'IPES02' ? 'primary' : 'default'"
          :class="{ 'active-furnace': selectParam.prodCenterCode === 'IPES02' }"
          @click="switchFurnace('IPES02')"
          size="small"
          class="compact-button"
          style="min-width: 70px; height: 26px; padding: 2px 8px; font-size: 12px;">
          2#高炉
        </el-button>
      </div>
    </div>
    <!-- 上半部分：表格展示 -->
    <div style="margin-top: 7px; ">
      <vxe-table
        border
        align="center"
        :data="tableData"
        :row-config="{ height: 35 }"
        :header-row-config="{ height: 40 }"
        class="compact-table">
        <vxe-column v-for="(column, index) in columns" :key="`col-${column.field}-${index}`" :field="column.field" :title="column.title" width="auto"></vxe-column>
      </vxe-table>
    </div>

    <!-- 下半部分：料仓可视化展示 -->
    <BinChart :siloData="siloData" />
    <!-- <BinComponent :siloData="siloData" /> -->
  </div>
</template>

<script>
  import {slotPositionDataSelect,} from "@/api/report/preview/blastFurnace/slotPosition";
  import { request } from '@/utils/request';
  import BinChart from './binChart.vue';
  import BinComponent from './binComponent.vue';
    export default {
        name: "slotPosition",
      components: {
        BinChart,
        BinComponent
      },
      data(){
          return{
            selectParam:{
              prodCenterCode:'IPES01',
              status:'生效',
            },
            prodCenterList:[
              {label: '1#高炉', value: 'IPES01'},
              {label: '2#高炉', value: 'IPES02'},
            ],
            siloData: [], // 存储原始料仓数据
            tableData:[
              {
                typeName: '料仓名',
              },
              {
                typeName: '物料名',
              },
              {
                typeName: '重量',
              },
              {
                typeName: '料位高度',
              },
              {
                typeName: '料位百分比',
              },
            ],
            columns: [
              // { field: 'typeName', title: '类型' ,width:'auto'},
            ]
          }
      },
      created() {
          this.initData();
      },
      beforeDestroy() {
          this.stopAutoRefresh();
      },
      methods:{
          /* 搜索按钮触发*/
        handleQuery(){
          this.stopAutoRefresh();
          this.queryLists();
          this.startAutoRefresh();
        },

        /* 切换高炉 */
        switchFurnace(furnaceCode) {
          if (this.selectParam.prodCenterCode !== furnaceCode) {
            this.selectParam.prodCenterCode = furnaceCode;
            this.stopAutoRefresh();
            this.columns = []; // 重置列结构
            this.tableData = []; // 重置表格数据
            this.siloData = []; // 重置料仓数据
            this.initData(); // 重新初始化数据
          }
        },

        /* 获取当前高炉名称 */
        getCurrentFurnaceName() {
          const furnace = this.prodCenterList.find(item => item.value === this.selectParam.prodCenterCode);
          return furnace ? furnace.label : '未选择';
        },

        /* 格式化时间 */
        formatTime(date) {
          const hours = date.getHours().toString().padStart(2, '0');
          const minutes = date.getMinutes().toString().padStart(2, '0');
          const seconds = date.getSeconds().toString().padStart(2, '0');
          return `${hours}:${minutes}:${seconds}`;
        },

        /* 初始化数据 */
        initData(){
          slotPositionDataSelect(this.selectParam).then(response=>{
            if(response.data && response.data.length > 0){
              let siloData = response.data
              this.siloData = siloData

              // 构建表格列（只在初始化时执行一次）
              this.columns.push({ field: 'typeName', title: '料仓名称', width: '120'})
              for(let i = 0; i < siloData.length; i++){
                let silo = siloData[i]
                this.columns.push({
                  title: silo.binName,
                  field: `silo_${silo.binId}`,
                  width: '100'
                })
              }

              // 构建表格数据
              this.buildTableData()

              // 开始定时刷新
              this.startAutoRefresh()
            }
          }).catch(error => {
            console.error('初始化数据失败:', error)
          })
        },

        /* 数据展示 */
        queryLists(){
          if (this.isRequesting) return;

          this.isRequesting = true;
          slotPositionDataSelect(this.selectParam).then(response=>{
            console.log('完整API响应:', response);

            // 安全地获取数据
            let responseData = null;
            try {
              if (response && response.data) {
                responseData = response.data;
                console.log('获取到的数据:', responseData);
              }
            } catch (e) {
              console.error('数据解析错误:', e);
            }

            if(responseData && Array.isArray(responseData) && responseData.length > 0){
              this.siloData = responseData;
              this.buildTableData(); // 只更新数据，不重建列
            } else {
              console.warn('没有获取到有效的料仓数据');
            }
            this.isRequesting = false;
          }).catch(error => {
            console.error('数据更新失败:', error)
            this.isRequesting = false;
          })
        },

        /* 开始自动刷新 */
        startAutoRefresh() {
          this.timer = setInterval(() => {
            this.queryLists(); // 每1秒调用接口更新数据
          }, 1000);
        },

        /* 停止自动刷新 */
        stopAutoRefresh() {
          if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
          }
        },

        /* 构建表格数据 */
        buildTableData(){

          this.tableData = [
            { typeName: '物料名' },
            { typeName: '重量' },
            { typeName: '料位高度' },
            { typeName: '料位百分比' }
          ]

          // 每行数据添加料仓值
          this.tableData.forEach((row) => {
            this.siloData.forEach(silo => {
              const fieldName = `silo_${silo.binId}`

              switch(row.typeName) {
                case '料仓名':
                  row[fieldName] = silo.binName
                  break
                case '物料名':
                  row[fieldName] = silo.materialName || silo.binType || '-'
                  break
                case '重量':
                  row[fieldName] = silo.materialWeight ? silo.materialWeight.toFixed(1) : '0.0'
                  break
                case '料位高度':
                  row[fieldName] = silo.materialHight ? silo.materialHight.toFixed(2) :
                                  (silo.binHigh ? silo.binHigh.toFixed(2) : '0.00')
                  break
                case '料位百分比':
                  const percentage = this.calculatePercentage(silo)
                  row[fieldName] = percentage
                  break
              }
            })
          })

        },

        /* 百分比 */
        calculatePercentage(silo){
          if (silo.materialPercentage !== null && silo.materialPercentage !== undefined) {
            // 处理字符串格式的百分比（如"62.39600%"）
            if (typeof silo.materialPercentage === 'string') {
              const numStr = silo.materialPercentage.replace('%', '');
              const num = parseFloat(numStr);
              return isNaN(num) ? 0 : num.toFixed(1);
            }
            return silo.materialPercentage;
          }
          return "0.0"
        }
      }
    }
</script>

<style scoped>

.furnace-selector {
  transition: all 0.3s ease;
}

.furnace-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}


.compact-button {
  line-height: 1 !important;
  border-radius: 4px !important;
}

.compact-button span {
  font-size: 12px !important;
}


.active-furnace {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
  transform: translateY(-1px);
  transition: all 0.3s ease;
}


.el-button:hover {
  transform: translateY(-1px);
  transition: all 0.3s ease;
}


.furnace-status {
  font-family: 'Courier New', monospace;
}

.current-furnace {
  font-weight: 600;
  color: #2c3e50 !important;
}

.update-time {
  color: #27ae60 !important;
  font-size: 11px !important;
}


.compact-table {
  font-size: 12px;
}


.compact-table .vxe-header--column {
  font-size: 13px !important;
  font-weight: 600 !important;
  padding: 6px 8px !important;
}


.compact-table .vxe-body--column {
  font-size: 12px !important;
  padding: 4px 8px !important;
  line-height: 1.2 !important;
}


.compact-table .vxe-body--row {
  height: 35px !important;
}

.compact-table .vxe-header--row {
  height: 40px !important;
}


.compact-table .vxe-table--border-line {
  border-color: #e4e7ed;
}


.compact-table .vxe-body--row:hover {
  background-color: #f5f7fa !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .furnace-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .furnace-selector {
    padding: 6px 10px !important;
  }

  .compact-button {
    width: 100%;
    height: 24px !important;
    font-size: 11px !important;
  }

  /* 移动端表格进一步压缩 */
  .compact-table {
    font-size: 11px;
  }

  .compact-table .vxe-header--column {
    font-size: 12px !important;
    padding: 4px 6px !important;
  }

  .compact-table .vxe-body--column {
    font-size: 11px !important;
    padding: 3px 6px !important;
  }

  .compact-table .vxe-body--row {
    height: 30px !important;
  }

  .compact-table .vxe-header--row {
    height: 35px !important;
  }
}
</style>
