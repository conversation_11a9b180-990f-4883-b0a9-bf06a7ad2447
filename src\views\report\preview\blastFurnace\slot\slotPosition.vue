<template>
  <div class="app-container" style="padding: 0px;">
    <div class="block" style="margin-top:8px;">
      <span style=" margin-left: 10px;">高炉号:</span>
      <el-select v-model="selectParam.prodCenterCode" placeholder="请选择"  style="width: 200px;margin-left: 10px;" >
        <el-option v-for="item in prodCenterList" :key="item.value" :label="item.label" :value="item.value" ></el-option>
      </el-select>
      <el-button type="primary" style="margin-left: 10px;" @click="handleQuery" size="mini">搜索</el-button>
    </div>
    <!-- 上半部分：表格展示 -->
    <div style="margin-top: 7px; margin-bottom: 20px;">
      <vxe-table
        border
        align="center"
        :data="tableData">
        <vxe-column v-for="column in columns" :key="column.title" :field="column.field" :title="column.title" width="auto"></vxe-column>
      </vxe-table>
    </div>

    <!-- 下半部分：料仓可视化展示 -->
    <BinChart :siloData="siloData" />
  </div>
</template>

<script>
  import {
    slotPositionDataSelect,
  } from "@/api/report/preview/blastFurnace/slotPosition";
  import { request } from '@/utils/request';
  import BinChart from './binChart.vue';
    export default {
        name: "slotPosition",
      components: {
        BinChart
      },
      data(){
          return{
            selectParam:{
              prodCenterCode:'IPES01',
              status:'生效',
            },
            prodCenterList:[
              {label: '1#高炉', value: 'IPES01'},
              {label: '2#高炉', value: 'IPES02'},
            ],
            siloData: [], // 存储原始料仓数据
            tableData:[
              {
                typeName: '料仓名',
              },
              {
                typeName: '物料名',
              },
              {
                typeName: '重量',
              },
              {
                typeName: '料位高度',
              },
              {
                typeName: '料位百分比',
              },
            ],
            columns: [
              // { field: 'typeName', title: '类型' ,width:'auto'},
            ]
          }
      },
      created() {
          this.queryLists();
      },
      methods:{
          /* 搜索按钮触发*/
        handleQuery(){
          this.queryLists();
        },

        /* 数据展示 */
        queryLists(){
          slotPositionDataSelect(this.selectParam).then(response=>{
            this.columns = []
            if(response.data && response.data.length > 0){
              let siloData = response.data
              this.siloData = siloData

              // 构建表格列
              this.columns.push({ field: 'typeName', title: '料仓名称', width: '120'})

              // 料仓创建一列
              for(let i = 0; i < siloData.length; i++){
                let silo = siloData[i]
                this.columns.push({
                  title: silo.binName,
                  field: `silo_${silo.binId}`,
                  width: '100'
                })
              }

              // 重新构建表格数据
              this.buildTableData()
            }
          }).catch(error => {
            console.error('调用失败:', error)
          })
        },

        /* 构建表格数据 */
        buildTableData(){

          this.tableData = [
            { typeName: '物料名' },
            { typeName: '重量' },
            { typeName: '料位高度' },
            { typeName: '料位百分比' }
          ]

          // 每行数据添加料仓值
          this.tableData.forEach((row) => {
            this.siloData.forEach(silo => {
              const fieldName = `silo_${silo.binId}`

              switch(row.typeName) {
                case '料仓名':
                  row[fieldName] = silo.binName
                  break
                case '物料名':
                  row[fieldName] = silo.materialName || silo.materialName || '-'
                  break
                case '重量':
                  row[fieldName] = silo.materialWeight ? silo.materialWeight.toFixed(1) : '0.0'
                  break
                case '料位高度':
                  row[fieldName] = silo.materialHight ? silo.materialHight.toFixed(2) :
                                  (silo.binHigh ? silo.binHigh.toFixed(2) : '0.00')
                  break
                case '料位百分比':
                  const percentage = this.calculatePercentage(silo)
                  row[fieldName] = percentage
                  break
              }
            })
          })

        },

        /* 百分比 */
        calculatePercentage(silo){
          if (silo.materialPercentage) {
            return silo.materialPercentage
          }
          return "-"
        }
      }
    }
</script>

<style scoped>

</style>
