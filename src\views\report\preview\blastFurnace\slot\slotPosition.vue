<template>
  <div class="app-container" style="padding: 0px;">
    <div class="block" style="margin-top:8px;">
      <span style=" margin-left: 10px;">高炉号:</span>
      <el-select v-model="selectParam.prodCenterCode" placeholder="请选择"  style="width: 200px;margin-left: 10px;" >
        <el-option v-for="item in prodCenterList" :key="item.value" :label="item.label" :value="item.value" ></el-option>
      </el-select>
      <el-button type="primary" style="margin-left: 10px;" @click="handleQuery" size="mini">搜索</el-button>
    </div>
    <!-- 上半部分：表格展示 -->
    <div style="margin-top: 7px; margin-bottom: 20px;">
      <vxe-table
        border
        align="center"
        :data="tableData">
        <vxe-column v-for="column in columns" :key="column.title" :field="column.field" :title="column.title" width="auto"></vxe-column>
      </vxe-table>
    </div>

    <!-- 下半部分：料仓可视化展示 -->
    <div class="silo-container" style="display:hidden;">
      <div class="silo-header">
        <h3>料仓装货状态监控</h3>
        <span class="silo-count">共 {{ siloData.length }} 个料仓</span>
      </div>

      <div class="silo-grid">
        <div
          v-for="silo in siloData"
          :key="silo.binId"
          class="silo-item">

          <!-- 料仓名称 -->
          <div class="silo-name">{{ silo.binName }}</div>

          <!-- 料仓可视化容器 -->
          <div class="silo-visual">
            <!-- 料仓外壳 -->
            <div class="silo-shell">
              <!-- 料仓顶部 -->
              <div class="silo-top"></div>

              <!-- 料仓主体 -->
              <div class="silo-body">
                <!-- 物料填充 -->
                <div
                  class="silo-material"
                  :style="{
                    height: getPercentage(silo) + '%',
                    background: getMaterialColor(silo.binType)
                  }">
                </div>

                <!-- 料位刻度线 -->
                <div class="silo-scale">
                  <div v-for="i in 5" :key="i" class="scale-line" :style="{ bottom: (i-1) * 20 + '%' }">
                    <span class="scale-text">{{ (i-1) * 20 }}%</span>
                  </div>
                </div>
              </div>

              <!-- 料仓底部出料口 -->
              <div class="silo-bottom"></div>
            </div>
          </div>

          <!-- 料仓信息 -->
          <div class="silo-info">
            <div class="info-item">
              <span class="info-label">类型:</span>
              <span class="info-value">{{ silo.binType }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">重量:</span>
              <span class="info-value">{{ silo.materialWeight ? silo.materialWeight.toFixed(1) + 't' : '0.0t' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">料位:</span>
              <span class="info-value" :style="{ color: getPercentageColor(silo) }">
                {{ getPercentage(silo) }}%
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import {
    slotPositionDataSelect,
  } from "@/api/report/preview/blastFurnace/slotPosition";
  import { request } from '@/utils/request';
    export default {
        name: "slotPosition",
      data(){
          return{
            selectParam:{
              prodCenterCode:'IPES01',
              status:'生效',
            },
            prodCenterList:[
              {label: '1#高炉', value: 'IPES01'},
              {label: '2#高炉', value: 'IPES02'},
            ],
            siloData: [], // 存储原始料仓数据
            tableData:[
              {
                typeName: '料仓名',
              },
              {
                typeName: '物料名',
              },
              {
                typeName: '重量',
              },
              {
                typeName: '料位高度',
              },
              {
                typeName: '料位百分比',
              },
            ],
            columns: [
              // { field: 'typeName', title: '类型' ,width:'auto'},
            ]
          }
      },
      created() {
          this.queryLists();
      },
      methods:{
          /* 搜索按钮触发*/
        handleQuery(){
          this.queryLists();
        },

        /* 数据展示 */
        queryLists(){
          slotPositionDataSelect(this.selectParam).then(response=>{
            this.columns = []
            if(response.data && response.data.length > 0){
              let siloData = response.data
              this.siloData = siloData

              // 构建表格列
              this.columns.push({ field: 'typeName', title: '料仓名称', width: '120'})

              // 料仓创建一列
              for(let i = 0; i < siloData.length; i++){
                let silo = siloData[i]
                this.columns.push({
                  title: silo.binName,
                  field: `silo_${silo.binId}`,
                  width: '100'
                })
              }

              // 重新构建表格数据
              this.buildTableData()
            }
          }).catch(error => {
            console.error('调用失败:', error)
          })
        },

        /* 构建表格数据 */
        buildTableData(){

          this.tableData = [
            { typeName: '物料名' },
            { typeName: '重量' },
            { typeName: '料位高度' },
            { typeName: '料位百分比' }
          ]

          // 每行数据添加料仓值
          this.tableData.forEach((row) => {
            this.siloData.forEach(silo => {
              const fieldName = `silo_${silo.binId}`

              switch(row.typeName) {
                case '料仓名':
                  row[fieldName] = silo.binName
                  break
                case '物料名':
                  row[fieldName] = silo.materialName || silo.materialName || '-'
                  break
                case '重量':
                  row[fieldName] = silo.materialWeight ? silo.materialWeight.toFixed(1) : '0.0'
                  break
                case '料位高度':
                  row[fieldName] = silo.materialHight ? silo.materialHight.toFixed(2) :
                                  (silo.binHigh ? silo.binHigh.toFixed(2) : '0.00')
                  break
                case '料位百分比':
                  const percentage = this.calculatePercentage(silo)
                  row[fieldName] = percentage.toFixed(1)
                  break
              }
            })
          })

        },

        /* 百分比 */
        calculatePercentage(silo){
          if (silo.materialPercentage !== null && silo.materialPercentage !== undefined) {
            return silo.materialPercentage
          }
          return "-"
        },

        /* 获取料仓百分比（用于可视化） */
        getPercentage(silo) {
          const percentage = this.calculatePercentage(silo)
          if (percentage === "-") return 0
          return Math.round(percentage)
        },

        /* 根据物料类型获取颜色 */
        getMaterialColor(binType) {
          const colorMap = {
            '焦槽': 'linear-gradient(180deg, #2c3e50 0%, #34495e 100%)', // 深灰色（焦炭）
            '矿槽': 'linear-gradient(180deg, #8b4513 0%, #a0522d 100%)', // 棕色（矿石）
            'J10启动': 'linear-gradient(180deg, #e74c3c 0%, #c0392b 100%)', // 红色
            'J10运行': 'linear-gradient(180deg, #27ae60 0%, #229954 100%)', // 绿色
            'J7启动': 'linear-gradient(180deg, #f39c12 0%, #e67e22 100%)', // 橙色
            'J7运行': 'linear-gradient(180deg, #3498db 0%, #2980b9 100%)', // 蓝色
            '返焦料位': 'linear-gradient(180deg, #9b59b6 0%, #8e44ad 100%)', // 紫色
            '返矿料位': 'linear-gradient(180deg, #1abc9c 0%, #16a085 100%)', // 青色
          }
          return colorMap[binType] || 'linear-gradient(180deg, #95a5a6 0%, #7f8c8d 100%)' // 默认灰色
        },

        /* 根据料位百分比获取文字颜色 */
        getPercentageColor(silo) {
          const percentage = this.getPercentage(silo)
          if (percentage >= 80) return '#e74c3c' // 红色：料位过高
          if (percentage >= 60) return '#f39c12' // 橙色：料位较高
          if (percentage >= 30) return '#27ae60' // 绿色：料位正常
          return '#95a5a6' // 灰色：料位较低
        }
      }
    }
</script>

<style scoped>
/* 料仓容器样式 */
.silo-container {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

/* 料仓标题区域 */
.silo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #dee2e6;
}

.silo-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.silo-count {
  color: #6c757d;
  font-size: 14px;
  background: #e9ecef;
  padding: 4px 12px;
  border-radius: 12px;
}

/* 料仓网格布局 */
.silo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 20px;
  max-height: 600px;
  overflow-y: auto;
}

/* 单个料仓项 */
.silo-item {
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  border: 1px solid #e9ecef;
}

.silo-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

/* 料仓名称 */
.silo-name {
  text-align: center;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 14px;
}

/* 料仓可视化容器 */
.silo-visual {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}

/* 料仓外壳 */
.silo-shell {
  position: relative;
  width: 60px;
  height: 120px;
}

/* 料仓顶部 */
.silo-top {
  width: 70px;
  height: 8px;
  background: linear-gradient(135deg, #bdc3c7, #95a5a6);
  border-radius: 4px 4px 0 0;
  margin-left: -5px;
  border: 1px solid #7f8c8d;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

/* 料仓主体 */
.silo-body {
  position: relative;
  width: 60px;
  height: 100px;
  background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
  border: 2px solid #95a5a6;
  border-top: none;
  border-bottom: none;
  overflow: hidden;
}

/* 物料填充 */
.silo-material {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  transition: height 0.8s ease-in-out;
  border-radius: 0 0 2px 2px;
  opacity: 0.9;
}

/* 料位刻度 */
.silo-scale {
  position: absolute;
  right: -25px;
  top: 0;
  height: 100%;
  width: 20px;
}

.scale-line {
  position: absolute;
  right: 0;
  width: 8px;
  height: 1px;
  background: #7f8c8d;
}

.scale-text {
  position: absolute;
  right: 10px;
  top: -6px;
  font-size: 10px;
  color: #6c757d;
  white-space: nowrap;
}

/* 料仓底部 */
.silo-bottom {
  width: 40px;
  height: 12px;
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
  margin: 0 auto;
  border-radius: 0 0 6px 6px;
  border: 1px solid #7f8c8d;
  border-top: none;
  position: relative;
}

.silo-bottom::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 8px;
  height: 4px;
  background: #7f8c8d;
  border-radius: 0 0 2px 2px;
}

/* 料仓信息 */
.silo-info {
  font-size: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  padding: 2px 0;
}

.info-label {
  color: #6c757d;
  font-weight: 500;
}

.info-value {
  color: #2c3e50;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .silo-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
  }

  .silo-item {
    padding: 12px;
  }

  .silo-shell {
    width: 50px;
    height: 100px;
  }

  .silo-body {
    width: 50px;
    height: 80px;
  }

  .silo-top {
    width: 60px;
    margin-left: -5px;
  }
}
</style>
