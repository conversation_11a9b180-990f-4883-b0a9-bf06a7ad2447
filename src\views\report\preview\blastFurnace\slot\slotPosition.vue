<template>
  <div class="app-container" style="padding: 0px;">
    <div class="furnace-selector" style="margin-top:8px; padding: 15px; background: #f8f9fa; border-radius: 8px; border: 1px solid #e9ecef;">
      <div class="furnace-buttons" style="display: flex; align-items: center; gap: 15px;">
        
        <el-button
          :type="selectParam.prodCenterCode === 'IPES01' ? 'primary' : 'default'"
          :class="{ 'active-furnace': selectParam.prodCenterCode === 'IPES01' }"
          @click="switchFurnace('IPES01')"
          size="medium"
          style="min-width: 100px;">
          <!-- <i class="el-icon-s-home" style="margin-right: 5px;"></i> -->
          1#高炉
        </el-button>
        <el-button
          :type="selectParam.prodCenterCode === 'IPES02' ? 'primary' : 'default'"
          :class="{ 'active-furnace': selectParam.prodCenterCode === 'IPES02' }"
          @click="switchFurnace('IPES02')"
          size="medium"
          style="min-width: 100px;">
          <!-- <i class="el-icon-s-home" style="margin-right: 5px;"></i> -->
          2#高炉
        </el-button>
        <!-- <div class="furnace-status" style="margin-left: auto; display: flex; flex-direction: column; align-items: flex-end; font-size: 12px; color: #6c757d;">
          <span class="current-furnace">当前监控: {{ getCurrentFurnaceName() }}</span>
          <span class="update-time">最后更新: {{ formatTime(new Date()) }}</span>
        </div> -->
      </div>
    </div>
    <!-- 上半部分：表格展示 -->
    <div style="margin-top: 7px; margin-bottom: 20px;">
      <vxe-table
        border
        align="center"
        :data="tableData">
        <vxe-column v-for="(column, index) in columns" :key="`col-${column.field}-${index}`" :field="column.field" :title="column.title" width="auto"></vxe-column>
      </vxe-table>
    </div>

    <!-- 下半部分：料仓可视化展示 -->
    <!-- <BinChart :siloData="siloData" /> -->
    <BinComponent :siloData="siloData" />
  </div>
</template>

<script>
  import {
    slotPositionDataSelect,
  } from "@/api/report/preview/blastFurnace/slotPosition";
  import { request } from '@/utils/request';
  import BinChart from './binChart.vue';
  import BinComponent from './binComponent.vue';
    export default {
        name: "slotPosition",
      components: {
        BinChart,
        BinComponent
      },
      data(){
          return{
            selectParam:{
              prodCenterCode:'IPES01',
              status:'生效',
            },
            prodCenterList:[
              {label: '1#高炉', value: 'IPES01'},
              {label: '2#高炉', value: 'IPES02'},
            ],
            siloData: [], // 存储原始料仓数据
            tableData:[
              {
                typeName: '料仓名',
              },
              {
                typeName: '物料名',
              },
              {
                typeName: '重量',
              },
              {
                typeName: '料位高度',
              },
              {
                typeName: '料位百分比',
              },
            ],
            columns: [
              // { field: 'typeName', title: '类型' ,width:'auto'},
            ]
          }
      },
      created() {
          this.initData();
      },
      beforeDestroy() {
          this.stopAutoRefresh();
      },
      methods:{
          /* 搜索按钮触发*/
        handleQuery(){
          this.stopAutoRefresh();
          this.queryLists();
          this.startAutoRefresh();
        },

        /* 切换高炉 */
        switchFurnace(furnaceCode) {
          if (this.selectParam.prodCenterCode !== furnaceCode) {
            this.selectParam.prodCenterCode = furnaceCode;
            this.stopAutoRefresh();
            this.columns = []; // 重置列结构
            this.tableData = []; // 重置表格数据
            this.siloData = []; // 重置料仓数据
            this.initData(); // 重新初始化数据
          }
        },

        /* 获取当前高炉名称 */
        getCurrentFurnaceName() {
          const furnace = this.prodCenterList.find(item => item.value === this.selectParam.prodCenterCode);
          return furnace ? furnace.label : '未选择';
        },

        /* 格式化时间 */
        formatTime(date) {
          const hours = date.getHours().toString().padStart(2, '0');
          const minutes = date.getMinutes().toString().padStart(2, '0');
          const seconds = date.getSeconds().toString().padStart(2, '0');
          return `${hours}:${minutes}:${seconds}`;
        },

        /* 初始化数据 */
        initData(){
          slotPositionDataSelect(this.selectParam).then(response=>{
            if(response.data && response.data.length > 0){
              let siloData = response.data
              this.siloData = siloData

              // 构建表格列（只在初始化时执行一次）
              this.columns.push({ field: 'typeName', title: '料仓名称', width: '120'})
              for(let i = 0; i < siloData.length; i++){
                let silo = siloData[i]
                this.columns.push({
                  title: silo.binName,
                  field: `silo_${silo.binId}`,
                  width: '100'
                })
              }

              // 构建表格数据
              this.buildTableData()

              // 开始定时刷新
              this.startAutoRefresh()
            }
          }).catch(error => {
            console.error('初始化数据失败:', error)
          })
        },

        /* 数据展示 */
        queryLists(){
          if (this.isRequesting) return;

          this.isRequesting = true;
          slotPositionDataSelect(this.selectParam).then(response=>{
            if(response.data && response.data.length > 0){
              this.siloData = response.data;
              this.buildTableData(); // 只更新数据，不重建列
            }
            this.isRequesting = false;
          }).catch(error => {
            console.error('数据更新失败:', error)
            this.isRequesting = false;
          })
        },

        /* 开始自动刷新 */
        startAutoRefresh() {
          this.timer = setInterval(() => {
            this.queryLists(); // 每2秒调用接口更新数据
          }, 2000);
        },

        /* 停止自动刷新 */
        stopAutoRefresh() {
          if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
          }
        },

        /* 构建表格数据 */
        buildTableData(){

          this.tableData = [
            { typeName: '物料名' },
            { typeName: '重量' },
            { typeName: '料位高度' },
            { typeName: '料位百分比' }
          ]

          // 每行数据添加料仓值
          this.tableData.forEach((row) => {
            this.siloData.forEach(silo => {
              const fieldName = `silo_${silo.binId}`

              switch(row.typeName) {
                case '料仓名':
                  row[fieldName] = silo.binName
                  break
                case '物料名':
                  row[fieldName] = silo.materialName || silo.binType || '-'
                  break
                case '重量':
                  row[fieldName] = silo.materialWeight ? silo.materialWeight.toFixed(1) : '0.0'
                  break
                case '料位高度':
                  row[fieldName] = silo.materialHight ? silo.materialHight.toFixed(2) :
                                  (silo.binHigh ? silo.binHigh.toFixed(2) : '0.00')
                  break
                case '料位百分比':
                  const percentage = this.calculatePercentage(silo)
                  row[fieldName] = percentage
                  break
              }
            })
          })

        },

        /* 百分比 */
        calculatePercentage(silo){
          if (silo.materialPercentage) {
            return silo.materialPercentage
          }
          return "-"
        }
      }
    }
</script>

<style scoped>
/* 高炉选择器样式 */
.furnace-selector {
  transition: all 0.3s ease;
}

.furnace-buttons {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 激活状态的高炉按钮 */
.active-furnace {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
  transform: translateY(-1px);
  transition: all 0.3s ease;
}

/* 高炉按钮悬停效果 */
.el-button:hover {
  transform: translateY(-1px);
  transition: all 0.3s ease;
}

/* 状态信息样式 */
.furnace-status {
  font-family: 'Courier New', monospace;
}

.current-furnace {
  font-weight: 600;
  color: #2c3e50 !important;
}

.update-time {
  color: #27ae60 !important;
  font-size: 11px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .furnace-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .furnace-status {
    margin-left: 0 !important;
    align-items: center !important;
    margin-top: 10px;
  }

  .el-button {
    width: 100%;
  }
}
</style>
