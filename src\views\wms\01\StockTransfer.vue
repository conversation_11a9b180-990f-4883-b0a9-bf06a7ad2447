<template>
  <div>
    <vxe-grid ref="tableMainRef" v-bind="gridOptions" style="margin: 10px" @page-change="pageChangeEvent"
      @checkbox-change="selectChangeEvent" @checkbox-all="selectChangeEvent">
      <template #form>
        <vxe-form ref="formRef" v-bind="formOptions">
          <template #action>
            <vxe-button status="primary" @click="searchEvent">搜索</vxe-button>
            <vxe-button @click="resetEvent">重置</vxe-button>
          </template>
        </vxe-form>
      </template>
      <template #toolbarButtons>
        <vxe-button status="primary" @click="showForm('add')">新增</vxe-button>
        <vxe-button status="primary" @click="showForm('edit')">编辑</vxe-button>
        <vxe-button status="primary" @click="handleDelete">删除</vxe-button>
        <vxe-button status="primary" @click="handleExport">导出</vxe-button>
      </template>
    </vxe-grid>
    <!-- 数据操作弹窗 -->
    <vxe-modal v-model="formVisible" :title="formTitle" width="800" destroy-on-close @close="handleModalClose">
      <vxe-form ref="formDialogRef" v-bind="formDialogOptions">
        <template #action>
          <div style="text-align: center; margin-top: 20px">
            <vxe-button status="primary" @click="submitForm">提交</vxe-button>
            <vxe-button @click="formVisible = false">取消</vxe-button>
          </div>
        </template>
      </vxe-form>
    </vxe-modal>
  </div>
</template>

<script>
import {
  selectTransferData,
  insertTransferData,
  updateTransferData,
  deleteTransferData,
} from "@/api/wms/stocktransfer";
import { initMaterialList } from "@/api/md/material";


export default {
  name: "TransferData",
  data() {
    const stripEditRender = {
      name: 'VxeSelect',
      props: {
        clearable: true,
      },
      options: [
        { value: 'A', label: 'A' },
        { value: 'B', label: 'B' },
        { value: 'C', label: 'C' },
        { value: 'D', label: 'D' },
        { value: 'E', label: 'E' },
      ]
    }
    const materialEditRender = {
      name: 'VxeSelect',
      props: {
        filterable: true,
        clearable: true,
      },
      options: [],
      events: {
        change: this.changeMate
      }
    }
    return {
      stripEditRender,
      materialEditRender,

      // 表格配置
      gridOptions: {
        columns: [],
        data: [],
        height: 800,
        border: true,
        stripe: true,
        align: "center",
        columnConfig: { resizable: true },
        rowConfig: {
          isHover: true,
          isCurrent: true,
        },

        checkboxConfig: {
          checkField: "checked",
          highlight: true,
          range: true,
          trigger: "click",
        },

        pagerConfig: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        },

        toolbarConfig: {
          custom: true,
          zoom: true,
          slots: { buttons: "toolbarButtons" },
        },

        customConfig: { immediate: true },
        loading: false,
      },

      // 搜索表单
      formOptions: {
        data: {
          beginTime: "",
          endTime: "",
          crossRegion: "",
          mateCode: "",
        },
        items: [
          {
            field: "beginTime",
            title: "开始时间",
            itemRender: { name: "VxeDatePicker", props: { type: "datetime" } },
          },
          {
            field: "endTime",
            title: "结束时间",
            itemRender: { name: "VxeDatePicker", props: { type: "datetime" } },
          },
          {
            field: "outStorehouseName",
            title: "原料库",
            itemRender: { name: "VxeInput" },
          },
          {
            field: "outStrip",
            title: "料条",
            itemRender: { name: "VxeInput" },
          },
          { slots: { default: "action" }, align: "center" },
        ],
      },

      // 弹窗相关
      formVisible: false,
      formTitle: "新增数据",
      formMode: "add",
      formDialogOptions: {
        titleWidth: 120,
        titleAlign: "right",
        data: {
          transferSource: "",
          status: "0",
          mateCode: "",
          mateName: '',
          outStorehouseName: "",
          outTransferPlan: "",
          outStrip: "",
          outStack: "",
          planOutQuantity: 0,
          inStorehouseName: "",
          inTransferPlan: "",
          inStrip: "",
          inStack: "",
        },
        rules: {
          transferSource: [{ required: true, message: "调拨计划单必填" }],
          mateName: [{ required: true, message: "物料名称必填" }],
          planOutQuantity: [
            { required: true, message: "计划出库量必填" },
            { type: "number", min: 1, message: "数量必须大于0" },
          ],
          outStack: [{ type: "number", min: 1, message: "数字类型" }],
          inStack: [{ type: "number", min: 1, message: "数字类型" }]
        },
        items: [
          {
            field: "transferSource",
            title: "调拨计划单号",
            span: 12,
            itemRender: { name: "VxeInput" },
          },
          {
            field: "mateCode",
            title: "物料名称",
            span: 12,
            itemRender: materialEditRender,
          },
          {
            field: "outStorehouseName",
            title: "出库仓库",
            span: 12,
            itemRender: { name: "VxeInput" },
          },
          {
            field: "inStorehouseName",
            title: "入库仓库",
            span: 12,
            itemRender: { name: "VxeInput" },
          },
          {
            field: "planOutQuantity",
            title: "计划出库量",
            span: 12,
            itemRender: { name: "VxeInput", props: { type: "number", min: 0 } },
          },
          {
            field: "outTransferPlan",
            title: "出库堆料计划",
            span: 12,
            itemRender: { name: "VxeInput" },
          },
          {
            field: "inTransferPlan",
            title: "入库堆料计划",
            span: 12,
            itemRender: { name: "VxeInput" },
          },
          {
            field: "outStrip",
            title: "出库料条",
            span: 12,
            itemRender: { name: "VxeInput" },
          },
          {
            field: "inStrip",
            title: "入库料条",
            span: 12,
            itemRender: { name: "VxeInput" },
          },
          {
            field: "outStack",
            title: "出库垛位",
            span: 12,
            itemRender: { name: "VxeInput" },
          },
          {
            field: "inStack",
            title: "入库位置",
            span: 12,
            itemRender: { name: "VxeInput" },
          },
          {
            field: "status",
            title: "状态",
            span: 12,
            itemRender: {
              name: "VxeSelect",
              options: [
                { value: "0", label: "暂存" },
                { value: "1", label: "下发" },
                { value: "2", label: "完成" },
                { value: "3", label: "取消" },
              ],
            },
          },
          { slots: { default: "action" }, span: 24 },
        ],
      },

      // 选中数据
      selectedRows: [],
    };
  },

  mounted() {
    this.initGridData();
    this.initData()
  },

  methods: {
    // 初始化表格
    initGridData() {
      this.gridOptions.columns = [
        { type: "checkbox", width: 60 },
        { type: "seq", title: "序号", width: 60 },
        { field: "transferSource", title: "调拨计划单号" },
        {
          field: "status",
          title: "状态",
          formatter: ({ cellValue }) => {
            const statusMap = {
              0: "暂存",
              1: "下发",
              2: "完成",
              3: "取消",
            };
            return statusMap[cellValue] || cellValue;
          },
        },
        { field: "mateName", title: "物料" },
        { field: "outStorehouseName", title: "出库仓库" },
        { field: "outTransferPlan", title: "出库堆料计划" },
        { field: "outStrip", title: "出库料条" },
        { field: "outStack", title: "垛位" },
        { field: "planOutQuantity", title: "计划出库量" },
        { field: "inStorehouseName", title: "入库仓库" },
        { field: "inTransferPlan", title: "入库堆料计划" },
        { field: "inStrip", title: "入库料条" },
        { field: "inStack", title: "入库位置" },
      ];
      this.handlePageData();
    },

    // 显示表单弹窗
    showForm(mode) {
      if (mode === "edit" && this.selectedRows.length !== 1) {
        this.$message({
          message: `请选择一条数据`,
          type: "error",
        });
        return;
      }
      this.formMode = mode;
      this.formTitle = mode === "add" ? "新增数据" : "修改数据";
      this.formVisible = true;
      this.$nextTick(() => {
        if (mode === "edit") {
          this.formDialogOptions.data = {
            ...this.selectedRows[0],
          };
        } else {
          this.$refs.formDialogRef.reset();
          this.formDialogOptions.data.id = null;
        }
      });
    },
    //表单弹窗关闭
    handleModalClose() {
      this.formVisible = false;
      this.handlePageData();
    },

    // 提交表单
    async submitForm() {
      if (this.formMode === "add") {
        // 新增数据
        this.submitAddForm();
      } else {
        // 编辑数据
        this.submitEditForm();
      }
    },

    //新增数据
    submitAddForm() {
      this.$refs.formDialogRef.validate((valid) => {
        if (!valid) {
          insertTransferData(this.formDialogOptions.data)
            .then(() => {
              this.formVisible = false;
            })
            .then(() => {
              this.handlePageData();
              this.$modal.msgSuccess("新增成功");
            })
        } else {
          this.$modal.msgError("请填写必须字段");
        }
      })
    },

    //修改数据
    submitEditForm() {
      this.$refs.formDialogRef.validate((valid) => {
        if (!valid) {
          updateTransferData(this.formDialogOptions.data)
            .then(() => {
              this.formVisible = false;
            })
            .then(() => {
              this.handlePageData();
              this.$modal.msgSuccess("修改成功");
            })
        } else {
          this.$modal.msgError("请填写必须字段");
        }
      })
    },


    // 删除处理
    handleDelete() {
      // 1. 获取表格中选中的行数据
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords();
      // 2. 检查是否有选中数据
      if (selectedRows == null || selectedRows.length == 0) {
        this.$message({
          message: "请选择要删除的数据",
          type: "warning",
        });
        return;
      }

      const invalidRows = selectedRows.filter(row =>
        row.status === 1 || row.status === 2
      );
      if (invalidRows.length > 0) {
        const invalidIds = invalidRows.map(row => row.transferSource).join(", ");
        this.$message({
          message: `调拨单号为 ${invalidIds} 的数据状态为「下发/完成」，不可删除`,
          type: "warning",
        });
        return;
      }
      // 3. 弹出确认对话框
      this.$modal
        .confirm("是否确认删除？")
        .then(() => {
          const ids = selectedRows.map((row) => row.id);
          return deleteTransferData(ids);
        })
        .then(() => {
          this.handlePageData();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {
          this.$modal.msgError("删除失败");
        });
    },

    //记录选中完整行数据
    selectChangeEvent({ records }) {
      this.selectedRows = records;
    },

    searchEvent() {
      this.gridOptions.pagerConfig.currentPage = 1;
      this.handlePageData();
    },

    resetEvent() {
      this.$refs.formRef.reset();
      this.searchEvent();
    },

    handlePageData() {
      this.gridOptions.loading = true;
      selectTransferData(this.formOptions.data).then((response) => {
        let data = response.data;
        const { pageSize, currentPage } = this.gridOptions.pagerConfig;
        this.gridOptions.pagerConfig.total = data.length;
        this.gridOptions.data = data.slice(
          (currentPage - 1) * pageSize,
          currentPage * pageSize
        );
        this.gridOptions.loading = false;
      });
    },

    pageChangeEvent({ pageSize, currentPage }) {
      this.gridOptions.pagerConfig.currentPage = currentPage;
      this.gridOptions.pagerConfig.pageSize = pageSize;
      this.handlePageData();
    },

    handleExport() {
      const $table = this.$refs.tableMainRef
      if ($table) {
        $table.exportData({
          type: 'csv'
        })
      }
    },
    initData() {
      initMaterialList().then(response => {
        let data = response.data
        let list = []
        for (let i = 0; i < data.length; i++) {
          list.push({
            value: `${data[i].materialNumber}`,
            label: `${data[i].shortName ? data[i].shortName : data[i].materialName}`
          })
        }
        this.materialEditRender.options = list
      })
    },

    changeMate(value) {
      console.log(value.data);

    }
  },

}
</script>