<template>
  <div>
    <vxe-grid ref="tableMainRef" v-bind="gridOptions" style="margin: 10px" @page-change="pageChangeEvent"
      @checkbox-change="selectChangeEvent" @checkbox-all="selectChangeEvent">
      <template #form>
        <vxe-form ref="formRef" v-bind="formOptions">
          <template #action>
            <vxe-button status="primary" @click="searchEvent">搜索</vxe-button>
            <vxe-button @click="resetEvent">重置</vxe-button>
          </template>
        </vxe-form>
      </template>
      <template #toolbarButtons>
        <vxe-button status="primary" @click="showForm('add')">新增</vxe-button>
        <vxe-button status="primary" @click="showForm('edit')">编辑</vxe-button>
        <vxe-button status="primary" @click="handleDelete">删除</vxe-button>
        <vxe-button status="primary" @click="handleExport">导出</vxe-button>
      </template>
    </vxe-grid>
    <!-- 数据操作弹窗 -->
    <vxe-modal v-model="formVisible" :title="formTitle" width="800" destroy-on-close @close="handleModalClose">
      <vxe-form ref="formDialogRef" v-bind="formDialogOptions">
        <template #action>
          <div style="text-align: center; margin-top: 20px">
            <vxe-button status="primary" @click="submitForm">提交</vxe-button>
            <vxe-button @click="cancel">取消</vxe-button>
          </div>
        </template>
      </vxe-form>
    </vxe-modal>
  </div>
</template>

<script>
import {
  selectTransferData,
  insertTransferData,
  updateTransferData,
  deleteTransferData,
} from "@/api/wms/stocktransfer";
import { initMaterialList } from "@/api/md/material";

export default {
  name: "TransferData",
  data() {
    
    const stripEditRender = {
      name: 'VxeSelect',
      props: {
        clearable: true,
      },
      options: [
        { value: 'A', label: 'A' },
        { value: 'B', label: 'B' },
        { value: 'C', label: 'C' },
        { value: 'D', label: 'D' },
        { value: 'E', label: 'E' },
      ]
    }
    const materialEditRender = {
      name: 'VxeSelect',
      props: {
        filterable: true,
        clearable: true,
      },
      options: [],
      events: {
        change: this.changeMate
      }
    }
    
    // 新增三个下拉框配置
    const outStorehouseRender = {
      name: 'VxeSelect',
      props: {
        clearable: true,
        filterable: false,  // 取消搜索功能
      },
      options: [],
    }
    
    const outStripRender = {
      name: 'VxeSelect',
      props: {
        clearable: true,
        filterable: false,  // 取消搜索功能
      },
      options: [],
    }
    
    const outStackRender = {
      name: 'VxeSelect',
      props: {
        clearable: true,
        filterable: false,  // 取消搜索功能
      },
      options: [],
    }
    
    return {
      stripEditRender,
      materialEditRender,
      outStorehouseRender,
      outStripRender,
      outStackRender,

      // 表格配置
      gridOptions: {
        columns: [],
        data: [],
        height: 800,
        border: true,
        stripe: true,
        align: "center",
        columnConfig: { resizable: true },
        rowConfig: {
          isHover: true,
          isCurrent: true,
        },

        checkboxConfig: {
          checkField: "checked",
          highlight: true,
          range: true,
          trigger: "click",
        },

        pagerConfig: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        },

        toolbarConfig: {
          custom: true,
          zoom: true,
          slots: { buttons: "toolbarButtons" },
        },

        customConfig: { immediate: true },
        loading: false,
      },

      // 搜索表单
      formOptions: {
        data: {
          beginTime: "",
          endTime: "",
          crossRegion: "",
          mateCode: "",
        },
        items: [
          {
            field: "beginTime",
            title: "开始时间",
            itemRender: { name: "VxeDatePicker", props: { type: "datetime" } },
          },
          {
            field: "endTime",
            title: "结束时间",
            itemRender: { name: "VxeDatePicker", props: { type: "datetime" } },
          },
          {
            field: "outStorehouseName",
            title: "原料库",
            itemRender: { name: "VxeInput" },
          },
          {
            field: "outStrip",
            title: "料条",
            itemRender: { name: "VxeInput" },
          },
          { slots: { default: "action" }, align: "center" },
        ],
      },

      // 弹窗相关
      formVisible: false,
      formTitle: "新增数据",
      formMode: "add",
      formDialogOptions: {
        titleWidth: 120,
        titleAlign: "right",
        data: {
          transferSource: "",
          status: "0",
          mateCode: "",
          mateName: '',
          outStorehouseName: "",
          outTransferPlan: "",
          outStrip: "",
          outStack: "",
          planOutQuantity: 0,
          inStorehouseName: "",
          inTransferPlan: "",
          inStrip: "",
          inStack: "",
        },
        rules: {
          transferSource: [{ required: true, message: "调拨计划单必填" }],
          mateName: [{ required: true, message: "物料名称必填" }],
          planOutQuantity: [
            { required: true, message: "计划出库量必填" },
            { type: "number", min: 1, message: "数量必须大于0" },
          ]
        },
        items: [
          {
            field: "transferSource",
            title: "调拨计划单号",
            span: 12,
            itemRender: { name: "VxeInput" },
          },
          {
            field: "mateCode",
            title: "物料名称",
            span: 12,
            itemRender: materialEditRender,
          },
          {
            field: "outStorehouseName",
            title: "出库仓库",
            span: 12,
            itemRender: outStorehouseRender,  // 修改为下拉框
          },
          {
            field: "inStorehouseName",
            title: "入库仓库",
            span: 12,
            itemRender: { name: "VxeInput" },
          },
          {
            field: "planOutQuantity",
            title: "计划出库量",
            span: 12,
            itemRender: { name: "VxeInput", props: { type: "number", min: 0 } },
          },
          // {
          //   field: "outTransferPlan",
          //   title: "出库堆料计划",
          //   span: 12,
          //   itemRender: { name: "VxeInput" },
          // },
          // {
          //   field: "inTransferPlan",
          //   title: "入库堆料计划",
          //   span: 12,
          //   itemRender: { name: "VxeInput" },
          // },
          {
            field: "outStrip",
            title: "出库料条",
            span: 12,
            itemRender: outStripRender,  // 修改为下拉框
          },
          {
            field: "inStrip",
            title: "入库料条",
            span: 12,
            itemRender: { name: "VxeInput" },
          },
          {
            field: "outStack",
            title: "出库垛位",
            span: 12,
            itemRender: outStackRender,  // 修改为下拉框
          },
          {
            field: "inStack",
            title: "入库位置",
            span: 12,
            itemRender: { name: "VxeInput" },
          },
          {
            field: "status",
            title: "状态",
            span: 12,
            itemRender: {
              name: "VxeSelect",
              options: [
                { value: "0", label: "暂存" },
                { value: "1", label: "下发" },
                { value: "2", label: "完成" },
                { value: "3", label: "取消" },
              ],
            },
          },
          { slots: { default: "action" }, span: 24 },
        ],
      },

      // 选中数据
      selectedRows: [],
    };
  },

  mounted() {
    this.initGridData();
    this.initData()
  },

  methods: {
    // 初始化表格
    initGridData() {
      this.gridOptions.columns = [
        { type: "checkbox", width: 60 },
        { type: "seq", title: "序号", width: 60 },
        { field: "transferSource", title: "调拨计划单号" },
        {
          field: "status",
          title: "状态",
          formatter: ({ cellValue }) => {
            const statusMap = {
              0: "暂存",
              1: "下发",
              2: "完成",
              3: "取消",
            };
            return statusMap[cellValue] || cellValue;
          },
        },
        { field: "mateName", title: "物料名称" },
        { field: "outStorehouseName", title: "出库仓库" },
        { field: "outTransferPlan", title: "出库堆料计划" },
        { field: "outStrip", title: "出库料条" },
        { field: "outStack", title: "垛位" },
        { field: "planOutQuantity", title: "计划出库量" },
        { field: "inStorehouseName", title: "入库仓库" },
        { field: "inTransferPlan", title: "入库堆料计划" },
        { field: "inStrip", title: "入库料条" },
        { field: "inStack", title: "入库位置" },
      ];
      this.handlePageData();
    },

    // 显示表单弹窗
    showForm(mode) {
      if (mode === "edit" && this.selectedRows.length !== 1) {
        this.$message({
          message: `请选择一条数据`,
          type: "error",
        });
        return;
      }
      this.formMode = mode;
      this.formTitle = mode === "add" ? "新增数据" : "修改数据";
      this.formVisible = true;
      this.$nextTick(() => {
        if (mode === "edit") {
          this.formDialogOptions.data = {
            ...this.selectedRows[0],
          };
          this.clearSelectRow();
          // 编辑时，根据选中的物料加载对应的仓库、料条和垛位
          this.updateMaterialDependentOptions();
        } else {
          this.$refs.formDialogRef.reset();
          this.formDialogOptions.data.id = null;
          this.formDialogOptions.data.transferSource = this.generateTransferNumber();
          // 新增时清空下拉框选项
          this.clearMaterialDependentOptions();
        }
      });
    },
    
    // 清空物料相关的下拉框选项
    clearMaterialDependentOptions() {
      this.outStorehouseRender.options = [];
      this.outStripRender.options = [];
      this.outStackRender.options = [];
      // 清空表单数据
      this.formDialogOptions.data.outStorehouseName = "";
      this.formDialogOptions.data.outStrip = "";
      this.formDialogOptions.data.outStack = "";
    },
    
    // 根据选中的物料更新仓库、料条和垛位的下拉选项
    updateMaterialDependentOptions() {
      const mateCode = this.formDialogOptions.data.mateCode;
      if (!mateCode) return;
      
      // 从物料列表中筛选出当前选中物料的所有相关信息
      const materialInfoList = this.materialEditRender.options
        .filter(option => option.value === mateCode);
      
      if (materialInfoList.length > 0) {
        // 更新仓库下拉框选项（去重）
        const storehouses = [...new Set(materialInfoList.map(item => item.name))];
        this.outStorehouseRender.options = storehouses.map(name => ({
          value: name,
          label: name
        }));
        
        // 更新料条下拉框选项（去重）
        const strips = [...new Set(materialInfoList.map(item => item.stackingPosition))];
        this.outStripRender.options = strips.map(strip => ({
          value: strip,
          label: strip
        }));
        
        // 更新垛位下拉框选项（去重）
        const stacks = [...new Set(materialInfoList.map(item => item.crossRegion))];
        this.outStackRender.options = stacks.map(stack => ({
          value: stack,
          label: stack
        }));
        
        // 当只有一条数据时自动选中，多条数据时设为空
        this.formDialogOptions.data.outStorehouseName = storehouses.length === 1 ? storehouses[0] : "";
        this.formDialogOptions.data.outStrip = strips.length === 1 ? strips[0] : "";
        this.formDialogOptions.data.outStack = stacks.length === 1 ? stacks[0] : "";
      } else {
        // 没有匹配的物料信息时清空
        this.formDialogOptions.data.outStorehouseName = "";
        this.formDialogOptions.data.outStrip = "";
        this.formDialogOptions.data.outStack = "";
      }
    },

    //表单弹窗关闭
    handleModalClose() {
      this.formVisible = false;
      this.clearSelectRow();
      this.handlePageData();
    },

    // 提交表单
    async submitForm() {
      if (this.formMode === "add") {
        // 新增数据
        this.submitAddForm();
      } else {
        // 编辑数据
        this.submitEditForm();
      }
    },

    //关闭表单清空选中数据
    clearSelectRow(){
      this.selectedRows = [];
      this.$refs.tableMainRef.setCheckboxRow([]);
    },
    cancel(){
      this.formVisible = false;
      this.clearSelectRow();
      this.handlePageData();
    },

    generateTransferNumber(){
      const now = new Date();
      const year = now.getFullYear().toString(); // 取年份后两位
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = now.getDate().toString().padStart(2, '0');
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const seconds = now.getSeconds().toString().padStart(2, '0');
      return `DB${year}-${month}-${day}-${hours}${minutes}${seconds}`;
    },
    //新增数据
    submitAddForm() {
      this.$refs.formDialogRef.validate((valid) => {
        if (!valid) {
          this.formDialogOptions.data.transferSource=this.generateTransferNumber();
          insertTransferData(this.formDialogOptions.data)
            .then(() => {
              this.formVisible = false;
            })
            .then(() => {
              this.handlePageData();
              this.$modal.msgSuccess("新增成功");
            })
        } else {
          this.$modal.msgError("请填写必须字段");
        }
      })
    },

    //修改数据
    submitEditForm() {
      this.$refs.formDialogRef.validate((valid) => {
        if (!valid) {
          updateTransferData(this.formDialogOptions.data)
            .then(() => {
              this.formVisible = false;
              this.clearSelectRow();
            })
            .then(() => {
              this.handlePageData();
              this.$modal.msgSuccess("修改成功");
            })
        } else {
          this.$modal.msgError("请填写必须字段");
        }
      })
    },


    // 删除处理
    handleDelete() {
      // 1. 获取表格中选中的行数据
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords();
      // 2. 检查是否有选中数据
      if (selectedRows == null || selectedRows.length == 0) {
        this.$message({
          message: "请选择要删除的数据",
          type: "warning",
        });
        return;
      }

      const invalidRows = selectedRows.filter(row =>
        row.status === 1 || row.status === 2
      );
      if (invalidRows.length > 0) {
        const invalidIds = invalidRows.map(row => row.transferSource).join(", ");
        this.$message({
          message: `调拨单号为 ${invalidIds} 的数据状态为「下发/完成」，不可删除`,
          type: "warning",
        });
        return;
      }
      // 3. 弹出确认对话框
      this.$modal
        .confirm("是否确认删除？")
        .then(() => {
          const ids = selectedRows.map((row) => row.id);
          return deleteTransferData(ids);
        })
        .then(() => {
          this.handlePageData();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {
          this.$modal.msgError("删除失败");
        });
    },

    //记录选中完整行数据
    selectChangeEvent({ records }) {
      this.selectedRows = records;
    },

    searchEvent() {
      this.gridOptions.pagerConfig.currentPage = 1;
      this.handlePageData();
    },

    resetEvent() {
      this.$refs.formRef.reset();
      this.searchEvent();
    },

    handlePageData() {
      this.gridOptions.loading = true;
      selectTransferData(this.formOptions.data).then((response) => {
        let data = response.data;
        const { pageSize, currentPage } = this.gridOptions.pagerConfig;
        this.gridOptions.pagerConfig.total = data.length;
        this.gridOptions.data = data.slice(
          (currentPage - 1) * pageSize,
          currentPage * pageSize
        );
        this.gridOptions.loading = false;
      });
    },

    pageChangeEvent({ pageSize, currentPage }) {
      this.gridOptions.pagerConfig.currentPage = currentPage;
      this.gridOptions.pagerConfig.pageSize = pageSize;
      this.handlePageData();
    },

    handleExport() {
      const $table = this.$refs.tableMainRef
      if ($table) {
        $table.exportData({
          type: 'csv'
        })
      }
    },

    initData() {
      initMaterialList().then(response => {
        let data = response.data  
        console.log(data)
        let list = []
        for (let i = 0; i < data.length; i++) {
          list.push({
            value: `${data[i].materialNumber}`,
            label: `${data[i].materialName}`,
            name: `${data[i].storehouseName}`,
            crossRegion: `${data[i].crossRegion}`,
            stackingPosition: `${data[i].stackingPosition}`
          })
        }
        this.materialEditRender.options = list
      })
    },
    
    changeMate() {
      // 当物料选择变化时，更新仓库、料条和垛位的下拉选项
      this.updateMaterialDependentOptions();
      
      // 确保物料选项已加载后更新物料名称
      if (this.materialEditRender.options.length > 0) 
      {
        this.updateMaterialName();
      } else 
      {
        // 如果选项还未加载，监听选项变化
        this.$watch(
          () => this.materialEditRender.options.length,
          (newLength) => {
            if (newLength > 0) {
              this.updateMaterialName();
              this.updateMaterialDependentOptions();
            }
          }
        );
      }
    },
    
    updateMaterialName(){
      console.log('更新物料编码:', this.formDialogOptions.data.mateCode);
      const materialOption = this.materialEditRender.options.find(
      option => option.value === this.formDialogOptions.data.mateCode
      );
      if (materialOption) {
        this.formDialogOptions.data.mateName = materialOption.label;
      }
    }
  },

}
</script>