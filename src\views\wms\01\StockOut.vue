<template>
    <div>
        <div class="wrap">
            <div class="wrapGrid1">
                <vxe-grid ref="grid1Ref" v-bind="grid1Options">
                    <template #action="{ row }">
                        <vxe-button mode="text" status="primary" @click="saveRow(row)">保存</vxe-button>
                    </template>
                </vxe-grid>
            </div>

            <div class="wrapGrid2">
                <vxe-grid ref="grid2Ref" v-bind="grid2Options">
                </vxe-grid>
            </div>
        </div>
    </div>
</template>

<script>
import { getJsonArray } from "@/api/wms/stockinout";
import { initMaterialList } from "@/api/md/material";

export default {
    name: 'StockOut',
    data() {

        const materialEditRender = {
            name: 'VxeSelect',
            props: {
                filterable: true,
                // clearable: true,
            },
            options: []
        }


        // grid1数据
        const grid1Options = {
            border: true,
            stripe: true,
            align: 'center',
            // height: 800,

            columnConfig: {
                resizable: true
            },
            rowConfig: {
                isHover: true,
                isCurrent: true,
            },
            mouseConfig: {
                selected: true
            },
            editConfig: {
                trigger: 'dblclick',
                mode: 'cell',
            },
            columns: [
                { type: 'seq', width: 50 },
                { field: 'pdc', title: '皮带秤', },
                { field: 'start', title: '起点', editRender: { name: 'VxeInput' } },
                { field: 'end', title: '终点', editRender: { name: 'VxeInput' } },
                { field: 'mateName', title: '物料', editRender: materialEditRender },
                { field: 'action', title: '操作', slots: { default: 'action' } }
            ],
            data: [],
        }

        const grid2Options = {
            border: true,
            stripe: true,
            align: 'center',
            // height: 800,

            columnConfig: {
                resizable: true
            },
            rowConfig: {
                isHover: true,
                isCurrent: true,
            },
            mouseConfig: {
                selected: true
            },

            columns: [
                { field: 'v2', title: '累计值', },
                { field: 'v1', title: '瞬时值', },
            ],
            data: [],
        }

        return {
            grid1Options,
            grid2Options,
            materialEditRender,

            timer: null,
        }
    },

    methods: {
        initMaterialList() {
            initMaterialList().then(response => {
                let data = response.data
                let list = []
                for (let i = 0; i < data.length; i++) {
                    list.push({
                        value: `${data[i].materialNumber}`,
                        label: `${data[i].shortName ? data[i].shortName : data[i].materialName}`
                    })
                }
                this.materialEditRender.options = list
            })
        },

        initGridData() {
            this.initGrid1Data()
        },

        // 初始化grid1数据
        initGrid1Data() {
            this.grid1Options.data = [
                {
                    id: 10001, pdc: 'G8A', start: 'A-2', end: '圆盘', mateCode: '', mateName: '',
                },
                {
                    id: 10002, pdc: 'G8B', start: 'B-2', end: '圆盘', mateCode: '', mateName: '',
                },
                {
                    id: 10003, pdc: 'G4', start: 'A-5', end: '供烧结', mateCode: '', mateName: '',
                },
                {
                    id: 10004, pdc: 'S2', start: 'B-1', end: '供1#高炉烧结矿', mateCode: '', mateName: '烧结矿',
                },
                {
                    id: 10005, pdc: 'K1B', start: 'D-3', end: '供1#高炉块矿', mateCode: '', mateName: '块矿',
                },
                {
                    id: 10006, pdc: 'J7', start: 'C-2', end: '供1#2#高炉焦炭', mateCode: '', mateName: '焦炭',
                },
                {
                    id: 10007, pdc: 'J1B', start: 'C-4', end: '天焦供高炉焦炭', mateCode: '', mateName: '焦炭',
                },

                {
                    id: 10008, pdc: '拉式1', start: '', end: '供高炉煤', mateCode: '', mateName: '',
                },

                {
                    id: 10009, pdc: '拉式2', start: '', end: '供高炉煤', mateCode: '', mateName: '',
                },

                {
                    id: 10010, pdc: '拉式3', start: '', end: '供高炉煤', mateCode: '', mateName: '',
                },
            ]


        },


        startTimer() {
            this.timer = setInterval(() => {
                getJsonArray().then(response => {
                    let data = response.data
                    this.grid2Options.data = [
                        {
                            id: 10001, v1: data[10].val, v2: data[0].val,
                        },
                        {
                            id: 10002, v1: data[11].val, v2: data[1].val,
                        },
                        {
                            id: 10003, v1: data[12].val, v2: data[2].val
                        },
                        {
                            id: 10004, v1: data[13].val, v2: data[3].val
                        },
                        {
                            id: 10005, v1: data[14].val, v2: data[4].val
                        },
                        {
                            id: 10006, v1: data[15].val, v2: data[5].val
                        },
                        {
                            id: 10007, v1: data[16].val, v2: data[6].val
                        },

                        {
                            id: 10007, v1: data[17].val, v2: data[7].val
                        },
                        {
                            id: 10007, v1: data[18].val, v2: data[8].val
                        },
                        {
                            id: 10007, v1: data[19].val, v2: data[9].val
                        },
                    ]

                })
            }, 1000); // 1000毫秒执行一次
        },
        stopTimer() {
            clearInterval(this.timer);
        },

        saveRow(row) {
            console.log(row);
            let weights = this.grid2Options.data.find((item) => {
                return item.id === row.id
            })
            console.log(weights);

        },

    },

    mounted() {
        this.initMaterialList()
        this.initGridData()
        this.startTimer()
    },
    beforeDestroy() {
        this.stopTimer();
    }


}
</script>

<style scoped>
.wrap {
    display: flex;
    justify-content: stretch;
}

/* .wrap>div {
    width: 100%;
} */

.wrapGrid1 {
    width: 80%;
}

.wrapGrid2 {
    width: 20%;
}
</style>