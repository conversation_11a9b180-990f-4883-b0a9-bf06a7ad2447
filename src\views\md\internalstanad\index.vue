<template>
  <div class="app-container">
    <!-- 搜索框 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="加工中心" prop="prodCenterName">
        <el-input :disabled="true" v-model="queryParams.prodCenterName" placeholder="请输入加工中心名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="报表单元" prop="reportUnitName">
        <el-select v-model="queryParams.reportUnitName" placeholder="请选择包含内容" clearable>
          <el-option v-for="dict in editReportList" :key="dict.reportUnitName" :label="dict.reportUnitName"
            :value="dict.reportUnitName">
            <span>{{ dict.reportUnitCode }}</span>-<span>{{ dict.reportUnitName }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="物料名称 " prop="materialName">
        <el-input v-model="queryParams.materialName" placeholder="请输入物料名称 " clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="元素名称" prop="elementName">
        <el-input v-model="queryParams.elementName" placeholder="请输入元素名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <!-- 功能按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <div class="mes_new_table">
      <el-table size="small" v-loading="loading" :data="stanadList" @selection-change="handleSelectionChange">
        <af-table-column type="selection" width="55" align="left" header-align="center" />
        <af-table-column label="加工中心编码" align="center" header-align="center" min-width="130" prop="prodCenterCode" />
        <af-table-column label="加工中心名称" align="center" header-align="center" min-width="130" prop="prodCenterName" />
        <af-table-column label="报表单元编码" align="center" header-align="center" min-width="130" prop="reportUnitCode" />
        <af-table-column label="报表单元名称" align="center" header-align="center" min-width="130" prop="reportUnitName" />
        <af-table-column label="物料编码" align="center" header-align="center" min-width="130" prop="materialNumber" />
        <af-table-column label="物料名称 " align="center" header-align="center" min-width="130" prop="materialName" />
        <af-table-column label="预警元素" align="center" header-align="center" prop="elementName" />
        <af-table-column label="上限值" align="center" header-align="center" prop="upperLimitValue" />
        <af-table-column label="下限值" align="center" header-align="center" prop="lowerLimitValue" />
        <af-table-column label="状态" align="center" header-align="center" prop="status">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == '生效'" type="success">{{
              scope.row.status
              }}</el-tag>
            <el-tag v-if="scope.row.status == '失效'" type="danger">{{
              scope.row.status
              }}</el-tag>
          </template>
        </af-table-column>
        <af-table-column label="显示顺序" align="center" header-align="center" prop="orderNum" />
        <af-table-column label="创建者" align="center" header-align="center" prop="createBy" />
        <af-table-column label="创建时间" align="center" header-align="center" prop="createTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}</span>
          </template>
        </af-table-column>
        <af-table-column label="更新者" align="center" header-align="center" prop="updateBy" />
        <af-table-column label="更新时间" align="center" header-align="center" prop="updateTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateTime, "{y}-{m}-{d} {h}:{i}:{s}") }}</span>
          </template>
        </af-table-column>
        <af-table-column label="备注" align="center" header-align="center" prop="remark" />
        <af-table-column label="操作" align="center" header-align="center" fixed="right" width="120"
                         class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </af-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                  @pagination="getList" />
    </div>


    <!-- 添加或修改内控标准对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="加工中心" prop="prodCenterId">
              <treeselect :disabled="true" :clearable="false" v-model="form.prodCenterId" :options="editProductCenterList"
                :show-count="true" placeholder="请选择加工中心" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报表单元" prop="reportUnitCode">
              <el-select v-model="form.reportUnitCode" placeholder="请选择包含内容">
                <el-option v-for="dict in editReportList" :key="dict.reportUnitCode" :label="dict.reportUnitName"
                  :value="dict.reportUnitCode">
                  <span>{{ dict.reportUnitCode }}</span>-<span>{{ dict.reportUnitName }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="物料名称" prop="materialNumber">
              <el-select v-model="form.materialNumber" placeholder="请选择物料名称">
                <el-option v-for="dict in colGetMaterialNameList" :key="dict.materialNumber" :label="dict.materialName"
                  :value="dict.materialNumber">
                  <span>{{ dict.materialNumber }}</span>-<span>{{ dict.materialName }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预警元素" prop="elementName">
              <el-input v-model="form.elementName" placeholder="请输入预警元素" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="上限值" prop="upperLimitValue">
              <el-input v-model="form.upperLimitValue" placeholder="请输入上限值" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下限值" prop="lowerLimitValue">
              <el-input v-model="form.lowerLimitValue" placeholder="请输入下限值" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="显示顺序" prop="orderNum">
              <el-input v-model="form.orderNum" placeholder="请输入显示顺序" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option v-for="dict in dict.type.effective_or_not" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style>
/* 解决输入框(text)与文本域(textarea)字体不一致问题 */
.el-textarea__inner {
  font-family: Arial, Helvetica, sans-serif !important;
}
</style>
<script>
import { listStanad, getStanad, delStanad, saveOrUpdate } from "@/api/md/internalstanad";
import { getbyReportUnitID } from '@/api/md/reportunit'
import { treeselect, queryByCode } from "@/api/md/productcenter";
import { treeMaterial } from "@/api/md/material";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Stanad",
  components: { Treeselect, treeMaterial },
  dicts: ["effective_or_not"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 内控标准表格数据
      stanadList: [],
      // 弹出层标题
      title: "",
      //加工中心菜单
      editProductCenterList: [],
      colGetMaterialNameList: [],
      editReportList: [],
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        prodCenterName: null,
        reportUnitName: null,
        materialName: null,
        elementName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        prodCenterId: [
          { required: true, message: "加工中心不能为空", trigger: "blur" },
        ],
        reportUnitCode: [
          { required: true, message: "报表单元不能为空", trigger: "blur" },
        ],
        elementName: [
          { required: true, message: "元素名称不能为空", trigger: "blur" },
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "blur" },
        ],
      }
    };
  },
  created() {
    queryByCode(this.getProdCenterCode()).then((response) => {
      this.queryParams.prodCenterName = response.data.prodCenterName;

      getbyReportUnitID(response.data.prodCenterId).then((response) => {
        this.editReportList = response.data;
        this.getList();
      })
    });
  },
  methods: {
    /** 查询内控标准列表 */
    getList() {
      this.loading = true;
      listStanad(this.queryParams).then(response => {
        this.stanadList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getProdCenterCode() {
      return this.$route.query.prodCenterCode;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        stanadId: null,
        getEditInfo: null,
        prodCenterCode: null,
        prodCenterName: null,
        reportUnitCode: null,
        reportUnitName: null,
        materialNumber: null,
        materialName: null,
        elementName: null,
        upperLimitValue: null,
        lowerLimitValue: null,
        status: "生效",
        orderNum: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.stanadId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      treeselect().then((response) => {
        this.editProductCenterList = response.data;
        treeMaterial().then((Materialres) => {
          this.colGetMaterialNameList = Materialres.data;

          queryByCode(this.getProdCenterCode()).then((response2) => {
            this.form.prodCenterId = response2.data.prodCenterId;

            this.open = true;
            this.title = "添加预警配置";

          });

        });
      });

    },


    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const stanadId = row.stanadId || this.ids;
      treeselect().then((response) => {
        this.editProductCenterList = response.data;

        getStanad(stanadId).then(standres => {
          const obj = standres.data;

          getbyReportUnitID(obj.prodCenterId).then((response3) => {
            this.editReportList = response3.data;

            treeMaterial().then((Materialres) => {
              this.colGetMaterialNameList = Materialres.data;
              this.form = obj;
              this.open = true;
              this.title = "修改预警配置";
            });
          });
        });
      })

    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const obj = JSON.parse(JSON.stringify(this.form));
          saveOrUpdate(obj).then((response) => {
            this.$modal.msgSuccess("保存成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const stanadIds = row.stanadId || this.ids;
      this.$modal.confirm('是否确认删除预警配置号为"' + stanadIds + '"的数据项？').then(function () {
        return delStanad(stanadIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "/api/md/internalStanad/export",
        {
          ...this.queryParams,
        },
        `stanad_${new Date().getTime()}.xlsx`
      );
    }
  }
};
</script>
