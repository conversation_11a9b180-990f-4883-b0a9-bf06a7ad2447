<template>
	<div style="position: relative;height: 45px;">
		<v-form-render :form-json="formJson" :form-data="formData" :option-data="optionData" ref="vfr">
		</v-form-render>
		<el-button-group style="position:absolute;left: 90%;top:20%">
		<el-button type="primary" @click="search" v-show="true" size="small">查询</el-button>
		<el-button type="success" @click="handleExport" v-show="true" size="small">导出</el-button>
		</el-button-group>
	</div>
</template>
<script>
import { getformjson } from '@/api/formtemplate/searchbox';

export default {
	data() {
		return {
			formJson: {},
			formData: {},
			optionData: {},
			pageKey: '',
			defFormData: {},
		}
	},
	created() {
		this.pageKey = this.$route.query.pageKey;
		this.defFormData = this.$route.query;
		if (this.$route.query.pageKey == null) {
			// this.pageKey = 'CPES004013';
		}
		getformjson(this.pageKey).then((response) => {
			this.formjson = response;
			this.$refs.vfr.setFormJson(this.formjson);
			this.$nextTick(function () {
				// 设置搜索框默认值
				Object.keys(this.defFormData).forEach(k => {
					this.$refs.vfr.setFieldValue(k, this.defFormData[k]);
				});
			});
			return "ok";
		}).then(response=>{
			Object.keys(this.defFormData).forEach(k => {
					this.$refs.vfr.setFieldValue(k, this.defFormData[k]);
				});
			this.search();
		});
	},
	mounted() {

	},
	methods: {
		search() {
			this.$emit('searchBoxAction');
		},
		handleExport() {
			this.$parent.$parent.searchBoxAction();
			this.$parent.$parent.handleExport();
		}
	}
}
</script>
