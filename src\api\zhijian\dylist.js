import request from '@/utils/request'

//查询 铁水成分数据
export function ironListData(query,selectVO) {
  return request({
    url: '/api/element/ironList',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}
//查询 烧结矿成分数据
export function sinterListData(query,selectVO) {
  return request({
    url: '/api/element/sinterList',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

//查询 焦炭成分数据
export function jiaoTanListData(query,selectVO) {
  return request({
    url: '/api/element/jiaoTanList',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

//查询 焦炭成分数据
export function coalListData(query,selectVO) {
  return request({
    url: '/api/element/coalList',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

//查询 块矿成分数据
export function kuankuangListData(query,selectVO) {
  return request({
    url: '/api/element/kuanKuangList',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

//查询 球团矿成分数据
export function qiuTuanKuangListData(query,selectVO) {
  return request({
    url: '/api/element/qiuTuanKuangList',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

//查询 炉渣成分数据
export function slagListData(query,selectVO) {
  return request({
    url: '/api/element/slagList',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}
