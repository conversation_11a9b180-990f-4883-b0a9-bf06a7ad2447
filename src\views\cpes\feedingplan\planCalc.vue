<template>
  <div class="app-container" :style="scaleform()" v-loading="loading" element-loading-text="正在拉取化学成分..."
    @dblclick="handleConDbClick" v-if="loadcomplateflag">
    <!-- 录入表单项目 -->
    <el-form ref="inputform" :model="planObj" label-width="auto"
      style="box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); padding: 10px;">
      <!-- 第一行：主要字段 -->
      <el-row :gutter="20">
        <el-col :span="4">
          <el-form-item label="烧结" size="medium">
            <el-select v-model="planObj.prodCode" placeholder="选择烧结" clearable style="width: 100%;"
              :disabled="isEmptyStr(propProdCode) ? false : true" @change="prodChange">
              <el-option v-for="item in optionProdCode" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="料批" size="medium" required>
            <el-input v-model="planObj.batchWt" style="width: 100%;" placeholder="请输入料批"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="堆号" size="medium" required>
            <el-input v-model="planObj.planNo" style="width: 100%;" placeholder="请输入堆号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="开堆时间" size="medium" required>
            <el-input v-model="planObj.planExecDate1" style="width: 100%;" placeholder="请输入开堆时间"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="换堆时间" size="medium">
            <el-input v-model="planObj.planExecDate2" style="width: 100%;" placeholder="请输入换堆时间"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="2">
          <div style="display: flex; justify-content: flex-end;">
            <el-button-group size="medium">
              <el-button type="primary" :loading="loadingCalcBtn" @click="handlerCalc" size="mini">计算</el-button>
              <el-button type="success" :loading="loadingSaveBtn" @click="handlerSave" size="mini">保存</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>

      <!-- 备注字段 -->
      <el-row :gutter="20" style="margin-top: 8px;">
        <el-col :span="8">
          <el-form-item label="备注" size="medium">
            <el-input v-model="planObj.planRemark" style="width: 100%;" type="textarea" :rows="1"
              placeholder="请输入备注信息..." show-word-limit resize="none"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 物料列表 -->
    <el-row>
      <el-col :span="24">
        <div style="box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); padding: 0px; margin-top: 5px;">
          <!-- 一次配料 -->
          <span class="boldspan1">一次配料</span>
          <vxe-table v-if="showflag_1st" border show-overflow keep-source ref="tableRef1st"
            :row-config="{ height: 30 }"
            :header-config="{ height: 30, rowHeight: 30 }"
            :footer-config="{ height: 30, rowHeight: 30 }"
            :style="{
              '--vxe-ui-table-border-color': '#000000',
              '--vxe-ui-table-border-width': '1px',
              '--vxe-ui-table-header-background-color': 'rgb(140, 197, 255)',
              '--vxe-ui-table-footer-background-color': 'rgb(140, 197, 255)'
            }" :data="planObj.list1st" :edit-rules="validRules" :edit-config="editConfig" :show-footer="true"
            :footer-data="this.planObj.listSum1st" @edit-disabled="editDisabledEvent" class="compact-table">
            <vxe-column field="materialnumber" title="原料" :edit-render="{ name: 'VxeInput' }" width="220px">
              <template #edit="{ row }">
                <i class="el-icon-s-promotion flash" style="color: blue" @click="showelements(row)"></i>
                <el-select v-model="row.materialnumber" placeholder="请选择原料" filterable :filter-method="filterOption1st"
                  @change="v => handerMateChange(v, row, oreoptions)" @visible-change="handlerVisibileChangerOre"
                  style="width: 140px;height: 50%;" :popper-append-to-body="true" transfer="true">
                  <el-option v-for="item in oreoptions" :key="item.value" :label="item.showtxt" :value="item.value">
                    <span style="float: left; margin-right: 10px;">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.matenum
                      }}</span>
                    <span style="float: left; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.alias
                      }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.schemeinfo
                      }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.prodcode
                      }}</span>
                  </el-option>
                </el-select>
                <i class="el-icon-delete" style="color: red" @click="del1st(row.rowid)"></i>
              </template>
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: flex-start; width: 100%; text-align: left;">
                  <i class="el-icon-s-promotion flash" style="color: blue; margin-left: 8px; flex-shrink: 0;" @click.stop="showelements(row)"></i>
                  <span style="flex: 1; text-align: center; margin-right: 8px; cursor: pointer;" @click="editMaterial1st(row)">{{ row.materialname }}</span>
                  <i class="el-icon-delete" style="color: red; cursor: pointer; flex-shrink: 0; margin-right: 8px;" @click.stop="del1st(row.rowid)" v-if="row.materialname"></i>
                </div>
              </template>
              <template #header>
                <span>原料</span>
                <el-button style="width:30px; background-color: rgb(140, 197, 255);color:#000;text-align: center;"
                  icon="el-icon-plus" size="mini" @click="add1st" type="text"></el-button>
              </template>
            </vxe-column>
            <vxe-column field="elemTfe" title="TFe" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemFeo" title="FeO" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemSio2" title="SiO2" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemAl2o3" title="Al2O3" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemCao" title="CaO" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemMgo" title="MgO" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemTio2" title="TiO2" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemP" title="P" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemS" title="S" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemMn" title="Mn" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemZn" title="Zn" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemH2o" title="H2O" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="metalLoss" title="烧损" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="dryRate" title="干配比" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="dryWt" title="干料量" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="settingVal" title="设定值" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="actVal" title="切出量" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="wetRate" title="湿配比" :edit-render="{ name: 'VxeInput' }" width="80px" align="center"></vxe-column>
            <vxe-column field="stockPrice" title="库存价格" :edit-render="{ name: 'VxeInput' }" width="90px" align="center"></vxe-column>
            <vxe-column field="lastPrice" title="报盘价格" :edit-render="{ name: 'VxeInput' }" width="80px" align="center"></vxe-column>
            <vxe-column field="rateAlsi" title="Al/Si" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="stockTfePrice" title="吨数" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="lastTfePrice" title="报盘吨数" :edit-render="{ name: 'VxeInput' }" width="80px" align="center"></vxe-column>
            <vxe-column field="particleLessthan1" title="-1mm" :edit-render="{ name: 'VxeInput' }"
              width="80px" align="center"></vxe-column>
            <vxe-column field="particle13" title="1-3mm" :edit-render="{ name: 'VxeInput' }" width="80px" align="center"></vxe-column>
            <vxe-column field="particle35" title="3-5mm" :edit-render="{ name: 'VxeInput' }" width="80px" align="center"></vxe-column>
            <vxe-column field="particle58" title="5-8mm" :edit-render="{ name: 'VxeInput' }" width="80px" align="center"></vxe-column>
            <vxe-column field="particleMorethan8" title="+8mm" :edit-render="{ name: 'VxeInput' }"
              width="80px" align="center"></vxe-column>
          </vxe-table>

          <!-- 二次配料 -->
          <span v-if="showflag_2nd" class="boldspan1">二次配料</span>
          <vxe-table v-if="showflag_2nd" border show-overflow keep-source ref="tableRef2nd"
            :row-config="{ height: 30 }"
            :header-config="{ height: 30, rowHeight: 30 }"
            :footer-config="{ height: 30, rowHeight: 30 }"
            :style="{
              '--vxe-ui-table-border-color': '#000000',
              '--vxe-ui-table-border-width': '1px',
              '--vxe-ui-table-header-background-color': 'rgb(253, 226, 226)',
              '--vxe-ui-table-footer-background-color': 'rgb(253, 226, 226)'
            }" :data="planObj.list2nd" :edit-rules="validRules" :edit-config="editConfig" :show-footer="true"
            :footer-data="this.planObj.listSum2nd" @edit-disabled="editDisabledEvent" class="compact-table">
            <vxe-column field="materialnumber" title="原料" :edit-render="{ name: 'VxeInput' }" width="220px">
              <template #edit="{ row }">
                <i class="el-icon-s-promotion flash" style="color: blue" @click="showelements(row)"></i>
                <el-select v-model="row.materialnumber" placeholder="请选择原料" filterable :filter-method="filterOption2nd"
                  @change="v => handerMateChange(v, row, sovsndfueloption)" @visible-change="handlerVisibileChangerOre"
                  style="width: 140px;height: 50%;" v-if="row.edit" :popper-append-to-body="true" transfer="true">
                  <el-option v-for="item in sovsndfueloption" :key="item.value" :label="item.showtxt"
                    :value="item.value">
                    <span style="float: left; margin-right: 10px;">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.matenum
                    }}</span>
                    <span style="float: left; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.alias
                    }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.schemeinfo
                    }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.prodcode
                    }}</span>
                  </el-option>
                </el-select>
                <i class="el-icon-delete" style="color: red" @click="del2nd(row.rowid)"></i>
              </template>
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: flex-start; width: 100%; text-align: left;">
                  <i class="el-icon-s-promotion flash" style="color: blue; margin-left: 8px; flex-shrink: 0;" @click.stop="showelements(row)"></i>
                  <span style="flex: 1; text-align: center; margin-right: 8px; cursor: pointer;" @click="editMaterial2nd(row)">{{ row.materialname }}</span>
                  <i class="el-icon-delete" style="color: red; cursor: pointer; flex-shrink: 0; margin-right: 8px;" @click.stop="del2nd(row.rowid)" v-if="row.materialname"></i>
                </div>
              </template>
              <template #header>
                <span>原料</span>
                <el-button style="width:30px; background-color: rgb(253, 226, 226);color:#000;text-align: center;"
                  icon="el-icon-plus" size="mini" @click="add2nd" type="text"></el-button>
              </template>
            </vxe-column>
            <vxe-column field="elemTfe" title="TFe" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemFeo" title="FeO" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemSio2" title="SiO2" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemAl2o3" title="Al2O3" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemCao" title="CaO" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemMgo" title="MgO" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemTio2" title="TiO2" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemP" title="P" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemS" title="S" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemMn" title="Mn" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemZn" title="Zn" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="elemH2o" title="H2O" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="metalLoss" title="烧损" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="dryRate" title="干配比" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="dryWt" title="干料量" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="settingVal" title="设定值" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="actVal" title="切出量" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="wetRate" title="湿配比" :edit-render="{ name: 'VxeInput' }" width="80px" align="center"></vxe-column>
            <vxe-column field="stockPrice" title="库存价格" :edit-render="{ name: 'VxeInput' }" width="90px" align="center"></vxe-column>
            <vxe-column field="lastPrice" title="报盘价格" :edit-render="{ name: 'VxeInput' }" width="80px" align="center"></vxe-column>
            <vxe-column field="rateAlsi" title="Al/Si" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="stockTfePrice" title="吨数" :edit-render="{ name: 'VxeInput' }" width="70px" align="center"></vxe-column>
            <vxe-column field="lastTfePrice" title="报盘吨数" :edit-render="{ name: 'VxeInput' }" width="80px" align="center"></vxe-column>
            <vxe-column field="particleLessthan1" title="-1mm" :edit-render="{ name: 'VxeInput' }"
              width="80px" align="center"></vxe-column>
            <vxe-column field="particle13" title="1-3mm" :edit-render="{ name: 'VxeInput' }" width="80px" align="center"></vxe-column>
            <vxe-column field="particle35" title="3-5mm" :edit-render="{ name: 'VxeInput' }" width="80px" align="center"></vxe-column>
            <vxe-column field="particle58" title="5-8mm" :edit-render="{ name: 'VxeInput' }" width="80px" align="center"></vxe-column>
            <vxe-column field="particleMorethan8" title="+8mm" :edit-render="{ name: 'VxeInput' }"
              width="80px" align="center"></vxe-column>
          </vxe-table>
        </div>
      </el-col>
    </el-row>

    <!-- 化学成分选择 -->
    <el-dialog :title="dialogTitle" :visible.sync="openEle" v-if="openEle" width="80%" append-to-body>
      <eleminfo ref="eleminfo" :planEleCurrentObj="currOpRow" @reflashMate="handlerReflashMate"></eleminfo>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryFormManger,
  getMaterial,
  delMaterial,
  saveOrUpdate,
  getSaveEntity,
} from "@/api/md/material";
import { treeselectByGroup } from "@/api/md/materialCategory";
import { queryByUnitGropCode } from "@/api/md/materialUnit";
import Treeselect from "@riophae/vue-treeselect";
import { getToken } from "@/utils/auth";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
// import { getemptydatamodel, getCokeList, getSolventList, getMateElemJS, getMateElemLT, getCoalList, saveplan } from "@/api/feedingplan/feedingpage";
// import { getCokeNutList, getOreAndSolvent, getMateElemALL, } from "@/api/feedingplan/feedingpage";
import json from "highlight.js/lib/languages/json";
import { mount } from "sortablejs";
import { pinyin, match } from 'pinyin-pro';
import { v4 as uuidv4 } from 'uuid';
import { getepagedata, getOrelist, getSovAndFuellist, bindserchbox, getdensity, calc, save } from "@/api/cpes/feedingpage";
import eleminfo from '@/views/ingredient/col/laboratoryReport'
import { VxeUI } from 'vxe-table'

export default {
  name:"planCalc",
  components: { Treeselect, eleminfo },
  props: ['planid', 'srcplanid', 'propProdCode'],
  data() {
    const validRules = {
      wetRate: [
        { required: true, message: '必须填写' },
        { type: "number", message: '必须为数字' }
      ],
      stockPrice: [
        { required: true, message: '必须填写' },
        { type: "number", message: '必须为数字' }
      ]
    }
    const editConfig = {
      trigger: 'click',
      mode: 'row',
      showStatus: true,
      showIcon: false,
      beforeEditMethod({ row }) {
        if (row.edit) {
          return true
        }
        return false
      }
    }
    return {
      validRules,
      editConfig,
      // 列表标题行显示标记
      showHeader: true,
      // 页面缩放比例
      currentRatio: 1,
      // 遮罩层
      loading: true,
      // 计算按钮loading
      loadingCalcBtn: false,
      // 保存方案按钮loading
      loadingSaveBtn: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openEle: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialCategoryCode: null,
        materialNumber: null,
        materialNumberDatacollect: null,
        materialName: null,
      },
      // 物料化学成分检化验结果
      materiaEle: [],
      // 弹出dialog显示的物料信息
      dialogTitle: '',
      // 弹出dialog计划化学成分
      planEle: [
        {
          ELETYPE: '当前',
          TFE_VALUE: null,
          SIO2_VALUE: null,
          CAO_VALUE: null,
          MGO_VALUE: null,
          AL2O3_VALUE: null,
          MATERIAL_CODE: '',
          DENSITY: null,
        },
        {
          ELETYPE: '平均值',
          TFE_VALUE: null,
          SIO2_VALUE: null,
          CAO_VALUE: null,
          MGO_VALUE: null,
          AL2O3_VALUE: null,
          MATERIAL_CODE: '',
          DENSITY: null,
        },
      ],
      // 表单参数
      form: {},
      // 矿批需要合计的列prop
      summaryCols: ["allRate", "allWt", "vol", "tfeWt", "sio2Wt", "caoWt", "mgoWt", "al2o3Wt", "sWt", "pWt", "znWt", "tiWt", "mnWt", "kWt", "naWt", "madwt", "mtwt", "adwt", "fcadwt", "vdafwt", "qgrdwt", "price"],
      weightAvgCols: ["tfeRate", "sio2Rate", "caoRate", "mgoRate", "al2o3Rate", "sRate", "pRate", "znRate", "tiRate", "mnRate", "kRate", "naRate", "madrate", "mtrate", "adrate", "fcadrate", "vdafrate", "qgrdrate"],
      // 计划数据对象
      planObj: {},
      // 空行对象
      materialobj: {},
      // 矿批下拉
      oreoptions: [],
      oreoptionsAll: [],
      oreoptionsSrc: [],
      // 溶剂和燃料下拉
      sovsndfueloption: [],
      sovsndfueloptionAll: [],
      sovsndfueloptionSrc: [],
      // 焦下拉
      cokeoptions: [],
      cokeoptionsAll: [],
      cokeoptionsSrc: [],
      // 焦丁下拉
      cokenutoptions: [],
      cokenutoptionsAll: [],
      cokenutoptionsSrc: [],
      // 煤下拉
      coaloptions: [],
      coaloptionsAll: [],
      coaloptionsSrc: [],
      // 当前操作的物料编码
      currMateNum: '',
      // 当前操作的集合
      currOpList: [],
      // 当前操作的行
      currOpRow: null,
      // 计划ID
      // planid: '',
      // 重量显示标记_测试用
      wtShowFlag: false,
      // 加工中心下拉数据
      optionProdCode: [],
      // 渲染数据标记
      loadcomplateflag: false,
      // 矿料化学成分显示标记
      eleOreFlag: false,
      // 列表显示标记
      showflag_1st: true,
      showflag_1stsum: true,
      showflag_2nd: true,
      showflag_2ndsum: true,
      // 一次配料尾行
      //sum1st: {},
      //sum2nd: {},

    };
  },
  created() {
    this.loading = false;
    if (undefined != this.$route.query.planid) {
      this.planid = this.$route.query.planid;
    }
    // 测试用
    // this.planid = 13;
    bindserchbox(null, null).then(resp => {
      if (resp.code == 200) {
        this.optionProdCode = resp.data.prod;
        this.loadData();
      }
    }).catch(ex => { });
  },
  mounted() {

  },
  methods: {
    // 页面内容加载
    loadData() {
      console.log("this.propProdCode", this.propProdCode);
      getepagedata(this.planid, this.srcplanid, this.propProdCode).then((resp) => {
        // 等待绑定执行结束
        setTimeout(() => {
          if (resp.code === 200) {
            this.planObj = resp.data;
            this.materialobj = this.planObj.materialModel;
            this.planObj.planid = this.planid;

            // 过滤掉空行数据（保留原有的简单过滤逻辑）
            if (this.planObj.list1st && Array.isArray(this.planObj.list1st)) {
              this.planObj.list1st = this.planObj.list1st.filter(item => {
                return item && (item.materialnumber || item.materialname || item.rowid);
              });
            }

            if (this.planObj.list2nd && Array.isArray(this.planObj.list2nd)) {
              this.planObj.list2nd = this.planObj.list2nd.filter(item => {
                return item && (item.materialnumber || item.materialname || item.rowid);
              });
            }

            // this.sum1st = this.planObj.listSum1st;
            // this.sum2nd = this.planObj.listSum2nd;
            if (!this.isEmptyStr(this.propProdCode)) {
              this.planObj.prod_code = this.propProdCode;
            }
            this.loadcomplateflag = true;
          }
        }, 0);
      });
      getOrelist().then(resp => {
        if (resp.code === 200) {
          this.oreoptions = resp.data;
          this.oreoptionsAll = resp.data;
          this.oreoptionsSrc = resp.data;
        }
      });
      getSovAndFuellist().then(resp => {
        if (resp.code === 200) {
          this.sovsndfueloption = resp.data;
          this.sovsndfueloptionAll = resp.data;
          this.sovsndfueloptionSrc = resp.data;
        }
      });
    },
    // 物料下拉事件
    handerMateChange(v, data, curroptions) {
      // 获取当前选项对象
      let curroptionObj = curroptions.filter(item => {
        return item.value === v
      })[0];
      // 获取堆比重
      getdensity(curroptionObj.matenum, curroptionObj.schemeno, this.planObj.prod_code).then(resp => {
        // console.log("getdensity响应 ", resp.data);
        if (resp.code == 200) {
          // 堆比重
          data.density = resp.data.mateinfo.densityValue;
          data.density = resp.data.mateinfo["densityValue" + this.planObj.prod_code]
          // 物料名称
          if (this.isEmptyStr(resp.data.mateinfo.aliasName)) {
            data.materialname = resp.data.mateinfo.materialName;
          } else {
            data.materialname = resp.data.mateinfo.aliasName;
          }
          // 方案信息
          // console.log("curroptions.schemeinfo",curroptionObj.schemeinfo);
          data.schemeInfo = curroptionObj.schemeinfo;
          // console.log("data.schemeinfo",data.schemeInfo);
          // 单价
          data.stockPrice = resp.data.mateinfo.proposedPrice;
          // 设置展开
          // data.hasChildren = this.isEmptyStr(curroptionObj.schemeno);
          data.children = resp.data.schemeinfo;
          // console.log(data.materialName);
        }
      }).catch(err => {

      });
    },
    // 高炉切换
    prodChange(v) {
      // 250518注释,待业务核实
      // if (!this.isEmptyStr(v)) {
      //   this.oreoptions = this.oreoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));
      //   this.oreoptionsAll = this.oreoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));

      //   this.cokeoptions = this.cokeoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));
      //   this.cokeoptionsAll = this.cokeoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));

      //   this.cokenutoptions = this.cokenutoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));
      //   this.cokenutoptionsAll = this.cokenutoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));

      //   this.coaloptions = this.coaloptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));
      //   this.coaloptionsAll = this.coaloptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));
      // }
      // else {
      //   this.oreoptions = this.oreoptionsSrc
      //   this.oreoptionsAll = this.oreoptionsSrc

      //   this.cokeoptions = this.cokeoptionsSrc
      //   this.cokeoptionsAll = this.cokeoptionsSrc

      //   this.cokenutoptions = this.cokenutoptionsSrc
      //   this.cokenutoptionsAll = this.cokenutoptionsSrc

      //   this.coaloptions = this.coaloptionsSrc
      //   this.coaloptionsAll = this.coaloptionsSrc
      // }
    },
    // 调试用handler,大容器双击触发
    handleConDbClick() {
      //this.$message.success("双击");
      this.wtShowFlag = !this.wtShowFlag;
      // this.$message.success(this.wtShowFlag);
    },
    handlerCalc() {
      this.loadingCalcBtn = true;
      this.fullValidEvent().then(r => {
        if (!r) {
          this.loadingCalcBtn = false;
          return;
        }
        calc(this.planObj).then(resp => {
          console.log(resp);
          if (resp.code === 200) {
            this.planObj = resp.data;
            this.$message.success("计算完成");
          }
          this.loadingCalcBtn = false;
        }).catch(err => {
          this.loadingCalcBtn = false;
          // this.$message.error(err);
        });
      })
    },
    handlerSave() {
      // 校验必填项
      if (!this.validateRequiredFields()) {
        return;
      }

      this.loadingSaveBtn = true;
      this.fullValidEvent().then(r => {
        // 计算
        calc(this.planObj).then(resp => {
          if (resp.code === 200) {
            this.planObj = resp.data;
          }
        });
        console.log('计算后:', this.planObj);
        // 保存
        save(this.planObj).then(resp => {
          if (resp.code === 200) {
            this.$message.success("保存成功");
          }
          this.loadingSaveBtn = false;
        }).catch(err => {
          this.loadingSaveBtn = false;
        });
      })
    },
    // 校验必填字段
    validateRequiredFields() {
      const requiredFields = [
        { field: 'batchWt', name: '料批' },
        { field: 'planNo', name: '堆号' },
        { field: 'planExecDate1', name: '开堆时间' }
      ];

      for (let item of requiredFields) {
        const value = this.planObj[item.field];
        if (this.isEmptyStr(value)) {
          this.$message.error(`${item.name}为必填项，请填写后再保存`);
          return false;
        }
      }

      return true;
    },
    handleSelectionChange1() {

    },
    add1st() {
      let modelMate = JSON.parse(JSON.stringify(this.materialobj));
      modelMate.rowid = uuidv4();
      modelMate.edit = true; // 设置为可编辑状态
      this.planObj.list1st.push(modelMate);
      // this.planObj.listOre.planitemid += 1;
    },
    add2nd() {
      let modelMate = JSON.parse(JSON.stringify(this.materialobj));
      modelMate.rowid = uuidv4();
      modelMate.edit = true; // 设置为可编辑状态
      this.planObj.list2nd.push(modelMate);
      // this.planObj.listOre.planitemid += 1;
    },
    del1st(itemid) {
      this.planObj.list1st = this.planObj.list1st.filter(item => item.rowid !== itemid);
    },
    del2nd(itemid) {
      this.planObj.list2nd = this.planObj.list2nd.filter(item => item.rowid !== itemid);
    },
    // 编辑一次配料物料
    editMaterial1st(row) {
      row.edit = true;
      this.$nextTick(() => {
        // 触发VXE Table的编辑模式
        this.$refs.xTable1st.setActiveCell(row, 'materialnumber');
      });
    },
    // 编辑二次配料物料
    editMaterial2nd(row) {
      row.edit = true;
      this.$nextTick(() => {
        // 触发VXE Table的编辑模式
        this.$refs.xTable2nd.setActiveCell(row, 'materialnumber');
      });
    },
    /** 鼠标移入cell */
    handleCellEnter(row, column, cell, event) {
      // row.edit = true
    },
    /** 鼠标移出cell */
    handleCellLeave(row, column, cell, event) {
      // row.edit = false
      // row.edit = !row.edit;
    },
    handlerDBClick(row, column, cell, even) {
      // row.edit = true;
    },
    cellClick(row) {
      // row.edit = !row.edit;
    },
    showelements(rowinfo) {
      // 根据物料编码获取近期检验结果
      this.loading = true;
      this.currOpRow = rowinfo;
      this.eleOreFlag = true;
      this.openEle = true;
      this.loading = false;
    },
    handlerReflashMate(planEleCurrentObj) {
      this.openEle = false;
    },
    // 打开物料化学成分窗口
    showelementsNotOre(mtype, rowinfo) {
      // 根据物料编码获取近期检验结果
      this.loading = true;
      this.currOpRow = rowinfo;
      this.eleOreFlag = false;
      let tempMateNo = rowinfo.materialNumber.split(':')[0];
      // getMateElemALL(mtype, rowinfo.materialNumber).then((resp) => {
      getMateElemALL(mtype, tempMateNo).then((resp) => {
        try {
          if (resp.code == 200) {
            // console.log(JSON.stringify(resp));
            this.materiaEle = resp.data.samples;
            this.openEle = true;
            this.dialogTitle = '化验结果 ';
            this.dialogTitle += resp.data.MATERIAL_NAME;
            this.dialogTitle += ' 物料编码:' + rowinfo.materialNumber + ' ';
            // this.dialogTitle += ' 堆比重:' + resp.data.DENSITY_VALUE;
            let selectedDen = resp.data["DENSITY_VALUE_" + this.planObj.prod_code];
            selectedDen = this.isEmptyStr(selectedDen) ? "0.0" : selectedDen;
            this.dialogTitle += ' 堆比重:' + selectedDen;
            this.planEle[0].MATERIAL_CODE = rowinfo.materialNumber;
            this.planEle[1].MATERIAL_CODE = rowinfo.materialNumber;
            this.planEle[0].DENSITY = resp.data.DENSITY_VALUE;
            this.planEle[1].DENSITY = resp.data.DENSITY_VALUE;
            this.currMateNum = rowinfo.materialNumber;
          }
        } catch (error) {
          this.loading = false;
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    // 打开物料化学成分窗口
    showelementsOre(rowinfo) {
      // 根据物料编码获取近期检验结果
      this.loading = true;
      this.currOpRow = rowinfo;
      this.eleOreFlag = true;

      let tempMateNo = rowinfo.materialnumber;
      console.log(tempMateNo);
      this.openEle = true;

      this.$refs["eleminfo"].mateCode = tempMateNo;

      // getMateElemALL('ore', rowinfo.materialNumber).then((resp) => {
      // getMateElemALL('ore', tempMateNo).then((resp) => {
      //   try {
      //     if (resp.code == 200) {
      //       // console.log(JSON.stringify(resp));
      //       this.materiaEle = resp.data.samples;
      //       this.openEle = true;
      //       this.dialogTitle = '化验结果 ';
      //       this.dialogTitle += resp.data.MATERIAL_NAME;
      //       this.dialogTitle += ' [' + rowinfo.materialNumber + '] ';
      //       // this.dialogTitle += '堆比重:' + resp.data.DENSITY_VALUE;
      //       let selectedDen = resp.data["DENSITY_VALUE_" + this.planObj.prod_code];
      //       selectedDen = this.isEmptyStr(selectedDen) ? "0.0" : selectedDen;
      //       this.dialogTitle += ' 堆比重:' + selectedDen;
      //       this.planEle[0].MATERIAL_CODE = rowinfo.materialNumber;
      //       this.planEle[1].MATERIAL_CODE = rowinfo.materialNumber;
      //       this.planEle[0].DENSITY = resp.data.DENSITY_VALUE;
      //       this.planEle[1].DENSITY = resp.data.DENSITY_VALUE;
      //       this.currMateNum = rowinfo.materialNumber;
      //     }
      //   } catch (error) {
      //     this.loading = false;
      //   }
      //   this.loading = false;
      // }).catch(() => {
      //   this.loading = false;
      // });
      this.loading = false;
    },
    handleSelectionChange(selection) {
      // TFE
      const valuesTFE = selection.map(item => Number(item['TFE_VALUE']));
      let avgTFE = this.getAvg(valuesTFE);
      this.planEle[1].TFE_VALUE = avgTFE;
      // SIO2
      const valuesSIO2 = selection.map(item => Number(item['SIO2_VALUE']));
      let avgSIO2 = this.getAvg(valuesSIO2);
      this.planEle[1].SIO2_VALUE = avgSIO2;
      // CAO
      const valuesCAO = selection.map(item => Number(item['CAO_VALUE']));
      let avgCAO = this.getAvg(valuesCAO);
      this.planEle[1].CAO_VALUE = avgCAO;
      // MGO
      const valuesMGO = selection.map(item => Number(item['MGO_VALUE']));
      let avgMGO = this.getAvg(valuesMGO);
      this.planEle[1].MGO_VALUE = avgMGO;
      // AL2O3
      const valuesAL2O3 = selection.map(item => Number(item['AL2O3_VALUE']));
      let avgAL2O3 = this.getAvg(valuesAL2O3);
      this.planEle[1].AL2O3_VALUE = avgAL2O3;
      // S
      const valuesS = selection.map(item => Number(item['S_VALUE']));
      let avgS = this.getAvg(valuesS);
      this.planEle[1].S_VALUE = avgS;
      // P
      const valuesP = selection.map(item => Number(item['P_VALUE']));
      let avgP = this.getAvg(valuesP);
      this.planEle[1].P_VALUE = avgP;
      // Zn
      const valuesZn = selection.map(item => Number(item['ZN_VALUE']));
      let avgZn = this.getAvg(valuesZn);
      this.planEle[1].ZN_VALUE = avgZn;
      // Ti
      const valuesTi = selection.map(item => Number(item['TI_VALUE']));
      let avgTi = this.getAvg(valuesTi);
      this.planEle[1].TI_VALUE = avgTi;
      // Mn
      const valuesMn = selection.map(item => Number(item['MN_VALUE']));
      let avgMn = this.getAvg(valuesMn);
      this.planEle[1].MN_VALUE = avgMn;
      // K
      const valuesK = selection.map(item => Number(item['K_VALUE']));
      let avgK = this.getAvg(valuesK);
      this.planEle[1].K_VALUE = avgK;
      // Na
      const valuesNa = selection.map(item => Number(item['NA_VALUE']));
      let avgNa = this.getAvg(valuesNa);
      this.planEle[1].NA_VALUE = avgNa;
      // Mad
      const valuesMad = selection.map(item => Number(item['MAD_VALUE']));
      let avgMad = this.getAvg(valuesMad);
      this.planEle[1].MAD_VALUE = avgMad;
      // Mt
      const valuesMt = selection.map(item => Number(item['MT_VALUE']));
      let avgMt = this.getAvg(valuesMt);
      this.planEle[1].MT_VALUE = avgMt;
      // Ad
      const valuesAd = selection.map(item => Number(item['AD_VALUE']));
      let avgAd = this.getAvg(valuesAd);
      this.planEle[1].AD_VALUE = avgAd;
      // Vdaf
      const valuesVdaf = selection.map(item => Number(item['VDAF_VALUE']));
      let avgVdaf = this.getAvg(valuesVdaf);
      this.planEle[1].VDAF_VALUE = avgVdaf;
      // Fcad
      const valuesFcad = selection.map(item => Number(item['FCAD_VALUE']));
      let avgFcad = this.getAvg(valuesFcad);
      this.planEle[1].FCAD_VALUE = avgFcad;
      // Fcad
      const valuesQgad = selection.map(item => Number(item['QGRD_VALUE']));
      let avgQgad = this.getAvg(valuesQgad);
      this.planEle[1].QGRD_VALUE = avgQgad;
    },
    getAvg(values) {
      let sum = 0;
      values.forEach((v, index) => {
        if (!isNaN(v)) {
          sum += v;
        }
      });
      let avg = sum / (values.length);
      if (avg >= 0.001) {
        return avg.toFixed(3);
      }
      else {
        return avg;
      }
    },
    handleCurrentChange(val) {
      this.currentRow = val;
      console.log(this.currentRow);
    },
    selectedDialogELE() {
      // let currMateIndex = null;
      // this.planObj.listOre.forEach((ore, index) => {
      //   if (ore.materialNumber == this.currMateNum) {
      //     currMateIndex = index;
      //   }
      // });
      // console.log('当前index', currMateIndex);

      // 修改当前物料元素占比
      // this.planObj.listOre[currMateIndex].tfeRate = this.planEle[1].TFE_VALUE;
      // this.planObj.listOre[currMateIndex].sio2Rate = this.planEle[1].SIO2_VALUE;
      // this.planObj.listOre[currMateIndex].caoRate = this.planEle[1].CAO_VALUE;
      // this.planObj.listOre[currMateIndex].mgoRate = this.planEle[1].MGO_VALUE;
      // this.planObj.listOre[currMateIndex].al2o3Rate = this.planEle[1].AL2O3_VALUE;
      // this.planObj.listOre[currMateIndex].pRate = this.planEle[1].P_VALUE;
      // this.planObj.listOre[currMateIndex].sRate = this.planEle[1].S_VALUE;
      // this.planObj.listOre[currMateIndex].znRate = this.planEle[1].ZN_VALUE;
      // this.planObj.listOre[currMateIndex].tiRate = this.planEle[1].TI_VALUE;
      // this.planObj.listOre[currMateIndex].mnRate = this.planEle[1].MN_VALUE;
      // this.planObj.listOre[currMateIndex].kRate = this.planEle[1].K_VALUE;
      // this.planObj.listOre[currMateIndex].naRate = this.planEle[1].NA_VALUE;
      // this.planObj.listOre[currMateIndex].density = this.planEle[1].DENSITY;

      this.currOpRow.tfeRate = this.planEle[1].TFE_VALUE;
      this.currOpRow.sio2Rate = this.planEle[1].SIO2_VALUE;
      this.currOpRow.caoRate = this.planEle[1].CAO_VALUE;
      this.currOpRow.mgoRate = this.planEle[1].MGO_VALUE;
      this.currOpRow.al2o3Rate = this.planEle[1].AL2O3_VALUE;
      this.currOpRow.pRate = this.planEle[1].P_VALUE;
      this.currOpRow.sRate = this.planEle[1].S_VALUE;
      this.currOpRow.znRate = this.planEle[1].ZN_VALUE;
      this.currOpRow.tiRate = this.planEle[1].TI_VALUE;
      this.currOpRow.mnRate = this.planEle[1].MN_VALUE;
      this.currOpRow.kRate = this.planEle[1].K_VALUE;
      this.currOpRow.naRate = this.planEle[1].NA_VALUE;
      this.currOpRow.madrate = this.planEle[1].MAD_VALUE;
      this.currOpRow.mtrate = this.planEle[1].MT_VALUE;
      this.currOpRow.adrate = this.planEle[1].AD_VALUE;
      this.currOpRow.vdafrate = this.planEle[1].VDAF_VALUE;
      this.currOpRow.fcadrate = this.planEle[1].FCAD_VALUE;
      this.currOpRow.qgrdrate = this.planEle[1].QGRD_VALUE;

      this.currOpRow.density = this.planEle[1].DENSITY;
      this.openEle = false;
    },
    getSummariesOre(param) {
      return this.getSummaries(param, '小记');
    },
    getSummariesCoke(param) {
      return this.getSummaries(param, '焦-小计');
    },
    getSummariesCokeNut(param) {
      return this.getSummaries(param, '焦丁-小计');
    },
    getSummariesCoal(param) {
      return this.getSummaries(param, '煤-小计');
    },
    getSummaries(param, suntxt) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = suntxt + '';
          return;
        }
        // console.log(column.property);
        if (this.summaryCols.indexOf(column.property) >= 0) {
          const values = data.map(item => Number(item[column.property]));
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] = sums[index].toFixed(3);
        } else if (this.weightAvgCols.indexOf(column.property) >= 0) {
          const values = data.map(item => Number(item[column.property]));
          let sumValue = 0.0;
          let sumWeight = 0.0;
          data.forEach((v, i) => {
            if (!isNaN(v[column.property])) {
              sumValue += v[column.property] * v["allRate"];
              sumWeight += v["allRate"];
            }
          });
          if (sumWeight > 0) {
            sums[index] = sumValue / sumWeight;
            sums[index] = sums[index].toFixed(3);
          } else {
            sums[index] = 0;
          }
        }
      });
      return sums;
    },

    // 一次配料筛选
    filterOption1st(val) {
      if (this.isEmptyStr(val)) {
        this.oreoptions = this.oreoptionsAll;
      } else {
        this.oreoptions = this.oreoptionsAll.filter((item) => {
          return this.checkOption(item, val);
        });
      }
    },
    // 二次配料筛选
    filterOption2nd(val) {
      if (this.isEmptyStr(val)) {
        this.sovsndfueloption = this.sovsndfueloptionAll;
      } else {
        this.sovsndfueloption = this.sovsndfueloptionAll.filter((item) => {
          return this.checkOption(item, val);
        });
      }
    },

    // 下拉框选项显示
    handlerVisibileChangerOre(e) {
      if (e) {
        this.oreoptions = this.oreoptionsAll;
      }
    },
    // 焦
    filterOptionCoke(val) {
      if (this.isEmptyStr(val)) {
        this.cokeoptions = this.cokeoptionsAll;
      } else {
        this.cokeoptions = this.cokeoptionsAll.filter((item) => {
          return this.checkOption(item, val);
        });
      }
    },
    // 下拉框选项显示
    handlerVisibileChangerCoke(e) {
      if (e) {
        this.cokeoptions = this.cokeoptionsAll;
      }
    },
    // 焦丁
    filterOptionCokenut(val) {
      if (this.isEmptyStr(val)) {
        this.cokenutoptions = this.cokenutoptionsAll;
      } else {
        this.cokenutoptions = this.cokenutoptionsAll.filter((item) => {
          return this.checkOption(item, val);
        });
      }
    },
    // 下拉框选项显示
    handlerVisibileChangerCokenut(e) {
      if (e) {
        this.cokenutoptions = this.cokenutoptionsAll;
      }
    },
    // 煤
    filterOptionCoal(val) {
      if (this.isEmptyStr(val)) {
        this.coaloptions = this.coaloptionsAll;
      } else {
        this.coaloptions = this.coaloptionsAll.filter((item) => {
          return this.checkOption(item, val);
        });
      }
    },
    // 下拉框选项显示
    handlerVisibileChangerCoal(e) {
      if (e) {
        this.coaloptions = this.coaloptionsAll;
      }
    },
    isEmptyStr(s) {
      if (s == undefined || s == null || s == '') {
        return true
      }
      return false
    },
    // 校验下拉框选项是否符合录入值
    checkOption(item, val) {
      // console.log(`${item.label} ${item.value} ${item.alias}`);
      return !!~item.label.indexOf(val)
        ||
        !!~item.value.indexOf(val)
        ||
        !!~item.alias.indexOf(val)
        ||
        this.pinyinMatched(item.label, val)
        ||
        this.pinyinMatched(item.alias, val)
    },
    pinyinMatched(src, val) {
      const mRet = match(src, val, { continuous: true });
      if (mRet == null) {
        return false;
      }
      return true;
    },
    tableRowClassName({ row, rowIndex }) {
      // console.log('orebg');
      return 'orebg';
    },
    formatBoolean: function (row, column, cellValue) {
      var ret = ''  //你想在页面展示的值
      // console.log(cellValue);
      if (cellValue) {
        ret = "是"  //根据自己的需求设定
      } else {
        ret = "否"
      }
      return ret;
    },
    scaleform() {
      return {
        transform: `scale(${this.currentRatio},${this.currentRatio})`,
        padding: `5px`
      }
    },
    editDisabledEvent({ row, column }) {
      VxeUI.modal.message({
        content: `物料[${row.materialname}]，不可编辑。`,
        status: 'warning'
      })
    },
    async fullValidEvent() {
      const $table1st = this.$refs.tableRef1st
      if ($table1st) {
        const errMap = await $table1st.validate(true)
        if (errMap) {
          VxeUI.modal.message({ status: 'error', content: '校验不通过！' })
          return false;
        } else {
          //VxeUI.modal.message({ status: 'success', content: '校验成功！' })
        }
        return true;
      }

      const $table2nd = this.$refs.tableRef2nd
      if ($table2nd) {
        const errMap = await $table2nd.validate(true)
        if (errMap) {
          VxeUI.modal.message({ status: 'error', content: '校验不通过！' })
        } else {
          //VxeUI.modal.message({ status: 'success', content: '校验成功！' })
        }
      }
    }
  }
};
</script>

<style scoped>
.dataForm {
  margin: 0px;
  border-left: 1px solid #000000;
  border-bottom: 1px solid #000000;
}

.formItemDuty {
  border-top: 1px solid #000000;
  border-right: 1px solid #000000;
  margin: 0px;
}

.formItemDutyIn {
  border-left: 1px solid #000000;
  border-right: 1px solid #000000;
}

/* 单元格回行设置 */
::deep .el-table .cell {
  white-space: nowrap;
  padding-left: 5px;
  padding-right: 5px;
  overflow: visible;
}

::deep .el-select-dropdown__wrap.el-scrollbar__wrap {
  margin-bottom: 0 !important;
}

/*闪烁动画*/
@keyframes twinkle {
  from {
    opacity: 1.0;
  }

  50% {
    opacity: 0.4;
  }

  to {
    opacity: 1.0;
  }
}

.flash {
  animation: twinkle 1s;
  animation-iteration-count: infinite;
}

.itemfeedinput {

  /* width: 100px; */
  /* 调整elementUI中样式 如果不需要调整请忽略 */
  .el-input__inner {
    padding-left: 2px;
    padding-right: 2px;
    height: 24px !important;
    border: none !important;
  }
}

.feedplaninput {

  /* width: 100px; */
  /* 调整elementUI中样式 如果不需要调整请忽略 */
  .el-input__inner {
    padding-left: 2px;
    padding-right: 2px;
    height: 24px !important;
    /* border: none!important; */
  }
}

.feedplaninput_long {

  /* width: 100px; */
  /* 调整elementUI中样式 如果不需要调整请忽略 */
  .el-input__inner {
    width: 80px !important;
    ;
    padding-left: 2px;
    padding-right: 2px;
    height: 24px !important;
    /* border: none!important; */
  }
}

.feedplaninput_mid {

  /* width: 100px; */
  /* 调整elementUI中样式 如果不需要调整请忽略 */
  .el-input__inner {
    width: 70px !important;
    ;
    padding-left: 2px;
    padding-right: 2px;
    height: 24px !important;
    /* border: none!important; */
  }
}

.el-form>>>.el-form-item__label {
  text-align: justify;
  text-align-last: justify;
}

.el-form-item {
  margin-bottom: 1px;
  height: 30px;
}

.el-form-item__label-wrap {
  margin-left: 0px !important;
}

.el-table>>>.el-select {
  width: 184px;
  height: 23px;

  .el-input__inner {
    height: 23px;
    border: none !important;
  }

  .el-input__prefix,
  .el-input__suffix {
    height: 23px;
  }

  /* 下面设置右侧按钮居中 */
  .el-input__suffix {
    top: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
    flex-direction: row;
    align-content: flex-start;
  }

  /* 输入框加上上下边是 32px + 2px =34px */
  .el-input__icon {
    line-height: 25px;
  }
}

.el-table /deep/ .el-table__footer-wrapper tbody td {
  height: 25px;
  padding-top: 2px;
  padding-bottom: 2px;
  font-weight: bolder;
  border-top: 1px solid #000000;
  border-bottom: 1px solid #000000;
  border-right: 1px solid #000000;

}

.el-table /deep/ .el-table__fixed-footer-wrapper tbody td {
  height: 25px;
  padding-top: 2px;
  padding-bottom: 2px;
  border-top: 1px solid #000000;
  border-bottom: 1px solid #000000;
  border-right: 1px solid #000000;
}

::v-deep .el-table .el-table__header th,
.el-table .el-table__header tr,
.el-table .el-table__header td {
  /* 表头颜色 */
  background: #c7d1c6 !important;
  padding: 0px;
  height: 25px;
  border-bottom: 1px solid #000000;
  border-right: 1px solid #000000;
}

.el-table {
  /* border: 1px solid #000000; */
  border-left: 1px solid #000000;
  border-top: 1px solid #000000;
}

.boldspan1 {
  font-weight: 700;
  color: #000000;
  font-size: 20px;
}

/* VXE表格紧凑样式 */
.compact-table {
  font-size: 12px;
}

/* VXE表格单元格居中对齐 */
.compact-table /deep/ .vxe-body--column {
  text-align: center !important;
  vertical-align: middle !important;
  padding: 2px 3px !important;
}

.compact-table /deep/ .vxe-cell {
  padding: 2px 3px !important;
  line-height: 26px !important;
  text-align: center !important;
}

/* VXE表格表头居中对齐 */
.compact-table /deep/ .vxe-header--column {
  text-align: center !important;
  vertical-align: middle !important;
  padding: 2px 3px !important;
}

.compact-table /deep/ .vxe-header--column .vxe-cell {
  text-align: center !important;
  font-weight: bold !important;
  line-height: 26px !important;
  padding: 2px 3px !important;
}

/* VXE表格表尾居中对齐 */
.compact-table /deep/ .vxe-footer--column {
  text-align: center !important;
  vertical-align: middle !important;
  padding: 2px 3px !important;
}

.compact-table /deep/ .vxe-footer--column .vxe-cell {
  text-align: center !important;
  font-weight: bold !important;
  line-height: 26px !important;
  padding: 2px 3px !important;
}

/* VXE表格输入框样式 */
.compact-table /deep/ .vxe-input {
  text-align: center !important;
  font-size: 12px !important;
  height: 24px !important;
  line-height: 24px !important;
}

.compact-table /deep/ .vxe-input .vxe-input--inner {
  text-align: center !important;
  font-size: 12px !important;
  height: 24px !important;
  line-height: 24px !important;
  padding: 0 3px !important;
}

/* VXE表格下拉选择框样式 */
.compact-table /deep/ .el-select {
  width: 100% !important;
}

.compact-table /deep/ .el-select .el-input__inner {
  text-align: center !important;
  font-size: 12px !important;
  height: 24px !important;
  line-height: 24px !important;
  padding: 0 3px !important;
}

/* 原料列特殊处理，左对齐 - 使用更强的选择器 */
.compact-table /deep/ .vxe-body--column[data-colid="materialnumber"] .vxe-cell,
.compact-table /deep/ td[data-colid="materialnumber"] .vxe-cell,
.compact-table /deep/ .vxe-table .vxe-body--column[data-colid="materialnumber"] .vxe-cell {
  text-align: left !important;
  padding-left: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  flex-direction: row !important;
}

.compact-table /deep/ .vxe-header--column[data-colid="materialnumber"] .vxe-cell {
  text-align: center !important;
}

/* 原料列中的图标和文字布局 - 使用更强的选择器 */
.compact-table /deep/ .vxe-body--column[data-colid="materialnumber"] .vxe-cell .el-icon-s-promotion,
.compact-table /deep/ td[data-colid="materialnumber"] .vxe-cell .el-icon-s-promotion {
  margin-right: 8px !important;
  flex-shrink: 0 !important;
  order: 1 !important;
}

.compact-table /deep/ .vxe-body--column[data-colid="materialnumber"] .vxe-cell span,
.compact-table /deep/ td[data-colid="materialnumber"] .vxe-cell span {
  flex: 1 !important;
  margin-left: 0 !important;
  margin-right: 8px !important;
  text-align: left !important;
  order: 2 !important;
}

.compact-table /deep/ .vxe-body--column[data-colid="materialnumber"] .vxe-cell .el-icon-delete,
.compact-table /deep/ td[data-colid="materialnumber"] .vxe-cell .el-icon-delete {
  margin-left: auto !important;
  flex-shrink: 0 !important;
  order: 3 !important;
}

/* 调整表格行高 - 使用更具体的选择器 */
.compact-table /deep/ .vxe-body--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-header--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-footer--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

/* 针对第一行和最后一行的特殊处理 */
.compact-table /deep/ .vxe-body--row:first-child {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-body--row:last-child {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

/* 针对所有行的通用设置 */
.compact-table /deep/ .vxe-table--body .vxe-body--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

/* 针对表格单元格的高度控制 */
.compact-table /deep/ .vxe-body--column {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-header--column {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-footer--column {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

/* 确保表格整体紧凑 */
.compact-table /deep/ .vxe-table {
  line-height: 1.2 !important;
}

.compact-table /deep/ .vxe-table--body-wrapper {
  font-size: 12px !important;
}

.compact-table /deep/ .vxe-table--header-wrapper {
  font-size: 12px !important;
}

.compact-table /deep/ .vxe-table--footer-wrapper {
  font-size: 12px !important;
}

/* 强制覆盖VXE Table的默认样式 */
.compact-table /deep/ .vxe-table--render-default .vxe-body--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table--render-default .vxe-header--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table--render-default .vxe-footer--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

/* 针对可能的编辑状态行 */
.compact-table /deep/ .vxe-body--row.row--edit {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

/* 针对可能的选中状态行 */
.compact-table /deep/ .vxe-body--row.row--selected {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

/* 针对可能的悬停状态行 */
.compact-table /deep/ .vxe-body--row.row--hover {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

/* 针对表格的tr元素 */
.compact-table /deep/ tr {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

/* 针对表格的td元素 */
.compact-table /deep/ td {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  padding: 2px 3px !important;
}

/* 针对表格的th元素 */
.compact-table /deep/ th {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  padding: 2px 3px !important;
}

/* 特别针对footer汇总行的样式 */
.compact-table /deep/ .vxe-table--footer-wrapper {
  height: auto !important;
}

.compact-table /deep/ .vxe-table--footer-wrapper .vxe-footer--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table--footer-wrapper .vxe-footer--column {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table--footer-wrapper .vxe-footer--column .vxe-cell {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  line-height: 26px !important;
  padding: 2px 3px !important;
}

/* 针对footer中的tr和td元素 */
.compact-table /deep/ .vxe-table--footer-wrapper tr {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table--footer-wrapper td {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  padding: 2px 3px !important;
}

/* 强制覆盖VXE Table footer的默认样式 */
.compact-table /deep/ .vxe-table--render-default .vxe-table--footer-wrapper .vxe-footer--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

/* 针对可能的footer特殊状态 */
.compact-table /deep/ .vxe-footer--row.row--hover {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

/* 特别针对header表头行的样式 */
.compact-table /deep/ .vxe-table--header-wrapper {
  height: auto !important;
}

.compact-table /deep/ .vxe-table--header-wrapper .vxe-header--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table--header-wrapper .vxe-header--column {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table--header-wrapper .vxe-header--column .vxe-cell {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  line-height: 26px !important;
  padding: 2px 3px !important;
}

/* 针对header中的tr和th元素 */
.compact-table /deep/ .vxe-table--header-wrapper tr {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

.compact-table /deep/ .vxe-table--header-wrapper th {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
  padding: 2px 3px !important;
}

/* 强制覆盖VXE Table header的默认样式 */
.compact-table /deep/ .vxe-table--render-default .vxe-table--header-wrapper .vxe-header--row {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

/* 针对可能的header特殊状态 */
.compact-table /deep/ .vxe-header--row.row--hover {
  height: 30px !important;
  min-height: 30px !important;
  max-height: 30px !important;
}

/* 针对表头中可能包含的按钮元素 */
.compact-table /deep/ .vxe-table--header-wrapper .el-button {
  height: 24px !important;
  min-height: 24px !important;
  max-height: 24px !important;
  padding: 2px 4px !important;
  line-height: 20px !important;
}

/* 取消VXE Table表格体的最小高度，避免空行显示 */
.compact-table /deep/ .vxe-table--body-inner-wrapper {
  min-height: auto !important;

}





/* .el-table {
  border-bottom: 1px solid #000000 !important;
} */
</style>