<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="检斤单号" prop="weightNumber">
        <el-input
          v-model="queryParams.weightNumber"
          placeholder="请输入检斤单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车号" prop="truckNumber">
        <el-input
          v-model="queryParams.truckNumber"
          placeholder="请输入车号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入物料名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="二检时间" prop="grossTime">
        <el-date-picker clearable
                        v-model="queryParams.grossTime"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="数据标识" prop="receptionDataFlag">
        <el-select v-model="queryParams.receptionDataFlag" clearable placeholder="请选择">
          <el-option
            v-for="item in receptionDataFlagOption"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增-->
<!--        </el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate">修改-->
<!--        </el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">删除-->
<!--        </el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出-->
<!--        </el-button>-->
<!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>


    <div class="mes_new_table">
      <el-table size="mini" :height="dltableHeight"
                virtual-scroll v-loading="loading" :data="tableData" border @selection-change="handleSelectionChange"
                align="center">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column prop="weightNumber" label="检斤单号" fixed="left" width="230" align="center"></el-table-column>
        <el-table-column prop="materialNumber" label="物料编码" width="120" align="center"></el-table-column>
        <el-table-column prop="materialName" label="物料名称" width="200" align="center"></el-table-column>
        <el-table-column prop="netWeight" label="净重" width="120" align="center"></el-table-column>
        <el-table-column prop="transportType" label="运输方式" width="120" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.transportType == 1">汽运</span>
            <span v-if="scope.row.transportType == 2">火运</span>
            <span v-if="scope.row.transportType == 3">船运</span>
            <span v-if="scope.row.transportType == 4">空运</span>
            <span v-if="scope.row.transportType == 5">其他</span>
            <span v-if="scope.row.transportType == 6">天钢站集装箱</span>
          </template>
        </el-table-column>
        <el-table-column prop="truckNumber" label="车号" width="120" align="center"></el-table-column>
        <el-table-column prop="grossTime" label="二检时间" width="180" align="center"></el-table-column>
        <el-table-column prop="receiveTime" label="收货时间" width="200" align="center"></el-table-column>
        <el-table-column prop="receiveAcrossStack" label="收货跨垛" width="200" align="center"></el-table-column>
        <el-table-column prop="sendAcrossStack" label="发货跨垛" width="200" align="center"></el-table-column>
        <el-table-column prop="receptionDataFlag" label="数据标识" width="120" align="center"></el-table-column>
        <el-table-column prop="updateTime" label="修改时间" width="180" align="center"></el-table-column>
        <el-table-column prop="operatorBy" label="操作人" width="120" align="center"></el-table-column>
        <el-table-column prop="failReason" label="失败原因" width="220" align="center"></el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="pageConfig.total>0"
      :total="pageConfig.total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改【请填写功能名称】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="检斤单id" prop="weightId">
          <el-input v-model="form.weightId" placeholder="请输入检斤单id"/>
        </el-form-item>
        <el-form-item label="检斤单号" prop="weightNumber">
          <el-input v-model="form.weightNumber" placeholder="请输入检斤单号"/>
        </el-form-item>
        <el-form-item label="业务代码" prop="bizCode">
          <el-input v-model="form.bizCode" placeholder="请输入业务代码"/>
        </el-form-item>
        <el-form-item label="车号" prop="truckNumber">
          <el-input v-model="form.truckNumber" placeholder="请输入车号"/>
        </el-form-item>
        <el-form-item label="订单ID" prop="orderId">
          <el-input v-model="form.orderId" placeholder="请输入订单ID"/>
        </el-form-item>
        <el-form-item label="订单号" prop="orderNumber">
          <el-input v-model="form.orderNumber" placeholder="请输入订单号"/>
        </el-form-item>
        <el-form-item label="订单行号" prop="orderLineNumber">
          <el-input v-model="form.orderLineNumber" placeholder="请输入订单行号"/>
        </el-form-item>
        <el-form-item label="计量状态" prop="meterState">
          <el-input v-model="form.meterState" placeholder="请输入计量状态"/>
        </el-form-item>
        <el-form-item label="检斤通知单编号" prop="billno">
          <el-input v-model="form.billno" placeholder="请输入检斤通知单编号"/>
        </el-form-item>
        <el-form-item label="物料编码" prop="materialNumber">
          <el-input v-model="form.materialNumber" placeholder="请输入物料编码"/>
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="form.materialName" placeholder="请输入物料名称"/>
        </el-form-item>
        <el-form-item label="规格型号" prop="specModel">
          <el-input v-model="form.specModel" placeholder="请输入规格型号"/>
        </el-form-item>
        <el-form-item label="发货库存地编码" prop="sendStockCode">
          <el-input v-model="form.sendStockCode" placeholder="请输入发货库存地编码"/>
        </el-form-item>
        <el-form-item label="发货库
存地名称" prop="sendStockName">
          <el-input v-model="form.sendStockName" placeholder="请输入发货库
存地名称"/>
        </el-form-item>
        <el-form-item label="发货库存地组织编码" prop="sendStockOrgCode">
          <el-input v-model="form.sendStockOrgCode" placeholder="请输入发货库存地组织编码"/>
        </el-form-item>
        <el-form-item label="发货库存地组织名称" prop="sendStockOrgName">
          <el-input v-model="form.sendStockOrgName" placeholder="请输入发货库存地组织名称"/>
        </el-form-item>
        <el-form-item label="收货库存地编码" prop="receiveStockCode">
          <el-input v-model="form.receiveStockCode" placeholder="请输入收货库存地编码"/>
        </el-form-item>
        <el-form-item label="收货库存地名称" prop="receiveStockName">
          <el-input v-model="form.receiveStockName" placeholder="请输入收货库存地名称"/>
        </el-form-item>
        <el-form-item label="皮重(一检)" prop="tareWeight">
          <el-input v-model="form.tareWeight" placeholder="请输入皮重(一检)"/>
        </el-form-item>
        <el-form-item label="皮重时间(一检时间)" prop="tareTime">
          <el-date-picker clearable
                          v-model="form.tareTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择皮重时间(一检时间)">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="皮重人员(一检人员)" prop="tarePersonnel">
          <el-input v-model="form.tarePersonnel" placeholder="请输入皮重人员(一检人员)"/>
        </el-form-item>
        <el-form-item label="毛重(二检)" prop="grossWeight">
          <el-input v-model="form.grossWeight" placeholder="请输入毛重(二检)"/>
        </el-form-item>
        <el-form-item label="毛重时间(二检时间)" prop="grossTime">
          <el-date-picker clearable
                          v-model="form.grossTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择毛重时间(二检时间)">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="毛重人员(二检人员)" prop="grossPersonnel">
          <el-input v-model="form.grossPersonnel" placeholder="请输入毛重人员(二检人员)"/>
        </el-form-item>
        <el-form-item label="净重" prop="netWeight">
          <el-input v-model="form.netWeight" placeholder="请输入净重"/>
        </el-form-item>
        <el-form-item label="净重时间" prop="netTime">
          <el-input v-model="form.netTime" placeholder="请输入净重时间"/>
        </el-form-item>
        <el-form-item label="扣杂重量" prop="deductWeight">
          <el-input v-model="form.deductWeight" placeholder="请输入扣杂重量"/>
        </el-form-item>
        <el-form-item label="扣杂时间" prop="deductTime">
          <el-date-picker clearable
                          v-model="form.deductTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择扣杂时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="扣杂人员" prop="deductPersonnel">
          <el-input v-model="form.deductPersonnel" placeholder="请输入扣杂人员"/>
        </el-form-item>
        <el-form-item label="对方理论重量" prop="oppositeTheoreticalWeight">
          <el-input v-model="form.oppositeTheoreticalWeight" placeholder="请输入对方理论重量"/>
        </el-form-item>
        <el-form-item label="有效净重" prop="netEffectiveWeight">
          <el-input v-model="form.netEffectiveWeight" placeholder="请输入有效净重"/>
        </el-form-item>
        <el-form-item label="计量单位" prop="measurementUnit">
          <el-input v-model="form.measurementUnit" placeholder="请输入计量单位"/>
        </el-form-item>
        <el-form-item label="办卡人" prop="cardHolder">
          <el-input v-model="form.cardHolder" placeholder="请输入办卡人"/>
        </el-form-item>
        <el-form-item label="办卡时间" prop="cardTime">
          <el-date-picker clearable
                          v-model="form.cardTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择办卡时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否取样" prop="sampleOrNot">
          <el-input v-model="form.sampleOrNot" placeholder="请输入是否取样"/>
        </el-form-item>
        <el-form-item label="检斤单类型编码" prop="weightTypeCode">
          <el-input v-model="form.weightTypeCode" placeholder="请输入检斤单类型编码"/>
        </el-form-item>
        <el-form-item label="检斤单类型名称" prop="weightTypeName">
          <el-input v-model="form.weightTypeName" placeholder="请输入检斤单类型名称"/>
        </el-form-item>
        <el-form-item label="产品类别名称" prop="productTypeName">
          <el-input v-model="form.productTypeName" placeholder="请输入产品类别名称"/>
        </el-form-item>
        <el-form-item label="供应商编码" prop="supplierCode">
          <el-input v-model="form.supplierCode" placeholder="请输入供应商编码"/>
        </el-form-item>
        <el-form-item label="供应商名称" prop="supplierName">
          <el-input v-model="form.supplierName" placeholder="请输入供应商名称"/>
        </el-form-item>
        <el-form-item label="客户编码" prop="clientCode">
          <el-input v-model="form.clientCode" placeholder="请输入客户编码"/>
        </el-form-item>
        <el-form-item label="客户名称" prop="clientName">
          <el-input v-model="form.clientName" placeholder="请输入客户名称"/>
        </el-form-item>
        <el-form-item label="业务日期" prop="businessDate">
          <el-date-picker clearable
                          v-model="form.businessDate"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择业务日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="炉次号" prop="heatNo">
          <el-input v-model="form.heatNo" placeholder="请输入炉次号"/>
        </el-form-item>
        <el-form-item label="高炉号" prop="blastFurnaceNo">
          <el-input v-model="form.blastFurnaceNo" placeholder="请输入高炉号"/>
        </el-form-item>
        <el-form-item label="发货单位编码" prop="sendUnitCode">
          <el-input v-model="form.sendUnitCode" placeholder="请输入发货单位编码"/>
        </el-form-item>
        <el-form-item label="发货单位名称" prop="sendUnitName">
          <el-input v-model="form.sendUnitName" placeholder="请输入发货单位名称"/>
        </el-form-item>
        <el-form-item label="发货地点编码" prop="sendLocationCode">
          <el-input v-model="form.sendLocationCode" placeholder="请输入发货地点编码"/>
        </el-form-item>
        <el-form-item label="发货地点名称" prop="sendLocationName">
          <el-input v-model="form.sendLocationName" placeholder="请输入发货地点名称"/>
        </el-form-item>
        <el-form-item label="收货地点编码" prop="receiveLocationCode">
          <el-input v-model="form.receiveLocationCode" placeholder="请输入收货地点编码"/>
        </el-form-item>
        <el-form-item label="收货地点名称" prop="receiveLocationName">
          <el-input v-model="form.receiveLocationName" placeholder="请输入收货地点名称"/>
        </el-form-item>
        <el-form-item label="计量单位编码" prop="meterUnitCode">
          <el-input v-model="form.meterUnitCode" placeholder="请输入计量单位编码"/>
        </el-form-item>
        <el-form-item label="计量单位名称" prop="meterUnitName">
          <el-input v-model="form.meterUnitName" placeholder="请输入计量单位名称"/>
        </el-form-item>
        <el-form-item label="船号" prop="stockLot">
          <el-input v-model="form.stockLot" placeholder="请输入船号"/>
        </el-form-item>
        <el-form-item label="辅助数量" prop="auxiliaryQuantity">
          <el-input v-model="form.auxiliaryQuantity" placeholder="请输入辅助数量"/>
        </el-form-item>
        <el-form-item label="发货通知单分录序列号" prop="entrySerialNumber">
          <el-input v-model="form.entrySerialNumber" placeholder="请输入发货通知单分录序列号"/>
        </el-form-item>
        <el-form-item label="发货通知单分录ID" prop="entrySerialId">
          <el-input v-model="form.entrySerialId" placeholder="请输入发货通知单分录ID"/>
        </el-form-item>
        <el-form-item label="发货单号" prop="sendCode">
          <el-input v-model="form.sendCode" placeholder="请输入发货单号"/>
        </el-form-item>
        <el-form-item label="收货跨垛" prop="receiveAcrossStack">
          <el-input v-model="form.receiveAcrossStack" placeholder="请输入收货跨垛"/>
        </el-form-item>
        <el-form-item label="收货跨" prop="receiveAcross">
          <el-input v-model="form.receiveAcross" placeholder="请输入收货跨"/>
        </el-form-item>
        <el-form-item label="收货垛" prop="receiveStack">
          <el-input v-model="form.receiveStack" placeholder="请输入收货垛"/>
        </el-form-item>
        <el-form-item label="收货地点" prop="receiveLocation">
          <el-input v-model="form.receiveLocation" placeholder="请输入收货地点"/>
        </el-form-item>
        <el-form-item label="发货跨垛" prop="sendAcrossStack">
          <el-input v-model="form.sendAcrossStack" placeholder="请输入发货跨垛"/>
        </el-form-item>
        <el-form-item label="发货跨" prop="sendAcross">
          <el-input v-model="form.sendAcross" placeholder="请输入发货跨"/>
        </el-form-item>
        <el-form-item label="发货垛" prop="sendStack">
          <el-input v-model="form.sendStack" placeholder="请输入发货垛"/>
        </el-form-item>
        <el-form-item label="发货地点" prop="secdLocation">
          <el-input v-model="form.secdLocation" placeholder="请输入发货地点"/>
        </el-form-item>
        <el-form-item label="存放物流接口返回的json串" prop="jsonchar">
          <el-input v-model="form.jsonchar" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
        <el-form-item label="接收检斤数据标识" prop="receptionDataFlag">
          <el-input v-model="form.receptionDataFlag" placeholder="请输入接收检斤数据标识"/>
        </el-form-item>
        <el-form-item label="操作人" prop="operatorBy">
          <el-input v-model="form.operatorBy" placeholder="请输入操作人"/>
        </el-form-item>
        <el-form-item label="失败原因" prop="failReason">
          <el-input v-model="form.failReason" placeholder="请输入失败原因"/>
        </el-form-item>
        <el-form-item label="收货人" prop="receivePersonnel">
          <el-input v-model="form.receivePersonnel" placeholder="请输入收货人"/>
        </el-form-item>
        <el-form-item label="收货时间" prop="receiveTime">
          <el-date-picker clearable
                          v-model="form.receiveTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择收货时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    listLog,
    getLog,
    delLog,
    addLog,
    updateLog
  } from "@/api/meterweightlog/index";

  export default {
    name: "Log",
    data() {
      return {
        dltableHeight: 700,
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 【请填写功能名称】表格数据
        logList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 20,
          weightId: null,
          weightNumber: null,
          bizCode: null,
          truckNumber: null,
          orderId: null,
          orderNumber: null,
          orderLineNumber: null,
          meterState: null,
          documentStatus: null,
          billno: null,
          materialNumber: null,
          materialName: null,
          specModel: null,
          sendStockCode: null,
          sendStockName: null,
          sendStockOrgCode: null,
          sendStockOrgName: null,
          receiveStockCode: null,
          receiveStockName: null,
          tareWeight: null,
          tareTime: null,
          tarePersonnel: null,
          grossWeight: null,
          grossTime: null,
          grossPersonnel: null,
          netWeight: null,
          netTime: null,
          deductWeight: null,
          deductTime: null,
          deductPersonnel: null,
          oppositeTheoreticalWeight: null,
          netEffectiveWeight: null,
          measurementUnit: null,
          cardHolder: null,
          cardTime: null,
          sampleOrNot: null,
          weightTypeCode: null,
          weightTypeName: null,
          productType: null,
          productTypeName: null,
          supplierCode: null,
          supplierName: null,
          clientCode: null,
          clientName: null,
          businessDate: null,
          heatNo: null,
          blastFurnaceNo: null,
          sendUnitCode: null,
          sendUnitName: null,
          sendLocationCode: null,
          sendLocationName: null,
          receiveLocationCode: null,
          receiveLocationName: null,
          operationType: null,
          meterUnitCode: null,
          meterUnitName: null,
          stockLot: null,
          auxiliaryQuantity: null,
          entrySerialNumber: null,
          entrySerialId: null,
          sendCode: null,
          transportType: null,
          receiveAcrossStack: null,
          receiveAcross: null,
          receiveStack: null,
          receiveLocation: null,
          sendAcrossStack: null,
          sendAcross: null,
          sendStack: null,
          secdLocation: null,
          jsonchar: null,
          createTime: null,
          receptionDataFlag: null,
          updateTime: null,
          operatorBy: null,
          failReason: null,
          receivePersonnel: null,
          receiveTime: null
        },
        // 下拉框
        receptionDataFlagOption: [{
          value: '成功',
          label: '成功'
        }, {
          value: '缺数据',
          label: '缺数据'
        }, {
          value: '作废',
          label: '作废'
        }],
        // 表单参数
        form: {},
        rules: {
          weightNumber: [
            {
              required: true, message: "检斤单号不能为空", trigger: "blur" }
          ],
        },
        // 表单校验
        basicConfig: {
          index: true, // 是否启用序号列
          needPage: true, // 是否展示分页
          indexName: null, // 序号列名(默认为：序号)
          selectionType: true, // 是否启用多选框
          indexWidth: null, // 序号列宽(默认为：50)
          indexFixed: null, // 序号列定位(默认为：left)
          settingType: true, // 是否展示表格配置按钮
          headerSortSaveType: false // 表头排序是否保存在localStorage中
        },
        pageConfig: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页显示条目个数
          total: 0, // 总数
          background: true, // 是否展示分页器背景色
          pageSizes: [10, 20, 50, 100]// 分页器分页待选项
        },
        tableData: [],
        selectVO: '',
      };
    },
    created() {
      this.getList(null);
    },
    methods: {
      /** pageNum事件 */
      numChange(pageNum, selectVO) {
        this.pageConfig.pageNum = pageNum;
        this.queryParams.pageNum = pageNum;
        this.selectVO = selectVO;
        this.getList(selectVO);
      },
      /** pageSize事件 */
      sizeChange(pageSize, selectVO) {
        this.pageConfig.pageSize = pageSize;
        this.queryParams.pageSize = pageSize;
        this.selectVO = selectVO;
        this.getList(selectVO);
      },
      /** 查询【请填写功能名称】列表 */
      getList(selectVO) {
        this.loading = true;
        if (selectVO) {
          this.selectVO = selectVO;
        }
        this.queryList();
      },
      queryList() {
        console.log("this.queryParams:",JSON.stringify(this.queryParams))
        listLog(this.queryParams, this.selectVO).then(response => {
          this.tableData = response.rows
          this.pageConfig.total = response.total;
          this.total = response.total
          this.loading = false;
        });
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          fid: null,
          weightId: null,
          weightNumber: null,
          bizCode: null,
          truckNumber: null,
          orderId: null,
          orderNumber: null,
          orderLineNumber: null,
          meterState: null,
          documentStatus: "0",
          billno: null,
          materialNumber: null,
          materialName: null,
          specModel: null,
          sendStockCode: null,
          sendStockName: null,
          sendStockOrgCode: null,
          sendStockOrgName: null,
          receiveStockCode: null,
          receiveStockName: null,
          tareWeight: null,
          tareTime: null,
          tarePersonnel: null,
          grossWeight: null,
          grossTime: null,
          grossPersonnel: null,
          netWeight: null,
          netTime: null,
          deductWeight: null,
          deductTime: null,
          deductPersonnel: null,
          oppositeTheoreticalWeight: null,
          netEffectiveWeight: null,
          measurementUnit: null,
          cardHolder: null,
          cardTime: null,
          sampleOrNot: null,
          weightTypeCode: null,
          weightTypeName: null,
          productType: null,
          productTypeName: null,
          supplierCode: null,
          supplierName: null,
          clientCode: null,
          clientName: null,
          businessDate: null,
          heatNo: null,
          blastFurnaceNo: null,
          sendUnitCode: null,
          sendUnitName: null,
          sendLocationCode: null,
          sendLocationName: null,
          receiveLocationCode: null,
          receiveLocationName: null,
          operationType: null,
          meterUnitCode: null,
          meterUnitName: null,
          stockLot: null,
          auxiliaryQuantity: null,
          entrySerialNumber: null,
          entrySerialId: null,
          sendCode: null,
          transportType: null,
          receiveAcrossStack: null,
          receiveAcross: null,
          receiveStack: null,
          receiveLocation: null,
          sendAcrossStack: null,
          sendAcross: null,
          sendStack: null,
          secdLocation: null,
          jsonchar: null,
          createTime: null,
          receptionDataFlag: null,
          updateTime: null,
          operatorBy: null,
          failReason: null,
          receivePersonnel: null,
          receiveTime: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.fid)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加【请填写功能名称】";
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        const fid = row.fid || this.ids
        getLog(fid).then(response => {
          this.form = response.data;
          this.open = true;
          this.title = "修改【请填写功能名称】";
        });
      },
      /** 提交按钮 */
      submitForm() {

      },
      /** 删除按钮操作 */
      handleDelete(row) {

      },
      /** 导出按钮操作 */
      handleExport()
      {
        this.download('api/lingxiao/log/export', {
          ...this.queryParams
        }, `log_${new Date().getTime()}.xlsx`)
      }
    }
  }
  ;
</script>
