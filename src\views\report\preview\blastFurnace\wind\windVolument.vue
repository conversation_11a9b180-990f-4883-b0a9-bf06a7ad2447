<template>
  <div class="app-container" style="padding: 0px;">
    <div class="block" style="margin-top:8px;">
      <span >开始时间:</span>
      <el-date-picker style="margin-left: 10px;width: 392px;"
                      v-model="startTimeArr" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
      </el-date-picker>
      <el-button type="primary" style="margin-left: 100px;" @click="handleQuery" size="mini">搜索</el-button>
    </div>
    <div class="buttonOpe">
      <el-button type="primary"  @click="handleAdd" size="mini">新增</el-button>
      <el-button type="danger"  @click="handleDelete" size="mini">删除</el-button>
      <el-button type="primary"  @click="handleUpdate" size="mini">保存</el-button>
      <el-button type="primary"  @click="daochu" size="mini">导出</el-button>
    </div>
    <div style="margin-top: 7px;">
      <vxe-table
        border
        :loading="loading"
        ref="tableRef"
        :edit-config="{trigger: 'click', mode: 'row'}"
        @edit-closed="editClosedEvent"
        :checkbox-config="{checkField: 'isChecked', indeterminateField: 'isIndeterminate'}"
        @checkbox-change="handleCheckboxChange"
        @cell-click="handleCellClickTime"
        :data="tableData">
        <vxe-column type="checkbox" width="60"></vxe-column>
        <vxe-column field="startTime" title="开始时间"  width='auto' :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="endTime" title="结束时间"  width='auto' :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="beforeAdjustment" title="调剂前"  width='auto' :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="afterAdjustment" title="调剂后"  width='auto'  :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="windReduction" title="减风"  width='auto' :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="minWindPressure" title="最低风压"  width='auto' :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="reason" title="原因"  width='auto' :padding="false" vertical-align="top" :edit-render="{name: 'textarea'}" row-resize></vxe-column>
        <vxe-column field="inputer" title="录入人"  width='auto' :edit-render="{name: 'input'}"></vxe-column>
        <vxe-column field="inputTime" title="录入时间" width='auto' :edit-render="{name: 'input'}"></vxe-column>
      </vxe-table>
      <vxe-pager
        :current-page.sync="mainTableConfig.pageConfig.pageNum"
        :page-size.sync="mainTableConfig.pageConfig.pageSize"
        :total="mainTableConfig.pageConfig.total"
        @page-change="pageChange">
      </vxe-pager>
    </div>
  </div>
</template>

<script>
  import {
    windAdd,
    listWindSelect,
    windUpdateData,
    windDeleteData,
  } from "@/api/report/preview/blastFurnace/windVolument";
    import dayjs from "dayjs";

    export default {
        name: "windVolument",
      data(){
          return{
            // 加载
            loading:true,
            startTimeArr:[],
            tableData: [],
            mainTableConfig: {
              tableData: [],
              selectVO: '',
              pageConfig: {
                pageNum: 1, // 页码
                pageSize: 10, // 每页显示条目个数
                total: 0, // 总数
                background: true, // 是否展示分页器背景色
                pageSizes: [10, 20, 50, 100]// 分页器分页待选项
              }
            },
            selectParam:{
              startTime:'',
              endTime:'',
              prodCenterCode:'',
            },
            updateDataObj:{},
            windDataArr:[],
          }
      },
      created(){
        this.startTimeArr.push(dayjs(new Date()).add(-1, "day"));
        this.startTimeArr.push(dayjs(new Date()).add(1, "day"));
        // 展示页面数据
        this.queryLists()
      },
      methods:{
        /*  获取高炉号*/
        getBlastFurnaceNumber() {
          return this.$route.query.prodCenterCode;
        },
        /*搜索按钮*/
        handleQuery(){
          if(this.startTimeArr != null){
            if (this.startTimeArr.length == 2) {
              this.selectParam.startTime = dayjs(this.startTimeArr[0]).format(
                "YYYY-MM-DD HH:mm:ss"
              );
              this.selectParam.endTime = dayjs(this.startTimeArr[1]).format(
                "YYYY-MM-DD HH:mm:ss"
              );
            }
          }
          this.queryLists();
        },
        queryLists(){
          this.selectParam.prodCenterCode = this.getBlastFurnaceNumber();
          listWindSelect(this.selectParam).then(response=>{
            console.log("response:",JSON.stringify(response))
            this.tableData = response.rows
            this.loading = false;
            this.mainTableConfig.pageConfig.total = response.total
          });
        },
        /*关闭编辑框触发 失去焦点 进行保存数据*/
        editClosedEvent(row, column) {
          // ironCanWeightInputEdit(row.row).then(response=>{
          //   this.$modal.msgSuccess("编辑成功");
          //   this.queryLists()
          // });
        },

        /*多选框触发事件*/
        handleCheckboxChange({ records, rowIndex, row,checked }) {
          console.log("checked:",JSON.stringify(checked))
          console.log("row:",JSON.stringify(row))
          if(checked  == true){
            let obj = {fid:row.fid}
            this.windDataArr.push(obj)
          }else{
            const $table = this.$refs.tableRef
            if ($table) {
              $table.clearCheckboxRow()
            }
            this.windDataArr = []
          }
        },
        handleCellClickTime({ row, column }) {

          if (column.property === 'startTime') { // 判断是否点击的是时间列
            if(row.startTime == null){
              row.startTime = this.getCurrentTime(); // 获取当前时间并更新到行数据中
            }
          }
          if (column.property === 'endTime') { // 判断是否点击的是时间列
            if(row.endTime == null){
              row.endTime = this.getCurrentTime(); // 获取当前时间并更新到行数据中
            }
          }
          if (column.property === 'inputTime') { // 判断是否点击的是时间列
            if(row.inputTime == null){
              row.inputTime = this.getCurrentTime(); // 获取当前时间并更新到行数据中
            }
          }
          this.updateDataObj = row
        },
        getCurrentTime() {
          return  dayjs(new Date()).format(
            "YYYY-MM-DD HH:mm:ss"
          );
        },
        pageChange ({ pageSize, currentPage }) {
          this.mainTableConfig.pageConfig.pageNum = currentPage
          this.mainTableConfig.pageConfig.pageSize = pageSize
          this.selectParam.pageNum=this.mainTableConfig.pageConfig.pageNum
          this.selectParam.pageSize=this.mainTableConfig.pageConfig.pageSize
          this.queryLists()
        },
        /* 新增按钮 */
        handleAdd(){
          let obj ={prodCenterCode:this.getBlastFurnaceNumber()}
          windAdd(obj).then(response=>{
            if(response.code == 200){
              this.$modal.msgSuccess("新增成功");
              this.queryLists()
            }
          });

        },
        /* 删除按钮 */
        handleDelete(){
          console.log("windDataObj:",JSON.stringify(this.windDataArr))
          if(this.windDataArr.length == 0 || this.windDataArr == null){
            this.$message({
              message: '请选择要删除的数据',
              type:'warning'
            })
            return
          }
          windDeleteData(this.windDataArr).then(response=>{
            if(response.code == 200){
              this.$modal.msgSuccess("删除成功");
              this.queryLists()
            }
          });

        },
        /* 修改保存按钮 */
        handleUpdate(){
          windUpdateData(this.updateDataObj).then(response=>{
            if(response.code == 200){
              this.$modal.msgSuccess("保存成功");
              this.queryLists()
            }
          });

        },
        /* 导出按钮 */
        daochu(){
          this.download('/api/windVolume/export', {
            ...this.selectParam
          }, `volume_${new Date().getTime()}.xlsx`)
        },
      },

    }
</script>

<style scoped>
  .buttonOpe{
    margin-top: 10px;
    margin-left: 5px;
  }

</style>
